// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'live.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_LiveRoomParticipation _$LiveRoomParticipationFromJson(Map json) =>
    _LiveRoomParticipation(
      rtc: json['rtcToken'] as String,
      rtm: json['rtmToken'] as String,
      room: json['channel'] == null
          ? null
          : LiveRoom.fromJson(
              Map<String, dynamic>.from(json['channel'] as Map),
            ),
    );

Map<String, dynamic> _$LiveRoomParticipationToJson(
  _LiveRoomParticipation instance,
) => <String, dynamic>{
  'rtcToken': instance.rtc,
  'rtmToken': instance.rtm,
  'channel': instance.room?.toJson(),
};

_LiveRoom _$LiveRoomFromJson(Map json) => _LiveRoom(
  private: json['isPrivate'] as bool? ?? false,
  id: json['name'] as String,
  displayName: json['displayName'] as String,
  tokenMintAddress: json['tokenAddress'] as String?,
  tokenChain: json['tokenChain'] as String?,
  tokenLogo: json['tokenLogo'] as String?,
  tokenName: json['tokenName'] as String?,
  tokenSymbol: json['tokenSymbol'] as String?,
  rawGroupId: json['groupId'] as String?,
  group: json['group'] == null
      ? null
      : LiveRoomGroup.fromJson(Map<String, dynamic>.from(json['group'] as Map)),
  username: json['username'] as String,
  userDisplayName: json['userDisplayName'] as String? ?? '',
  userAvatar: json['userAvatar'] as String? ?? '',
  userCount: (json['userCount'] as num?)?.toInt() ?? 0,
  userId: json['userUniqId'] == null
      ? 0
      : const JsonUserIdConverter().fromJson(json['userUniqId'] as String),
  broadcasters:
      (json['broadcasters'] as List<dynamic>?)
          ?.map((e) => UserInfo.fromJson(Map<String, dynamic>.from(e as Map)))
          .toList() ??
      const [],
  broadcasterIds: json['broadcasterIds'] == null
      ? const []
      : const JsonUserIdsConverter().fromJson(json['broadcasterIds'] as List),
  openTime: const EpochDateTimeConverter().fromJson(
    (json['openTime'] as num).toInt(),
  ),
  closeTime: const EpochDateTimeNullableConverter().fromJson(
    (json['closeTime'] as num?)?.toInt(),
  ),
  chatEnabled: json['chatEnabled'] as bool? ?? true,
);

Map<String, dynamic> _$LiveRoomToJson(_LiveRoom instance) => <String, dynamic>{
  'isPrivate': instance.private,
  'name': instance.id,
  'displayName': instance.displayName,
  'tokenAddress': instance.tokenMintAddress,
  'tokenChain': instance.tokenChain,
  'tokenLogo': instance.tokenLogo,
  'tokenName': instance.tokenName,
  'tokenSymbol': instance.tokenSymbol,
  'groupId': instance.rawGroupId,
  'group': instance.group?.toJson(),
  'username': instance.username,
  'userDisplayName': instance.userDisplayName,
  'userAvatar': instance.userAvatar,
  'userCount': instance.userCount,
  'userUniqId': const JsonUserIdConverter().toJson(instance.userId),
  'broadcasters': instance.broadcasters.map((e) => e.toJson()).toList(),
  'broadcasterIds': const JsonUserIdsConverter().toJson(
    instance.broadcasterIds,
  ),
  'openTime': const EpochDateTimeConverter().toJson(instance.openTime),
  'closeTime': const EpochDateTimeNullableConverter().toJson(
    instance.closeTime,
  ),
  'chatEnabled': instance.chatEnabled,
};

_LiveRoomGroup _$LiveRoomGroupFromJson(Map json) => _LiveRoomGroup(
  id: json['uniqId'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  logo: json['logo'] as String? ?? '',
  opening: json['isOpen'] as bool? ?? true,
  isJoined: json['isJoined'] as bool? ?? true,
  hasApplied: json['hasApplied'] as bool? ?? false,
  needConfirm: json['needConfirm'] as bool? ?? false,
);

Map<String, dynamic> _$LiveRoomGroupToJson(_LiveRoomGroup instance) =>
    <String, dynamic>{
      'uniqId': instance.id,
      'name': instance.name,
      'description': instance.description,
      'logo': instance.logo,
      'isOpen': instance.opening,
      'isJoined': instance.isJoined,
      'hasApplied': instance.hasApplied,
      'needConfirm': instance.needConfirm,
    };

_LiveRoomUsers _$LiveRoomUsersFromJson(Map json) => _LiveRoomUsers(
  users: (json['users'] as List<dynamic>)
      .map((e) => LiveRoomUser.fromJson(Map<String, dynamic>.from(e as Map)))
      .toList(),
);

Map<String, dynamic> _$LiveRoomUsersToJson(_LiveRoomUsers instance) =>
    <String, dynamic>{'users': instance.users.map((e) => e.toJson()).toList()};

_LiveRoomUser _$LiveRoomUserFromJson(Map json) => _LiveRoomUser(
  userId: const JsonUserIdConverter().fromJson(json['uniqId'] as String),
  username: json['username'] as String,
  displayName: json['displayName'] as String? ?? '',
  avatar: json['avatar'] as String? ?? '',
  role: const LiveRoomUserRoleConverter().fromJson(
    (json['role'] as num?)?.toInt(),
  ),
  isPaused: json['isPaused'] as bool? ?? false,
);

Map<String, dynamic> _$LiveRoomUserToJson(_LiveRoomUser instance) =>
    <String, dynamic>{
      'uniqId': const JsonUserIdConverter().toJson(instance.userId),
      'username': instance.username,
      'displayName': instance.displayName,
      'avatar': instance.avatar,
      'role': const LiveRoomUserRoleConverter().toJson(instance.role),
      'isPaused': instance.isPaused,
    };

_LiveRoomToken _$LiveRoomTokenFromJson(Map json) => _LiveRoomToken(
  pinned: json['isPinned'] as bool? ?? false,
  address: json['address'] as String,
  chain: json['chain'] as String,
  icon: json['logoUri'] as String? ?? '',
  name: json['name'] as String?,
  symbol: json['symbol'] as String?,
);

Map<String, dynamic> _$LiveRoomTokenToJson(_LiveRoomToken instance) =>
    <String, dynamic>{
      'isPinned': instance.pinned,
      'address': instance.address,
      'chain': instance.chain,
      'logoUri': instance.icon,
      'name': instance.name,
      'symbol': instance.symbol,
    };

_LiveRoomSummary _$LiveRoomSummaryFromJson(Map json) => _LiveRoomSummary(
  private: json['isPrivate'] as bool? ?? false,
  id: json['uniqId'] as String,
  name: json['name'] as String? ?? '',
  group: json['group'] == null
      ? null
      : LiveRoomGroup.fromJson(Map<String, dynamic>.from(json['group'] as Map)),
  viewerCount: (json['totalViewer'] as num?)?.toInt() ?? 1,
  caMentionedCount: (json['mentionCa'] as num?)?.toInt() ?? 1,
  duration: (json['duration'] as num?)?.toInt(),
  openTime: const EpochDateTimeConverter().fromJson(
    (json['openTime'] as num).toInt(),
  ),
  closeTime: _$JsonConverterFromJson<int, DateTime>(
    json['closeTime'],
    const EpochDateTimeConverter().fromJson,
  ),
  userAvatars:
      (json['userAvatars'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  tokenLogos:
      (json['tokenLogos'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
);

Map<String, dynamic> _$LiveRoomSummaryToJson(_LiveRoomSummary instance) =>
    <String, dynamic>{
      'isPrivate': instance.private,
      'uniqId': instance.id,
      'name': instance.name,
      'group': instance.group?.toJson(),
      'totalViewer': instance.viewerCount,
      'mentionCa': instance.caMentionedCount,
      'duration': instance.duration,
      'openTime': const EpochDateTimeConverter().toJson(instance.openTime),
      'closeTime': _$JsonConverterToJson<int, DateTime>(
        instance.closeTime,
        const EpochDateTimeConverter().toJson,
      ),
      'userAvatars': instance.userAvatars,
      'tokenLogos': instance.tokenLogos,
    };

Value? _$JsonConverterFromJson<Json, Value>(
  Object? json,
  Value? Function(Json json) fromJson,
) => json == null ? null : fromJson(json as Json);

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) => value == null ? null : toJson(value);

_LiveRoomLink _$LiveRoomLinkFromJson(Map json) => _LiveRoomLink(
  pinned: json['isPinned'] as bool? ?? false,
  id: json['uniqId'] as String,
  href: json['href'] as String,
  siteName: json['siteName'] as String,
  title: json['title'] as String,
  description: json['description'] as String? ?? '',
  icon: json['favicon'] as String? ?? '',
);

Map<String, dynamic> _$LiveRoomLinkToJson(_LiveRoomLink instance) =>
    <String, dynamic>{
      'isPinned': instance.pinned,
      'uniqId': instance.id,
      'href': instance.href,
      'siteName': instance.siteName,
      'title': instance.title,
      'description': instance.description,
      'favicon': instance.icon,
    };

_LiveRPCRequest _$LiveRPCRequestFromJson(Map json) => _LiveRPCRequest(
  version: json['v'] as String,
  timestamp: const EpochDateTimeConverter().fromJson(
    (json['t'] as num).toInt(),
  ),
  nonce: json['n'] as String,
  method: const LiveMethodConverter().fromJson(json['m'] as String?),
  params: Map<String, Object?>.from(json['p'] as Map),
);

Map<String, dynamic> _$LiveRPCRequestToJson(_LiveRPCRequest instance) =>
    <String, dynamic>{
      'v': instance.version,
      't': const EpochDateTimeConverter().toJson(instance.timestamp),
      'n': instance.nonce,
      'm': const LiveMethodConverter().toJson(instance.method),
      'p': instance.params,
    };

LiveControlCloseRoom _$LiveControlCloseRoomFromJson(Map json) =>
    LiveControlCloseRoom(
      createAt: const EpochDateTimeConverter().fromJson(
        (json['createAt'] as num).toInt(),
      ),
      username: json['username'] as String,
      userId: const JsonUserIdConverter().fromJson(json['userId'] as String),
      userAvatar: json['userAvatar'] as String? ?? '',
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$LiveControlCloseRoomToJson(
  LiveControlCloseRoom instance,
) => <String, dynamic>{
  'createAt': const EpochDateTimeConverter().toJson(instance.createAt),
  'username': instance.username,
  'userId': const JsonUserIdConverter().toJson(instance.userId),
  'userAvatar': instance.userAvatar,
  'type': instance.$type,
};

LiveControlKickUser _$LiveControlKickUserFromJson(Map json) =>
    LiveControlKickUser(
      createAt: const EpochDateTimeConverter().fromJson(
        (json['createAt'] as num).toInt(),
      ),
      username: json['username'] as String,
      userId: const JsonUserIdConverter().fromJson(json['userId'] as String),
      userAvatar: json['userAvatar'] as String? ?? '',
      uuid: json['uuid'] as String,
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$LiveControlKickUserToJson(
  LiveControlKickUser instance,
) => <String, dynamic>{
  'createAt': const EpochDateTimeConverter().toJson(instance.createAt),
  'username': instance.username,
  'userId': const JsonUserIdConverter().toJson(instance.userId),
  'userAvatar': instance.userAvatar,
  'uuid': instance.uuid,
  'type': instance.$type,
};

LiveControlPinnedCA _$LiveControlPinnedCAFromJson(Map json) =>
    LiveControlPinnedCA(
      createAt: const EpochDateTimeConverter().fromJson(
        (json['createAt'] as num).toInt(),
      ),
      username: json['username'] as String,
      userId: const JsonUserIdConverter().fromJson(json['userId'] as String),
      userAvatar: json['userAvatar'] as String? ?? '',
      address: json['address'] as String,
      chain: json['chain'] as String,
      icon: json['icon'] as String? ?? '',
      name: json['name'] as String?,
      symbol: json['symbol'] as String?,
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$LiveControlPinnedCAToJson(
  LiveControlPinnedCA instance,
) => <String, dynamic>{
  'createAt': const EpochDateTimeConverter().toJson(instance.createAt),
  'username': instance.username,
  'userId': const JsonUserIdConverter().toJson(instance.userId),
  'userAvatar': instance.userAvatar,
  'address': instance.address,
  'chain': instance.chain,
  'icon': instance.icon,
  'name': instance.name,
  'symbol': instance.symbol,
  'type': instance.$type,
};

LiveControlUnpinnedCA _$LiveControlUnpinnedCAFromJson(Map json) =>
    LiveControlUnpinnedCA(
      createAt: const EpochDateTimeConverter().fromJson(
        (json['createAt'] as num).toInt(),
      ),
      username: json['username'] as String,
      userId: const JsonUserIdConverter().fromJson(json['userId'] as String),
      userAvatar: json['userAvatar'] as String? ?? '',
      address: json['address'] as String,
      chain: json['chain'] as String,
      icon: json['icon'] as String? ?? '',
      name: json['name'] as String?,
      symbol: json['symbol'] as String?,
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$LiveControlUnpinnedCAToJson(
  LiveControlUnpinnedCA instance,
) => <String, dynamic>{
  'createAt': const EpochDateTimeConverter().toJson(instance.createAt),
  'username': instance.username,
  'userId': const JsonUserIdConverter().toJson(instance.userId),
  'userAvatar': instance.userAvatar,
  'address': instance.address,
  'chain': instance.chain,
  'icon': instance.icon,
  'name': instance.name,
  'symbol': instance.symbol,
  'type': instance.$type,
};

LiveControlPushedCA _$LiveControlPushedCAFromJson(Map json) =>
    LiveControlPushedCA(
      createAt: const EpochDateTimeConverter().fromJson(
        (json['createAt'] as num).toInt(),
      ),
      username: json['username'] as String,
      userId: const JsonUserIdConverter().fromJson(json['userId'] as String),
      userAvatar: json['userAvatar'] as String? ?? '',
      address: json['address'] as String,
      chain: json['chain'] as String,
      icon: json['icon'] as String? ?? '',
      name: json['name'] as String?,
      symbol: json['symbol'] as String?,
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$LiveControlPushedCAToJson(
  LiveControlPushedCA instance,
) => <String, dynamic>{
  'createAt': const EpochDateTimeConverter().toJson(instance.createAt),
  'username': instance.username,
  'userId': const JsonUserIdConverter().toJson(instance.userId),
  'userAvatar': instance.userAvatar,
  'address': instance.address,
  'chain': instance.chain,
  'icon': instance.icon,
  'name': instance.name,
  'symbol': instance.symbol,
  'type': instance.$type,
};

LiveControlPinnedLink _$LiveControlPinnedLinkFromJson(Map json) =>
    LiveControlPinnedLink(
      createAt: const EpochDateTimeConverter().fromJson(
        (json['createAt'] as num).toInt(),
      ),
      username: json['username'] as String,
      userId: const JsonUserIdConverter().fromJson(json['userId'] as String),
      userAvatar: json['userAvatar'] as String? ?? '',
      url: json['url'] as String,
      icon: json['icon'] as String? ?? '',
      title: json['title'] as String? ?? '',
      description: json['description'] as String? ?? '',
      siteName: json['siteName'] as String? ?? '',
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$LiveControlPinnedLinkToJson(
  LiveControlPinnedLink instance,
) => <String, dynamic>{
  'createAt': const EpochDateTimeConverter().toJson(instance.createAt),
  'username': instance.username,
  'userId': const JsonUserIdConverter().toJson(instance.userId),
  'userAvatar': instance.userAvatar,
  'url': instance.url,
  'icon': instance.icon,
  'title': instance.title,
  'description': instance.description,
  'siteName': instance.siteName,
  'type': instance.$type,
};

LiveControlUnpinnedLink _$LiveControlUnpinnedLinkFromJson(Map json) =>
    LiveControlUnpinnedLink(
      createAt: const EpochDateTimeConverter().fromJson(
        (json['createAt'] as num).toInt(),
      ),
      username: json['username'] as String,
      userId: const JsonUserIdConverter().fromJson(json['userId'] as String),
      userAvatar: json['userAvatar'] as String? ?? '',
      url: json['url'] as String,
      icon: json['icon'] as String? ?? '',
      title: json['title'] as String? ?? '',
      description: json['description'] as String? ?? '',
      siteName: json['siteName'] as String? ?? '',
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$LiveControlUnpinnedLinkToJson(
  LiveControlUnpinnedLink instance,
) => <String, dynamic>{
  'createAt': const EpochDateTimeConverter().toJson(instance.createAt),
  'username': instance.username,
  'userId': const JsonUserIdConverter().toJson(instance.userId),
  'userAvatar': instance.userAvatar,
  'url': instance.url,
  'icon': instance.icon,
  'title': instance.title,
  'description': instance.description,
  'siteName': instance.siteName,
  'type': instance.$type,
};

LiveControlPushedLink _$LiveControlPushedLinkFromJson(Map json) =>
    LiveControlPushedLink(
      createAt: const EpochDateTimeConverter().fromJson(
        (json['createAt'] as num).toInt(),
      ),
      username: json['username'] as String,
      userId: const JsonUserIdConverter().fromJson(json['userId'] as String),
      userAvatar: json['userAvatar'] as String? ?? '',
      url: json['url'] as String,
      icon: json['icon'] as String? ?? '',
      title: json['title'] as String? ?? '',
      description: json['description'] as String? ?? '',
      siteName: json['siteName'] as String? ?? '',
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$LiveControlPushedLinkToJson(
  LiveControlPushedLink instance,
) => <String, dynamic>{
  'createAt': const EpochDateTimeConverter().toJson(instance.createAt),
  'username': instance.username,
  'userId': const JsonUserIdConverter().toJson(instance.userId),
  'userAvatar': instance.userAvatar,
  'url': instance.url,
  'icon': instance.icon,
  'title': instance.title,
  'description': instance.description,
  'siteName': instance.siteName,
  'type': instance.$type,
};

LiveControlRequestMicrophone _$LiveControlRequestMicrophoneFromJson(Map json) =>
    LiveControlRequestMicrophone(
      createAt: const EpochDateTimeConverter().fromJson(
        (json['createAt'] as num).toInt(),
      ),
      username: json['username'] as String,
      userId: const JsonUserIdConverter().fromJson(json['userId'] as String),
      userAvatar: json['userAvatar'] as String? ?? '',
      userInfo: UserInfo.fromJson(
        Map<String, dynamic>.from(json['userInfo'] as Map),
      ),
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$LiveControlRequestMicrophoneToJson(
  LiveControlRequestMicrophone instance,
) => <String, dynamic>{
  'createAt': const EpochDateTimeConverter().toJson(instance.createAt),
  'username': instance.username,
  'userId': const JsonUserIdConverter().toJson(instance.userId),
  'userAvatar': instance.userAvatar,
  'userInfo': instance.userInfo.toJson(),
  'type': instance.$type,
};

LiveControlUpdateMicrophone _$LiveControlUpdateMicrophoneFromJson(Map json) =>
    LiveControlUpdateMicrophone(
      createAt: const EpochDateTimeConverter().fromJson(
        (json['createAt'] as num).toInt(),
      ),
      username: json['username'] as String,
      userId: const JsonUserIdConverter().fromJson(json['userId'] as String),
      userAvatar: json['userAvatar'] as String? ?? '',
      userInfo: UserInfo.fromJson(
        Map<String, dynamic>.from(json['userInfo'] as Map),
      ),
      enabled: json['enabled'] as bool,
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$LiveControlUpdateMicrophoneToJson(
  LiveControlUpdateMicrophone instance,
) => <String, dynamic>{
  'createAt': const EpochDateTimeConverter().toJson(instance.createAt),
  'username': instance.username,
  'userId': const JsonUserIdConverter().toJson(instance.userId),
  'userAvatar': instance.userAvatar,
  'userInfo': instance.userInfo.toJson(),
  'enabled': instance.enabled,
  'type': instance.$type,
};

LiveControlUpdateChatAbility _$LiveControlUpdateChatAbilityFromJson(Map json) =>
    LiveControlUpdateChatAbility(
      createAt: const EpochDateTimeConverter().fromJson(
        (json['createAt'] as num).toInt(),
      ),
      username: json['username'] as String,
      userId: const JsonUserIdConverter().fromJson(json['userId'] as String),
      userAvatar: json['userAvatar'] as String? ?? '',
      enabled: json['enabled'] as bool,
      $type: json['type'] as String?,
    );

Map<String, dynamic> _$LiveControlUpdateChatAbilityToJson(
  LiveControlUpdateChatAbility instance,
) => <String, dynamic>{
  'createAt': const EpochDateTimeConverter().toJson(instance.createAt),
  'username': instance.username,
  'userId': const JsonUserIdConverter().toJson(instance.userId),
  'userAvatar': instance.userAvatar,
  'enabled': instance.enabled,
  'type': instance.$type,
};

LiveMessagePlain _$LiveMessagePlainFromJson(Map json) => LiveMessagePlain(
  createAt: const EpochDateTimeConverter().fromJson(
    (json['createAt'] as num).toInt(),
  ),
  username: json['username'] as String,
  userId: const JsonUserIdConverter().fromJson(json['userId'] as String),
  userAvatar: json['userAvatar'] as String? ?? '',
  text: json['text'] as String,
  $type: json['type'] as String?,
);

Map<String, dynamic> _$LiveMessagePlainToJson(LiveMessagePlain instance) =>
    <String, dynamic>{
      'createAt': const EpochDateTimeConverter().toJson(instance.createAt),
      'username': instance.username,
      'userId': const JsonUserIdConverter().toJson(instance.userId),
      'userAvatar': instance.userAvatar,
      'text': instance.text,
      'type': instance.$type,
    };

LiveMessageTrade _$LiveMessageTradeFromJson(Map json) => LiveMessageTrade(
  createAt: const EpochDateTimeConverter().fromJson(
    (json['createAt'] as num).toInt(),
  ),
  username: json['username'] as String,
  userId: const JsonUserIdConverter().fromJson(json['userId'] as String),
  userAvatar: json['userAvatar'] as String? ?? '',
  direction: $enumDecode(_$EnumTokenTradeDirectionEnumMap, json['direction']),
  tokenLogo: json['tokenLogo'] as String? ?? '',
  tokenAmount: Decimal.fromJson(json['tokenAmount'] as String),
  tokenSymbol: json['tokenSymbol'] as String,
  tokenAddress: json['tokenAddress'] as String,
  spentTokenLogo: json['spentTokenLogo'] as String? ?? '',
  spentTokenAmount: Decimal.fromJson(json['spentTokenAmount'] as String),
  spentTokenSymbol: json['spentTokenSymbol'] as String,
  chain: json['chain'] as String,
  $type: json['type'] as String?,
);

Map<String, dynamic> _$LiveMessageTradeToJson(LiveMessageTrade instance) =>
    <String, dynamic>{
      'createAt': const EpochDateTimeConverter().toJson(instance.createAt),
      'username': instance.username,
      'userId': const JsonUserIdConverter().toJson(instance.userId),
      'userAvatar': instance.userAvatar,
      'direction': _$EnumTokenTradeDirectionEnumMap[instance.direction]!,
      'tokenLogo': instance.tokenLogo,
      'tokenAmount': instance.tokenAmount.toJson(),
      'tokenSymbol': instance.tokenSymbol,
      'tokenAddress': instance.tokenAddress,
      'spentTokenLogo': instance.spentTokenLogo,
      'spentTokenAmount': instance.spentTokenAmount.toJson(),
      'spentTokenSymbol': instance.spentTokenSymbol,
      'chain': instance.chain,
      'type': instance.$type,
    };

const _$EnumTokenTradeDirectionEnumMap = {
  EnumTokenTradeDirection.buy: 0,
  EnumTokenTradeDirection.sell: 1,
};

LiveMessageCA _$LiveMessageCAFromJson(Map json) => LiveMessageCA(
  createAt: const EpochDateTimeConverter().fromJson(
    (json['createAt'] as num).toInt(),
  ),
  username: json['username'] as String,
  userId: const JsonUserIdConverter().fromJson(json['userId'] as String),
  userAvatar: json['userAvatar'] as String? ?? '',
  address: json['address'] as String,
  chain: json['chain'] as String,
  icon: json['icon'] as String? ?? '',
  name: json['name'] as String?,
  symbol: json['symbol'] as String?,
  $type: json['type'] as String?,
);

Map<String, dynamic> _$LiveMessageCAToJson(LiveMessageCA instance) =>
    <String, dynamic>{
      'createAt': const EpochDateTimeConverter().toJson(instance.createAt),
      'username': instance.username,
      'userId': const JsonUserIdConverter().toJson(instance.userId),
      'userAvatar': instance.userAvatar,
      'address': instance.address,
      'chain': instance.chain,
      'icon': instance.icon,
      'name': instance.name,
      'symbol': instance.symbol,
      'type': instance.$type,
    };
