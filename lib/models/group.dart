import 'package:flutter/widgets.dart' show BuildContext;
import 'package:freezed_annotation/freezed_annotation.dart';

import '/extensions/build_context_extension.dart' show BuildContextExtension;
import '_shared.dart';
import 'live.dart' show LiveRoomToken;
import 'user.dart';

part 'group.freezed.dart';

part 'group.g.dart';

enum GroupType {
  owned,
  joined,
  explore;

  String displayName(BuildContext context) {
    return switch (this) {
      GroupType.owned => context.l10n.labelGroupsOwned,
      GroupType.joined => context.l10n.labelGroupsJoined,
      GroupType.explore => context.l10n.labelGroupsExplore,
    };
  }
}

@freezed
sealed class Group with _$Group {
  const factory Group({
    @JsonKey(name: 'uniqId') required String id,
    @JsonKey(name: 'name') required String name,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'description') required String description,
    @Json<PERSON>ey(name: 'logo') @Default('') String logo,
    @<PERSON>son<PERSON>ey(name: 'isJoined') @Default(false) bool isJoined,
    @Json<PERSON>ey(name: 'hasApplied') @Default(false) bool hasApplied,
    @<PERSON>son<PERSON><PERSON>(name: 'needConfirm') @Default(false) bool needConfirm,
    @Json<PERSON>ey(name: 'creator') required UserInfo creator,
    @JsonKey(name: 'users') @Default([]) List<UserInfo> users,
    @JsonKey(name: 'userCount') @Default(1) int userCount,
    @EpochDateTimeNullableConverter()
    @JsonKey(name: 'lastLiveTime')
    DateTime? lastLiveTime,
    @EpochDateTimeNullableConverter()
    @JsonKey(name: 'lastLiveEndTime')
    DateTime? lastLiveEndTime,
  }) = _Group;

  const Group._();

  factory Group.fromJson(Map<String, dynamic> json) => _$GroupFromJson(json);

  bool isCreator(UserInfoBase? user) => user?.userId == creator.userId;

  bool get liveEnds => switch ((lastLiveTime, lastLiveEndTime)) {
    (final s?, final e?) when e.isAfter(s) => true,
    _ => false,
  };
}

@freezed
sealed class GroupLiveRoom with _$GroupLiveRoom {
  const factory GroupLiveRoom({
    @JsonKey(name: 'name') required String id,
    @JsonKey(name: 'displayName') @Default('') String displayName,
    @EpochDateTimeConverter()
    @JsonKey(name: 'openTime')
    required DateTime openTime,
    @EpochDateTimeNullableConverter()
    @JsonKey(name: 'closeTime')
    DateTime? closeTime,
    @JsonKey(name: 'userCount') @Default(0) int userCount,
    @JsonKey(name: 'users') @Default([]) List<UserInfo> users,
    @JsonKey(name: 'tokens') @Default([]) List<LiveRoomToken> tokens,
  }) = _GroupLiveRoom;

  const GroupLiveRoom._();

  factory GroupLiveRoom.fromJson(Map<String, dynamic> json) =>
      _$GroupLiveRoomFromJson(json);

  bool get ongoing => closeTime == null;
}
