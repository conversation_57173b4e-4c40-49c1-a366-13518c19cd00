// ignore_for_file: unused_element

import 'dart:convert';
import 'dart:math' as math;
import 'dart:typed_data' show Uint8List;

import 'package:cbor/simple.dart';
import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/widgets.dart' show BuildContext;
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_utils/me_utils.dart' show LogUtil;

import '/internals/methods.dart' show handleExceptions;

import '_shared.dart';
import 'tokens.dart' show EnumTokenTradeDirection;
import 'user.dart';

part 'live.freezed.dart';

part 'live.g.dart';

@freezed
sealed class LiveRoomParticipation with _$LiveRoomParticipation {
  const factory LiveRoomParticipation({
    @JsonKey(name: 'rtcToken') required String rtc,
    @JsonKey(name: 'rtmToken') required String rtm,
    @JsonKey(name: 'channel') LiveRoom? room,
  }) = _LiveRoomParticipation;

  factory LiveRoomParticipation.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomParticipationFromJson(json);
}

@freezed
sealed class LiveRoom with _$LiveRoom {
  const factory LiveRoom({
    @JsonKey(name: 'isPrivate') @Default(false) bool private,
    @JsonKey(name: 'name') required String id,
    @JsonKey(name: 'displayName') required String displayName,
    @JsonKey(name: 'tokenAddress') String? tokenMintAddress,
    @JsonKey(name: 'tokenChain') String? tokenChain,
    @JsonKey(name: 'tokenLogo') String? tokenLogo,
    @JsonKey(name: 'tokenName') String? tokenName,
    @JsonKey(name: 'tokenSymbol') String? tokenSymbol,
    @JsonKey(name: 'groupId') String? rawGroupId,
    @JsonKey(name: 'group') LiveRoomGroup? group,
    @JsonKey(name: 'username') required String username,
    @JsonKey(name: 'userDisplayName') @Default('') String userDisplayName,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
    @JsonKey(name: 'userCount') @Default(0) int userCount,
    @JsonUserIdConverter() @JsonKey(name: 'userUniqId') @Default(0) int userId,
    @JsonKey(name: 'broadcasters') @Default([]) List<UserInfo> broadcasters,
    @JsonUserIdsConverter()
    @JsonKey(name: 'broadcasterIds')
    @Default([])
    List<int> broadcasterIds,
    @EpochDateTimeConverter()
    @JsonKey(name: 'openTime')
    required DateTime openTime,
    @EpochDateTimeNullableConverter()
    @JsonKey(name: 'closeTime')
    DateTime? closeTime,
    @JsonKey(name: 'chatEnabled') @Default(true) bool chatEnabled,
  }) = _LiveRoom;

  const LiveRoom._();

  factory LiveRoom.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomFromJson(json);

  String? get groupId => group?.id ?? rawGroupId;

  bool isHost(int? userId) => userId == this.userId;

  bool isBroadcaster(int? userId) => broadcasterIds.contains(userId);

  bool canSpeakOf(int? userId) => isHost(userId) || isBroadcaster(userId);

  UserInfo get creator {
    return UserInfo(
      userId: userId,
      username: username,
      displayName: userDisplayName,
      avatar: userAvatar,
    );
  }

  String getTitle(BuildContext context) {
    return displayName.or(
      "${userDisplayName.or(username)}'s Live",
    );
  }
}

@freezed
sealed class LiveRoomGroup with _$LiveRoomGroup {
  const factory LiveRoomGroup({
    @JsonKey(name: 'uniqId') required String id,
    @JsonKey(name: 'name') required String name,
    @JsonKey(name: 'description') required String description,
    @JsonKey(name: 'logo') @Default('') String logo,
    @JsonKey(name: 'isOpen') @Default(true) bool opening,
    @JsonKey(name: 'isJoined') @Default(true) bool isJoined,
    @JsonKey(name: 'hasApplied') @Default(false) bool hasApplied,
    @JsonKey(name: 'needConfirm') @Default(false) bool needConfirm,
  }) = _LiveRoomGroup;

  factory LiveRoomGroup.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomGroupFromJson(json);
}

@freezed
sealed class LiveRoomUsers with _$LiveRoomUsers {
  const factory LiveRoomUsers({
    @JsonKey(name: 'users') required List<LiveRoomUser> users,
  }) = _LiveRoomUsers;

  const LiveRoomUsers._();

  factory LiveRoomUsers.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomUsersFromJson(json);

  LiveRoomUser? of(int? userId) {
    if (userId == null) {
      return null;
    }
    return users.firstWhereOrNull((e) => e.userId == userId);
  }

  LiveRoomUsersFiltered toFiltered({
    required LiveRoom room,
    required PersistentUserInfo? fallback,
  }) {
    int creator = room.userId;
    final List<int> broadcasters = <int>[];
    final List<int> players = <int>[];

    for (final user in users) {
      if (user.role == LiveRoomUserRole.creator) {
        creator = user.userId;
        continue;
      }
      if (user.role == LiveRoomUserRole.broadcaster) {
        broadcasters.add(user.userId);
        continue;
      }
      if (user.role == LiveRoomUserRole.player) {
        players.add(user.userId);
        continue;
      }
    }

    final sorted = users.sorted((a, b) {
      if (a.userId == creator) {
        return -1;
      }
      if (b.userId == creator) {
        return 1;
      }
      if (broadcasters.contains(a.userId)) {
        return -1;
      }
      if (broadcasters.contains(b.userId)) {
        return 1;
      }
      if (players.contains(a.userId)) {
        return -1;
      }
      if (players.contains(b.userId)) {
        return 1;
      }
      return 0;
    });

    bool fallbackAddAsUser = switch (fallback?.userId) {
      final u? =>
        creator != u && !broadcasters.contains(u) && !players.contains(u),
      _ => true,
    };

    // Adds the fallback user as the creator if matched and null.
    LiveRoomUser? rCreator = sorted.firstWhereOrNull(
      (e) => e.userId == creator,
    );
    if (fallback case final u? when u.userId == creator && rCreator == null) {
      rCreator = LiveRoomUser.fromBase(u, LiveRoomUserRole.creator);
      fallbackAddAsUser = false;
    }

    // Adds the fallback user as a broadcaster if matched.
    final rBroadcasters = sorted
        .where((e) => broadcasters.contains(e.userId))
        .toList();
    if (fallback case final u?
        when broadcasters.contains(u.userId) &&
            !rBroadcasters.any((e) => e.userId == u.userId)) {
      rBroadcasters.add(
        LiveRoomUser.fromBase(u, LiveRoomUserRole.broadcaster),
      );
      fallbackAddAsUser = false;
    }

    // Adds the fallback user as a broadcaster if matched.
    final rPlayers = sorted.where((e) => players.contains(e.userId)).toList();
    if (fallback case final u?
        when players.contains(u.userId) &&
            !rPlayers.any((e) => e.userId == u.userId)) {
      rPlayers.add(
        LiveRoomUser.fromBase(u, LiveRoomUserRole.player),
      );
      fallbackAddAsUser = false;
    }

    // Adds the fallback user as a user if none of the above roles match.
    final rUsers = sorted
        .where(
          (e) =>
              creator != e.userId &&
              !broadcasters.contains(e.userId) &&
              !players.contains(e.userId),
        )
        .toList();
    if (fallback case final u?
        when fallbackAddAsUser && !rUsers.any((e) => e.userId == u.userId)) {
      rUsers.add(LiveRoomUser.fromBase(u, LiveRoomUserRole.user));
    }

    return LiveRoomUsersFiltered(
      creator: rCreator,
      broadcasters: rBroadcasters,
      players: rPlayers,
      users: rUsers,
    );
  }
}

enum LiveRoomUserRole {
  creator(0),
  broadcaster(1),
  user(2),
  player(3),
  fake(4);

  const LiveRoomUserRole(this.value);

  factory LiveRoomUserRole.fromValue(int? value) {
    return values.firstWhere(
      (e) => e.value == value,
      orElse: () => LiveRoomUserRole.user,
    );
  }

  final int value;
}

final class LiveRoomUserRoleConverter
    implements JsonConverter<LiveRoomUserRole, int?> {
  const LiveRoomUserRoleConverter();

  @override
  LiveRoomUserRole fromJson(int? json) => LiveRoomUserRole.fromValue(json);

  @override
  int? toJson(LiveRoomUserRole object) => object.value;
}

@freezed
sealed class LiveRoomUser extends UserInfoBase with _$LiveRoomUser {
  const factory LiveRoomUser({
    @JsonUserIdConverter() @JsonKey(name: 'uniqId') required int userId,
    @JsonKey(name: 'username') required String username,
    @JsonKey(name: 'displayName') @Default('') String displayName,
    @JsonKey(name: 'avatar') @Default('') String avatar,
    @LiveRoomUserRoleConverter()
    @JsonKey(name: 'role')
    required LiveRoomUserRole role,

    /// For [LiveRoomUserRole.player].
    @JsonKey(name: 'isPaused') @Default(false) bool isPaused,
  }) = _LiveRoomUser;

  const LiveRoomUser._({
    required super.userId,
    required super.username,
    super.displayName = '',
    super.avatar = '',
  });

  factory LiveRoomUser.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomUserFromJson(json);

  factory LiveRoomUser.fromBase(UserInfoBase base, LiveRoomUserRole role) {
    return LiveRoomUser(
      userId: base.userId,
      username: base.username,
      displayName: base.displayName,
      avatar: base.avatar,
      role: role,
    );
  }

  bool get canSpeak {
    return switch (role) {
      LiveRoomUserRole.creator => true,
      LiveRoomUserRole.broadcaster => true,
      _ => false,
    };
  }

  UserInfo toUserInfo() {
    return UserInfo(
      userId: userId,
      username: username,
      displayName: displayName,
      avatar: avatar,
    );
  }
}

@Freezed(fromJson: false, toJson: false)
sealed class LiveRoomUsersFiltered with _$LiveRoomUsersFiltered {
  const factory LiveRoomUsersFiltered({
    // Creator could went offline.
    required LiveRoomUser? creator,
    required List<LiveRoomUser> broadcasters,
    required List<LiveRoomUser> players,
    required List<LiveRoomUser> users,
  }) = _LiveRoomUsersFiltered;

  const LiveRoomUsersFiltered._();

  int get count {
    int result = 0;
    if (creator != null) {
      result++;
    }
    result += broadcasters.length;
    result += players.length;
    result += users.length;
    return result;
  }

  LiveRoomUser? of(int? userId) {
    if (userId == null) {
      return null;
    }
    return switch (userId) {
      final u when creator?.userId == u => creator,
      final u when broadcasters.any((e) => e.userId == u) =>
        broadcasters.firstWhere((e) => e.userId == u),
      final u when players.any((e) => e.userId == u) => players.firstWhere(
        (e) => e.userId == u,
      ),
      final u when users.any((e) => e.userId == u) => users.firstWhere(
        (e) => e.userId == u,
      ),
      _ => null,
    };
  }

  bool canSpeakOf(int? userId) {
    if (userId == null) {
      return false;
    }
    return creator?.userId == userId ||
        broadcasters.any((e) => e.userId == userId);
  }
}

@freezed
sealed class LiveRoomToken with _$LiveRoomToken {
  const factory LiveRoomToken({
    @JsonKey(name: 'isPinned') @Default(false) bool pinned,
    @JsonKey(name: 'address') required String address,
    @JsonKey(name: 'chain') required String chain,
    @JsonKey(name: 'logoUri') @Default('') String icon,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: 'symbol') String? symbol,
  }) = _LiveRoomToken;

  factory LiveRoomToken.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomTokenFromJson(json);
}

@freezed
sealed class LiveRoomSummary with _$LiveRoomSummary {
  const factory LiveRoomSummary({
    @JsonKey(name: 'isPrivate') @Default(false) bool private,
    @JsonKey(name: 'uniqId') required String id,
    @JsonKey(name: 'name') @Default('') String name,
    @JsonKey(name: 'group') LiveRoomGroup? group,
    @JsonKey(name: 'totalViewer') @Default(1) int viewerCount,
    @JsonKey(name: 'mentionCa') @Default(1) int caMentionedCount,
    @JsonKey(name: 'duration') int? duration,
    @EpochDateTimeConverter()
    @JsonKey(name: 'openTime')
    required DateTime openTime,
    @EpochDateTimeConverter() @JsonKey(name: 'closeTime') DateTime? closeTime,
    @JsonKey(name: 'userAvatars') @Default([]) List<String> userAvatars,
    @JsonKey(name: 'tokenLogos') @Default([]) List<String> tokenLogos,
  }) = _LiveRoomSummary;

  factory LiveRoomSummary.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomSummaryFromJson(json);
}

// {
//   "uniqId": "JWt9U6mY9JLkZ4kl",
//   "href": "https://github.com/AstroxNetwork",
//   "siteName": "GitHub",
//   "title": "AstroxNetwork",
//   "description": "AstroxNetwork has 61 repositories available."
// }
@freezed
sealed class LiveRoomLink with _$LiveRoomLink {
  const factory LiveRoomLink({
    @JsonKey(name: 'isPinned') @Default(false) bool pinned,
    @JsonKey(name: 'uniqId') required String id,
    @JsonKey(name: 'href') required String href,
    @JsonKey(name: 'siteName') required String siteName,
    @JsonKey(name: 'title') required String title,
    @JsonKey(name: 'description') @Default('') String description,
    @JsonKey(name: 'favicon') @Default('') String icon,
  }) = _LiveRoomLink;

  factory LiveRoomLink.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomLinkFromJson(json);
}

@freezed
sealed class LiveRPCRequest with _$LiveRPCRequest {
  const factory LiveRPCRequest({
    @JsonKey(name: 'v') required String version,
    @EpochDateTimeConverter() @JsonKey(name: 't') required DateTime timestamp,
    @JsonKey(name: 'n') required String nonce,
    @LiveMethodConverter() @JsonKey(name: 'm') required LiveRPCMethod? method,
    @JsonKey(name: 'p') required Map<String, Object?> params,
  }) = _LiveRPCRequest;

  const LiveRPCRequest._();

  factory LiveRPCRequest.fromJson(Map<String, dynamic> json) =>
      _$LiveRPCRequestFromJson(json);

  factory LiveRPCRequest.create(LiveRPCSerializable data) {
    final t = DateTime.now();
    final n = math.Random.secure()
        .nextDouble()
        .toStringAsFixed(20)
        .substring(2, 11);
    return LiveRPCRequest(
      version: '1.0',
      timestamp: t,
      nonce: n,
      method: data.method,
      params: data.toJson(),
    );
  }

  LiveRPCSerializable? get data {
    try {
      return switch (method) {
        LiveRPCMethod.control => LiveControl.fromJson(params),
        LiveRPCMethod.message => LiveMessage.fromJson(params),
        null => null,
      };
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return null;
    }
  }
}

enum LiveRPCMethod {
  control('control'),
  message('message');

  const LiveRPCMethod(this.value);

  static LiveRPCMethod? fromValue(String value) {
    return LiveRPCMethod.values.firstWhereOrNull((e) => e.value == value);
  }

  final String value;
}

final class LiveMethodConverter
    implements JsonConverter<LiveRPCMethod?, String?> {
  const LiveMethodConverter();

  @override
  LiveRPCMethod? fromJson(String? json) {
    if (json == null) {
      return null;
    }
    final type = LiveRPCMethod.fromValue(json);
    if (type == null) {
      LogUtil.w(
        'Unsupported method $json',
        tag: 'LiveMethodConverter',
        tagWithTrace: false,
        report: true,
      );
    }
    return type;
  }

  @override
  String? toJson(LiveRPCMethod? object) => object?.value;
}

sealed class LiveRPCSerializable {
  const LiveRPCSerializable({
    required this.createAt,
    required this.username,
    required this.userId,
  });

  @EpochDateTimeConverter()
  @JsonKey(name: 'createAt')
  final DateTime createAt;

  @JsonKey(name: 'username')
  final String username;

  @JsonUserIdConverter()
  @JsonKey(name: 'userId')
  final int userId;

  LiveRPCMethod get method;

  Map<String, Object?> toJson();
}

/// RTM sending bytes are wrapped by `CBOR -> Base64 -> UTF-8`.
abstract class LiveRPCSerializer {
  static LiveRPCRequest? decodeRequest(Uint8List data) {
    try {
      final decoded = cbor.decode(base64.decode(utf8.decode(data)));
      if (decoded is! Map) {
        return null;
      }
      final json = decoded.cast<String, dynamic>();
      final deserialized = LiveRPCRequest.fromJson(json);
      return deserialized;
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return null;
    }
  }

  static Uint8List encodeRequest(LiveRPCRequest request) {
    final encoded = utf8.encode(base64.encode(cbor.encode(request.toJson())));
    return Uint8List.fromList(encoded);
  }

  static LiveRPCRequest serializeRequest(LiveRPCSerializable content) {
    return LiveRPCRequest.create(content);
  }
}

@Freezed(unionKey: 'type')
sealed class LiveControl extends LiveRPCSerializable with _$LiveControl {
  const factory LiveControl.closeRoom({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required DateTime createAt,
    @JsonKey(name: 'username') required String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required int userId,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
  }) = LiveControlCloseRoom;

  const factory LiveControl.kickUser({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required DateTime createAt,
    @JsonKey(name: 'username') required String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required int userId,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
    @JsonKey(name: 'uuid') required String uuid,
  }) = LiveControlKickUser;

  const factory LiveControl.pinnedCA({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required DateTime createAt,
    @JsonKey(name: 'username') required String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required int userId,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
    @JsonKey(name: 'address') required String address,
    @JsonKey(name: 'chain') required String chain,
    @JsonKey(name: 'icon') @Default('') String icon,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: 'symbol') String? symbol,
  }) = LiveControlPinnedCA;

  const factory LiveControl.unpinnedCA({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required DateTime createAt,
    @JsonKey(name: 'username') required String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required int userId,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
    @JsonKey(name: 'address') required String address,
    @JsonKey(name: 'chain') required String chain,
    @JsonKey(name: 'icon') @Default('') String icon,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: 'symbol') String? symbol,
  }) = LiveControlUnpinnedCA;

  const factory LiveControl.pushedCA({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required DateTime createAt,
    @JsonKey(name: 'username') required String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required int userId,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
    @JsonKey(name: 'address') required String address,
    @JsonKey(name: 'chain') required String chain,
    @JsonKey(name: 'icon') @Default('') String icon,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: 'symbol') String? symbol,
  }) = LiveControlPushedCA;

  const factory LiveControl.pinnedLink({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required DateTime createAt,
    @JsonKey(name: 'username') required String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required int userId,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
    @JsonKey(name: 'url') required String url,
    @JsonKey(name: 'icon') @Default('') String icon,
    @JsonKey(name: 'title') @Default('') String title,
    @JsonKey(name: 'description') @Default('') String description,
    @JsonKey(name: 'siteName') @Default('') String siteName,
  }) = LiveControlPinnedLink;

  const factory LiveControl.unpinnedLink({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required DateTime createAt,
    @JsonKey(name: 'username') required String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required int userId,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
    @JsonKey(name: 'url') required String url,
    @JsonKey(name: 'icon') @Default('') String icon,
    @JsonKey(name: 'title') @Default('') String title,
    @JsonKey(name: 'description') @Default('') String description,
    @JsonKey(name: 'siteName') @Default('') String siteName,
  }) = LiveControlUnpinnedLink;

  const factory LiveControl.pushedLink({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required DateTime createAt,
    @JsonKey(name: 'username') required String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required int userId,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
    @JsonKey(name: 'url') required String url,
    @JsonKey(name: 'icon') @Default('') String icon,
    @JsonKey(name: 'title') @Default('') String title,
    @JsonKey(name: 'description') @Default('') String description,
    @JsonKey(name: 'siteName') @Default('') String siteName,
  }) = LiveControlPushedLink;

  const factory LiveControl.requestMicrophone({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required DateTime createAt,
    @JsonKey(name: 'username') required String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required int userId,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
    @JsonKey(name: 'userInfo') required UserInfo userInfo,
  }) = LiveControlRequestMicrophone;

  const factory LiveControl.updateMicrophone({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required DateTime createAt,
    @JsonKey(name: 'username') required String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required int userId,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
    @JsonKey(name: 'userInfo') required UserInfo userInfo,
    @JsonKey(name: 'enabled') required bool enabled,
  }) = LiveControlUpdateMicrophone;

  const factory LiveControl.updateChatAbility({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required DateTime createAt,
    @JsonKey(name: 'username') required String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required int userId,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
    @JsonKey(name: 'enabled') required bool enabled,
  }) = LiveControlUpdateChatAbility;

  const LiveControl._({
    required super.createAt,
    required super.username,
    required super.userId,
  });

  factory LiveControl.fromJson(Map<String, Object?> json) =>
      _$LiveControlFromJson(json);

  @override
  LiveRPCMethod get method => LiveRPCMethod.control;
}

@Freezed(unionKey: 'type')
sealed class LiveMessage extends LiveRPCSerializable with _$LiveMessage {
  const factory LiveMessage.plain({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required DateTime createAt,
    @JsonKey(name: 'username') required String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required int userId,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
    @JsonKey(name: 'text') required String text,
  }) = LiveMessagePlain;

  const factory LiveMessage.trade({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required DateTime createAt,
    @JsonKey(name: 'username') required String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required int userId,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
    @JsonKey(name: 'direction') required EnumTokenTradeDirection direction,
    @JsonKey(name: 'tokenLogo') @Default('') String tokenLogo,
    @JsonKey(name: 'tokenAmount') required Decimal tokenAmount,
    @JsonKey(name: 'tokenSymbol') required String tokenSymbol,
    @JsonKey(name: 'tokenAddress') required String tokenAddress,
    @JsonKey(name: 'spentTokenLogo') @Default('') String spentTokenLogo,
    @JsonKey(name: 'spentTokenAmount') required Decimal spentTokenAmount,
    @JsonKey(name: 'spentTokenSymbol') required String spentTokenSymbol,
    @JsonKey(name: 'chain') required String chain,
  }) = LiveMessageTrade;

  const factory LiveMessage.ca({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required DateTime createAt,
    @JsonKey(name: 'username') required String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required int userId,
    @JsonKey(name: 'userAvatar') @Default('') String userAvatar,
    @JsonKey(name: 'address') required String address,
    @JsonKey(name: 'chain') required String chain,
    @JsonKey(name: 'icon') @Default('') String icon,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: 'symbol') String? symbol,
  }) = LiveMessageCA;

  const LiveMessage._({
    required super.createAt,
    required super.username,
    required super.userId,
  });

  factory LiveMessage.fromJson(Map<String, Object?> json) =>
      _$LiveMessageFromJson(json);

  @override
  LiveRPCMethod get method => LiveRPCMethod.message;
}
