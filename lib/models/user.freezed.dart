// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserInfo {
  @JsonUserIdConverter()
  @JsonKey(name: 'uniqId')
  int get userId;
  @JsonKey(name: 'username')
  String get username;
  @JsonKey(name: 'displayName')
  String get displayName;
  @JsonKey(name: 'avatar')
  String get avatar;
  @JsonKey(name: 'socialAccounts')
  Map<String, UserSocialAccount> get socialAccounts;
  @JsonKey(name: 'followersCount')
  int get followersCount;
  @JsonKey(name: 'followingCount')
  int get followingCount;
  @JsonKey(name: 'following')
  bool get following;
  @JsonKey(name: 'followedBy')
  bool get followedBy;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserInfoCopyWith<UserInfo> get copyWith =>
      _$UserInfoCopyWithImpl<UserInfo>(this as UserInfo, _$identity);

  /// Serializes this UserInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserInfo &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            const DeepCollectionEquality().equals(
              other.socialAccounts,
              socialAccounts,
            ) &&
            (identical(other.followersCount, followersCount) ||
                other.followersCount == followersCount) &&
            (identical(other.followingCount, followingCount) ||
                other.followingCount == followingCount) &&
            (identical(other.following, following) ||
                other.following == following) &&
            (identical(other.followedBy, followedBy) ||
                other.followedBy == followedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    username,
    displayName,
    avatar,
    const DeepCollectionEquality().hash(socialAccounts),
    followersCount,
    followingCount,
    following,
    followedBy,
  );

  @override
  String toString() {
    return 'UserInfo(userId: $userId, username: $username, displayName: $displayName, avatar: $avatar, socialAccounts: $socialAccounts, followersCount: $followersCount, followingCount: $followingCount, following: $following, followedBy: $followedBy)';
  }
}

/// @nodoc
abstract mixin class $UserInfoCopyWith<$Res> {
  factory $UserInfoCopyWith(UserInfo value, $Res Function(UserInfo) _then) =
      _$UserInfoCopyWithImpl;
  @useResult
  $Res call({
    @JsonUserIdConverter() @JsonKey(name: 'uniqId') int userId,
    @JsonKey(name: 'username') String username,
    @JsonKey(name: 'displayName') String displayName,
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'socialAccounts')
    Map<String, UserSocialAccount> socialAccounts,
    @JsonKey(name: 'followersCount') int followersCount,
    @JsonKey(name: 'followingCount') int followingCount,
    @JsonKey(name: 'following') bool following,
    @JsonKey(name: 'followedBy') bool followedBy,
  });
}

/// @nodoc
class _$UserInfoCopyWithImpl<$Res> implements $UserInfoCopyWith<$Res> {
  _$UserInfoCopyWithImpl(this._self, this._then);

  final UserInfo _self;
  final $Res Function(UserInfo) _then;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? username = null,
    Object? displayName = null,
    Object? avatar = null,
    Object? socialAccounts = null,
    Object? followersCount = null,
    Object? followingCount = null,
    Object? following = null,
    Object? followedBy = null,
  }) {
    return _then(
      _self.copyWith(
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        displayName: null == displayName
            ? _self.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        socialAccounts: null == socialAccounts
            ? _self.socialAccounts
            : socialAccounts // ignore: cast_nullable_to_non_nullable
                  as Map<String, UserSocialAccount>,
        followersCount: null == followersCount
            ? _self.followersCount
            : followersCount // ignore: cast_nullable_to_non_nullable
                  as int,
        followingCount: null == followingCount
            ? _self.followingCount
            : followingCount // ignore: cast_nullable_to_non_nullable
                  as int,
        following: null == following
            ? _self.following
            : following // ignore: cast_nullable_to_non_nullable
                  as bool,
        followedBy: null == followedBy
            ? _self.followedBy
            : followedBy // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _UserInfo extends UserInfo implements UserInfoBaseWithSocials {
  const _UserInfo({
    @JsonUserIdConverter() @JsonKey(name: 'uniqId') required final int userId,
    @JsonKey(name: 'username') required final String username,
    @JsonKey(name: 'displayName') final String displayName = '',
    @JsonKey(name: 'avatar') final String avatar = '',
    @JsonKey(name: 'socialAccounts')
    final Map<String, UserSocialAccount> socialAccounts = const {},
    @JsonKey(name: 'followersCount') this.followersCount = 0,
    @JsonKey(name: 'followingCount') this.followingCount = 0,
    @JsonKey(name: 'following') this.following = false,
    @JsonKey(name: 'followedBy') this.followedBy = false,
  }) : _socialAccounts = socialAccounts,
       super._(
         userId: userId,
         username: username,
         displayName: displayName,
         avatar: avatar,
       );
  factory _UserInfo.fromJson(Map<String, dynamic> json) =>
      _$UserInfoFromJson(json);

  final Map<String, UserSocialAccount> _socialAccounts;
  @override
  @JsonKey(name: 'socialAccounts')
  Map<String, UserSocialAccount> get socialAccounts {
    if (_socialAccounts is EqualUnmodifiableMapView) return _socialAccounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_socialAccounts);
  }

  @override
  @JsonKey(name: 'followersCount')
  final int followersCount;
  @override
  @JsonKey(name: 'followingCount')
  final int followingCount;
  @override
  @JsonKey(name: 'following')
  final bool following;
  @override
  @JsonKey(name: 'followedBy')
  final bool followedBy;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserInfoCopyWith<_UserInfo> get copyWith =>
      __$UserInfoCopyWithImpl<_UserInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserInfoToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserInfo &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            const DeepCollectionEquality().equals(
              other._socialAccounts,
              _socialAccounts,
            ) &&
            (identical(other.followersCount, followersCount) ||
                other.followersCount == followersCount) &&
            (identical(other.followingCount, followingCount) ||
                other.followingCount == followingCount) &&
            (identical(other.following, following) ||
                other.following == following) &&
            (identical(other.followedBy, followedBy) ||
                other.followedBy == followedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    username,
    displayName,
    avatar,
    const DeepCollectionEquality().hash(_socialAccounts),
    followersCount,
    followingCount,
    following,
    followedBy,
  );

  @override
  String toString() {
    return 'UserInfo(userId: $userId, username: $username, displayName: $displayName, avatar: $avatar, socialAccounts: $socialAccounts, followersCount: $followersCount, followingCount: $followingCount, following: $following, followedBy: $followedBy)';
  }
}

/// @nodoc
abstract mixin class _$UserInfoCopyWith<$Res>
    implements $UserInfoCopyWith<$Res> {
  factory _$UserInfoCopyWith(_UserInfo value, $Res Function(_UserInfo) _then) =
      __$UserInfoCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonUserIdConverter() @JsonKey(name: 'uniqId') int userId,
    @JsonKey(name: 'username') String username,
    @JsonKey(name: 'displayName') String displayName,
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'socialAccounts')
    Map<String, UserSocialAccount> socialAccounts,
    @JsonKey(name: 'followersCount') int followersCount,
    @JsonKey(name: 'followingCount') int followingCount,
    @JsonKey(name: 'following') bool following,
    @JsonKey(name: 'followedBy') bool followedBy,
  });
}

/// @nodoc
class __$UserInfoCopyWithImpl<$Res> implements _$UserInfoCopyWith<$Res> {
  __$UserInfoCopyWithImpl(this._self, this._then);

  final _UserInfo _self;
  final $Res Function(_UserInfo) _then;

  /// Create a copy of UserInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? userId = null,
    Object? username = null,
    Object? displayName = null,
    Object? avatar = null,
    Object? socialAccounts = null,
    Object? followersCount = null,
    Object? followingCount = null,
    Object? following = null,
    Object? followedBy = null,
  }) {
    return _then(
      _UserInfo(
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        displayName: null == displayName
            ? _self.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        socialAccounts: null == socialAccounts
            ? _self._socialAccounts
            : socialAccounts // ignore: cast_nullable_to_non_nullable
                  as Map<String, UserSocialAccount>,
        followersCount: null == followersCount
            ? _self.followersCount
            : followersCount // ignore: cast_nullable_to_non_nullable
                  as int,
        followingCount: null == followingCount
            ? _self.followingCount
            : followingCount // ignore: cast_nullable_to_non_nullable
                  as int,
        following: null == following
            ? _self.following
            : following // ignore: cast_nullable_to_non_nullable
                  as bool,
        followedBy: null == followedBy
            ? _self.followedBy
            : followedBy // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
mixin _$PersistentUserInfo {
  @JsonUserIdConverter()
  @JsonKey(name: 'uniqId')
  int get userId;
  @JsonKey(name: 'username')
  String get username;
  @JsonKey(name: 'displayName')
  String get displayName;
  @JsonKey(name: 'avatar')
  String get avatar;
  @JsonKey(name: 'isWalletBlocked')
  bool get walletBlocked;
  @JsonKey(name: 'isActive')
  bool get activated;
  @JsonKey(
    name: PersistentUserInfo._keyWalletAddressesByChains,
    readValue: PersistentUserInfo._walletAddressReadValue,
  )
  Map<String, String> get walletAddressesByChains;
  @JsonKey(name: 'referCode')
  String get referCode;
  @JsonKey(name: 'referCnt')
  int get referCount;
  @JsonKey(name: 'socialAccounts')
  Map<String, UserSocialAccount> get socialAccounts;
  @JsonKey(name: 'followersCount')
  int get followersCount;
  @JsonKey(name: 'followingCount')
  int get followingCount;

  /// Create a copy of PersistentUserInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PersistentUserInfoCopyWith<PersistentUserInfo> get copyWith =>
      _$PersistentUserInfoCopyWithImpl<PersistentUserInfo>(
        this as PersistentUserInfo,
        _$identity,
      );

  /// Serializes this PersistentUserInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PersistentUserInfo &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.walletBlocked, walletBlocked) ||
                other.walletBlocked == walletBlocked) &&
            (identical(other.activated, activated) ||
                other.activated == activated) &&
            const DeepCollectionEquality().equals(
              other.walletAddressesByChains,
              walletAddressesByChains,
            ) &&
            (identical(other.referCode, referCode) ||
                other.referCode == referCode) &&
            (identical(other.referCount, referCount) ||
                other.referCount == referCount) &&
            const DeepCollectionEquality().equals(
              other.socialAccounts,
              socialAccounts,
            ) &&
            (identical(other.followersCount, followersCount) ||
                other.followersCount == followersCount) &&
            (identical(other.followingCount, followingCount) ||
                other.followingCount == followingCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    username,
    displayName,
    avatar,
    walletBlocked,
    activated,
    const DeepCollectionEquality().hash(walletAddressesByChains),
    referCode,
    referCount,
    const DeepCollectionEquality().hash(socialAccounts),
    followersCount,
    followingCount,
  );

  @override
  String toString() {
    return 'PersistentUserInfo(userId: $userId, username: $username, displayName: $displayName, avatar: $avatar, walletBlocked: $walletBlocked, activated: $activated, walletAddressesByChains: $walletAddressesByChains, referCode: $referCode, referCount: $referCount, socialAccounts: $socialAccounts, followersCount: $followersCount, followingCount: $followingCount)';
  }
}

/// @nodoc
abstract mixin class $PersistentUserInfoCopyWith<$Res> {
  factory $PersistentUserInfoCopyWith(
    PersistentUserInfo value,
    $Res Function(PersistentUserInfo) _then,
  ) = _$PersistentUserInfoCopyWithImpl;
  @useResult
  $Res call({
    @JsonUserIdConverter() @JsonKey(name: 'uniqId') int userId,
    @JsonKey(name: 'username') String username,
    @JsonKey(name: 'displayName') String displayName,
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'isWalletBlocked') bool walletBlocked,
    @JsonKey(name: 'isActive') bool activated,
    @JsonKey(
      name: PersistentUserInfo._keyWalletAddressesByChains,
      readValue: PersistentUserInfo._walletAddressReadValue,
    )
    Map<String, String> walletAddressesByChains,
    @JsonKey(name: 'referCode') String referCode,
    @JsonKey(name: 'referCnt') int referCount,
    @JsonKey(name: 'socialAccounts')
    Map<String, UserSocialAccount> socialAccounts,
    @JsonKey(name: 'followersCount') int followersCount,
    @JsonKey(name: 'followingCount') int followingCount,
  });
}

/// @nodoc
class _$PersistentUserInfoCopyWithImpl<$Res>
    implements $PersistentUserInfoCopyWith<$Res> {
  _$PersistentUserInfoCopyWithImpl(this._self, this._then);

  final PersistentUserInfo _self;
  final $Res Function(PersistentUserInfo) _then;

  /// Create a copy of PersistentUserInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? username = null,
    Object? displayName = null,
    Object? avatar = null,
    Object? walletBlocked = null,
    Object? activated = null,
    Object? walletAddressesByChains = null,
    Object? referCode = null,
    Object? referCount = null,
    Object? socialAccounts = null,
    Object? followersCount = null,
    Object? followingCount = null,
  }) {
    return _then(
      _self.copyWith(
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        displayName: null == displayName
            ? _self.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        walletBlocked: null == walletBlocked
            ? _self.walletBlocked
            : walletBlocked // ignore: cast_nullable_to_non_nullable
                  as bool,
        activated: null == activated
            ? _self.activated
            : activated // ignore: cast_nullable_to_non_nullable
                  as bool,
        walletAddressesByChains: null == walletAddressesByChains
            ? _self.walletAddressesByChains
            : walletAddressesByChains // ignore: cast_nullable_to_non_nullable
                  as Map<String, String>,
        referCode: null == referCode
            ? _self.referCode
            : referCode // ignore: cast_nullable_to_non_nullable
                  as String,
        referCount: null == referCount
            ? _self.referCount
            : referCount // ignore: cast_nullable_to_non_nullable
                  as int,
        socialAccounts: null == socialAccounts
            ? _self.socialAccounts
            : socialAccounts // ignore: cast_nullable_to_non_nullable
                  as Map<String, UserSocialAccount>,
        followersCount: null == followersCount
            ? _self.followersCount
            : followersCount // ignore: cast_nullable_to_non_nullable
                  as int,
        followingCount: null == followingCount
            ? _self.followingCount
            : followingCount // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _PersistentUserInfo extends PersistentUserInfo
    implements UserInfoBaseWithSocials {
  const _PersistentUserInfo({
    @JsonUserIdConverter() @JsonKey(name: 'uniqId') required final int userId,
    @JsonKey(name: 'username') required final String username,
    @JsonKey(name: 'displayName') final String displayName = '',
    @JsonKey(name: 'avatar') final String avatar = '',
    @JsonKey(name: 'isWalletBlocked') this.walletBlocked = false,
    @JsonKey(name: 'isActive') this.activated = true,
    @JsonKey(
      name: PersistentUserInfo._keyWalletAddressesByChains,
      readValue: PersistentUserInfo._walletAddressReadValue,
    )
    required final Map<String, String> walletAddressesByChains,
    @JsonKey(name: 'referCode') this.referCode = '',
    @JsonKey(name: 'referCnt') this.referCount = 0,
    @JsonKey(name: 'socialAccounts')
    final Map<String, UserSocialAccount> socialAccounts = const {},
    @JsonKey(name: 'followersCount') this.followersCount = 0,
    @JsonKey(name: 'followingCount') this.followingCount = 0,
  }) : _walletAddressesByChains = walletAddressesByChains,
       _socialAccounts = socialAccounts,
       super._(
         userId: userId,
         username: username,
         displayName: displayName,
         avatar: avatar,
       );
  factory _PersistentUserInfo.fromJson(Map<String, dynamic> json) =>
      _$PersistentUserInfoFromJson(json);

  @override
  @JsonKey(name: 'isWalletBlocked')
  final bool walletBlocked;
  @override
  @JsonKey(name: 'isActive')
  final bool activated;
  final Map<String, String> _walletAddressesByChains;
  @override
  @JsonKey(
    name: PersistentUserInfo._keyWalletAddressesByChains,
    readValue: PersistentUserInfo._walletAddressReadValue,
  )
  Map<String, String> get walletAddressesByChains {
    if (_walletAddressesByChains is EqualUnmodifiableMapView)
      return _walletAddressesByChains;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_walletAddressesByChains);
  }

  @override
  @JsonKey(name: 'referCode')
  final String referCode;
  @override
  @JsonKey(name: 'referCnt')
  final int referCount;
  final Map<String, UserSocialAccount> _socialAccounts;
  @override
  @JsonKey(name: 'socialAccounts')
  Map<String, UserSocialAccount> get socialAccounts {
    if (_socialAccounts is EqualUnmodifiableMapView) return _socialAccounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_socialAccounts);
  }

  @override
  @JsonKey(name: 'followersCount')
  final int followersCount;
  @override
  @JsonKey(name: 'followingCount')
  final int followingCount;

  /// Create a copy of PersistentUserInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PersistentUserInfoCopyWith<_PersistentUserInfo> get copyWith =>
      __$PersistentUserInfoCopyWithImpl<_PersistentUserInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PersistentUserInfoToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PersistentUserInfo &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.walletBlocked, walletBlocked) ||
                other.walletBlocked == walletBlocked) &&
            (identical(other.activated, activated) ||
                other.activated == activated) &&
            const DeepCollectionEquality().equals(
              other._walletAddressesByChains,
              _walletAddressesByChains,
            ) &&
            (identical(other.referCode, referCode) ||
                other.referCode == referCode) &&
            (identical(other.referCount, referCount) ||
                other.referCount == referCount) &&
            const DeepCollectionEquality().equals(
              other._socialAccounts,
              _socialAccounts,
            ) &&
            (identical(other.followersCount, followersCount) ||
                other.followersCount == followersCount) &&
            (identical(other.followingCount, followingCount) ||
                other.followingCount == followingCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    username,
    displayName,
    avatar,
    walletBlocked,
    activated,
    const DeepCollectionEquality().hash(_walletAddressesByChains),
    referCode,
    referCount,
    const DeepCollectionEquality().hash(_socialAccounts),
    followersCount,
    followingCount,
  );

  @override
  String toString() {
    return 'PersistentUserInfo(userId: $userId, username: $username, displayName: $displayName, avatar: $avatar, walletBlocked: $walletBlocked, activated: $activated, walletAddressesByChains: $walletAddressesByChains, referCode: $referCode, referCount: $referCount, socialAccounts: $socialAccounts, followersCount: $followersCount, followingCount: $followingCount)';
  }
}

/// @nodoc
abstract mixin class _$PersistentUserInfoCopyWith<$Res>
    implements $PersistentUserInfoCopyWith<$Res> {
  factory _$PersistentUserInfoCopyWith(
    _PersistentUserInfo value,
    $Res Function(_PersistentUserInfo) _then,
  ) = __$PersistentUserInfoCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonUserIdConverter() @JsonKey(name: 'uniqId') int userId,
    @JsonKey(name: 'username') String username,
    @JsonKey(name: 'displayName') String displayName,
    @JsonKey(name: 'avatar') String avatar,
    @JsonKey(name: 'isWalletBlocked') bool walletBlocked,
    @JsonKey(name: 'isActive') bool activated,
    @JsonKey(
      name: PersistentUserInfo._keyWalletAddressesByChains,
      readValue: PersistentUserInfo._walletAddressReadValue,
    )
    Map<String, String> walletAddressesByChains,
    @JsonKey(name: 'referCode') String referCode,
    @JsonKey(name: 'referCnt') int referCount,
    @JsonKey(name: 'socialAccounts')
    Map<String, UserSocialAccount> socialAccounts,
    @JsonKey(name: 'followersCount') int followersCount,
    @JsonKey(name: 'followingCount') int followingCount,
  });
}

/// @nodoc
class __$PersistentUserInfoCopyWithImpl<$Res>
    implements _$PersistentUserInfoCopyWith<$Res> {
  __$PersistentUserInfoCopyWithImpl(this._self, this._then);

  final _PersistentUserInfo _self;
  final $Res Function(_PersistentUserInfo) _then;

  /// Create a copy of PersistentUserInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? userId = null,
    Object? username = null,
    Object? displayName = null,
    Object? avatar = null,
    Object? walletBlocked = null,
    Object? activated = null,
    Object? walletAddressesByChains = null,
    Object? referCode = null,
    Object? referCount = null,
    Object? socialAccounts = null,
    Object? followersCount = null,
    Object? followingCount = null,
  }) {
    return _then(
      _PersistentUserInfo(
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        displayName: null == displayName
            ? _self.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        walletBlocked: null == walletBlocked
            ? _self.walletBlocked
            : walletBlocked // ignore: cast_nullable_to_non_nullable
                  as bool,
        activated: null == activated
            ? _self.activated
            : activated // ignore: cast_nullable_to_non_nullable
                  as bool,
        walletAddressesByChains: null == walletAddressesByChains
            ? _self._walletAddressesByChains
            : walletAddressesByChains // ignore: cast_nullable_to_non_nullable
                  as Map<String, String>,
        referCode: null == referCode
            ? _self.referCode
            : referCode // ignore: cast_nullable_to_non_nullable
                  as String,
        referCount: null == referCount
            ? _self.referCount
            : referCount // ignore: cast_nullable_to_non_nullable
                  as int,
        socialAccounts: null == socialAccounts
            ? _self._socialAccounts
            : socialAccounts // ignore: cast_nullable_to_non_nullable
                  as Map<String, UserSocialAccount>,
        followersCount: null == followersCount
            ? _self.followersCount
            : followersCount // ignore: cast_nullable_to_non_nullable
                  as int,
        followingCount: null == followingCount
            ? _self.followingCount
            : followingCount // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
mixin _$UserSocialAccount {
  @JsonKey(name: 'platform')
  String get platform;
  @JsonKey(name: 'username')
  String get username;
  @JsonKey(name: 'displayName')
  String get displayName;
  @JsonKey(name: 'avatar')
  String get avatar;

  /// Create a copy of UserSocialAccount
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserSocialAccountCopyWith<UserSocialAccount> get copyWith =>
      _$UserSocialAccountCopyWithImpl<UserSocialAccount>(
        this as UserSocialAccount,
        _$identity,
      );

  /// Serializes this UserSocialAccount to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserSocialAccount &&
            (identical(other.platform, platform) ||
                other.platform == platform) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.avatar, avatar) || other.avatar == avatar));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, platform, username, displayName, avatar);

  @override
  String toString() {
    return 'UserSocialAccount(platform: $platform, username: $username, displayName: $displayName, avatar: $avatar)';
  }
}

/// @nodoc
abstract mixin class $UserSocialAccountCopyWith<$Res> {
  factory $UserSocialAccountCopyWith(
    UserSocialAccount value,
    $Res Function(UserSocialAccount) _then,
  ) = _$UserSocialAccountCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'platform') String platform,
    @JsonKey(name: 'username') String username,
    @JsonKey(name: 'displayName') String displayName,
    @JsonKey(name: 'avatar') String avatar,
  });
}

/// @nodoc
class _$UserSocialAccountCopyWithImpl<$Res>
    implements $UserSocialAccountCopyWith<$Res> {
  _$UserSocialAccountCopyWithImpl(this._self, this._then);

  final UserSocialAccount _self;
  final $Res Function(UserSocialAccount) _then;

  /// Create a copy of UserSocialAccount
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? platform = null,
    Object? username = null,
    Object? displayName = null,
    Object? avatar = null,
  }) {
    return _then(
      _self.copyWith(
        platform: null == platform
            ? _self.platform
            : platform // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        displayName: null == displayName
            ? _self.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _UserSocialAccount implements UserSocialAccount {
  const _UserSocialAccount({
    @JsonKey(name: 'platform') required this.platform,
    @JsonKey(name: 'username') required this.username,
    @JsonKey(name: 'displayName') this.displayName = '',
    @JsonKey(name: 'avatar') this.avatar = '',
  });
  factory _UserSocialAccount.fromJson(Map<String, dynamic> json) =>
      _$UserSocialAccountFromJson(json);

  @override
  @JsonKey(name: 'platform')
  final String platform;
  @override
  @JsonKey(name: 'username')
  final String username;
  @override
  @JsonKey(name: 'displayName')
  final String displayName;
  @override
  @JsonKey(name: 'avatar')
  final String avatar;

  /// Create a copy of UserSocialAccount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserSocialAccountCopyWith<_UserSocialAccount> get copyWith =>
      __$UserSocialAccountCopyWithImpl<_UserSocialAccount>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserSocialAccountToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserSocialAccount &&
            (identical(other.platform, platform) ||
                other.platform == platform) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.avatar, avatar) || other.avatar == avatar));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, platform, username, displayName, avatar);

  @override
  String toString() {
    return 'UserSocialAccount(platform: $platform, username: $username, displayName: $displayName, avatar: $avatar)';
  }
}

/// @nodoc
abstract mixin class _$UserSocialAccountCopyWith<$Res>
    implements $UserSocialAccountCopyWith<$Res> {
  factory _$UserSocialAccountCopyWith(
    _UserSocialAccount value,
    $Res Function(_UserSocialAccount) _then,
  ) = __$UserSocialAccountCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'platform') String platform,
    @JsonKey(name: 'username') String username,
    @JsonKey(name: 'displayName') String displayName,
    @JsonKey(name: 'avatar') String avatar,
  });
}

/// @nodoc
class __$UserSocialAccountCopyWithImpl<$Res>
    implements _$UserSocialAccountCopyWith<$Res> {
  __$UserSocialAccountCopyWithImpl(this._self, this._then);

  final _UserSocialAccount _self;
  final $Res Function(_UserSocialAccount) _then;

  /// Create a copy of UserSocialAccount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? platform = null,
    Object? username = null,
    Object? displayName = null,
    Object? avatar = null,
  }) {
    return _then(
      _UserSocialAccount(
        platform: null == platform
            ? _self.platform
            : platform // ignore: cast_nullable_to_non_nullable
                  as String,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        displayName: null == displayName
            ? _self.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$UserAccountProvider {
  @JsonKey(name: 'providerId')
  String get providerId;
  @JsonKey(name: 'username')
  String? get username;
  @JsonKey(name: 'displayName')
  String? get displayName;
  @JsonKey(name: 'email')
  String? get email;
  @JsonKey(name: 'uid')
  String? get uid;
  @JsonKey(name: 'avatar')
  String get avatar;

  /// Create a copy of UserAccountProvider
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserAccountProviderCopyWith<UserAccountProvider> get copyWith =>
      _$UserAccountProviderCopyWithImpl<UserAccountProvider>(
        this as UserAccountProvider,
        _$identity,
      );

  /// Serializes this UserAccountProvider to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserAccountProvider &&
            (identical(other.providerId, providerId) ||
                other.providerId == providerId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.uid, uid) || other.uid == uid) &&
            (identical(other.avatar, avatar) || other.avatar == avatar));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    providerId,
    username,
    displayName,
    email,
    uid,
    avatar,
  );

  @override
  String toString() {
    return 'UserAccountProvider(providerId: $providerId, username: $username, displayName: $displayName, email: $email, uid: $uid, avatar: $avatar)';
  }
}

/// @nodoc
abstract mixin class $UserAccountProviderCopyWith<$Res> {
  factory $UserAccountProviderCopyWith(
    UserAccountProvider value,
    $Res Function(UserAccountProvider) _then,
  ) = _$UserAccountProviderCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'providerId') String providerId,
    @JsonKey(name: 'username') String? username,
    @JsonKey(name: 'displayName') String? displayName,
    @JsonKey(name: 'email') String? email,
    @JsonKey(name: 'uid') String? uid,
    @JsonKey(name: 'avatar') String avatar,
  });
}

/// @nodoc
class _$UserAccountProviderCopyWithImpl<$Res>
    implements $UserAccountProviderCopyWith<$Res> {
  _$UserAccountProviderCopyWithImpl(this._self, this._then);

  final UserAccountProvider _self;
  final $Res Function(UserAccountProvider) _then;

  /// Create a copy of UserAccountProvider
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? providerId = null,
    Object? username = freezed,
    Object? displayName = freezed,
    Object? email = freezed,
    Object? uid = freezed,
    Object? avatar = null,
  }) {
    return _then(
      _self.copyWith(
        providerId: null == providerId
            ? _self.providerId
            : providerId // ignore: cast_nullable_to_non_nullable
                  as String,
        username: freezed == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String?,
        displayName: freezed == displayName
            ? _self.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String?,
        email: freezed == email
            ? _self.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String?,
        uid: freezed == uid
            ? _self.uid
            : uid // ignore: cast_nullable_to_non_nullable
                  as String?,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _UserAccountProvider implements UserAccountProvider {
  const _UserAccountProvider({
    @JsonKey(name: 'providerId') required this.providerId,
    @JsonKey(name: 'username') this.username,
    @JsonKey(name: 'displayName') this.displayName,
    @JsonKey(name: 'email') this.email,
    @JsonKey(name: 'uid') this.uid,
    @JsonKey(name: 'avatar') this.avatar = '',
  });
  factory _UserAccountProvider.fromJson(Map<String, dynamic> json) =>
      _$UserAccountProviderFromJson(json);

  @override
  @JsonKey(name: 'providerId')
  final String providerId;
  @override
  @JsonKey(name: 'username')
  final String? username;
  @override
  @JsonKey(name: 'displayName')
  final String? displayName;
  @override
  @JsonKey(name: 'email')
  final String? email;
  @override
  @JsonKey(name: 'uid')
  final String? uid;
  @override
  @JsonKey(name: 'avatar')
  final String avatar;

  /// Create a copy of UserAccountProvider
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserAccountProviderCopyWith<_UserAccountProvider> get copyWith =>
      __$UserAccountProviderCopyWithImpl<_UserAccountProvider>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$UserAccountProviderToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserAccountProvider &&
            (identical(other.providerId, providerId) ||
                other.providerId == providerId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.uid, uid) || other.uid == uid) &&
            (identical(other.avatar, avatar) || other.avatar == avatar));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    providerId,
    username,
    displayName,
    email,
    uid,
    avatar,
  );

  @override
  String toString() {
    return 'UserAccountProvider(providerId: $providerId, username: $username, displayName: $displayName, email: $email, uid: $uid, avatar: $avatar)';
  }
}

/// @nodoc
abstract mixin class _$UserAccountProviderCopyWith<$Res>
    implements $UserAccountProviderCopyWith<$Res> {
  factory _$UserAccountProviderCopyWith(
    _UserAccountProvider value,
    $Res Function(_UserAccountProvider) _then,
  ) = __$UserAccountProviderCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'providerId') String providerId,
    @JsonKey(name: 'username') String? username,
    @JsonKey(name: 'displayName') String? displayName,
    @JsonKey(name: 'email') String? email,
    @JsonKey(name: 'uid') String? uid,
    @JsonKey(name: 'avatar') String avatar,
  });
}

/// @nodoc
class __$UserAccountProviderCopyWithImpl<$Res>
    implements _$UserAccountProviderCopyWith<$Res> {
  __$UserAccountProviderCopyWithImpl(this._self, this._then);

  final _UserAccountProvider _self;
  final $Res Function(_UserAccountProvider) _then;

  /// Create a copy of UserAccountProvider
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? providerId = null,
    Object? username = freezed,
    Object? displayName = freezed,
    Object? email = freezed,
    Object? uid = freezed,
    Object? avatar = null,
  }) {
    return _then(
      _UserAccountProvider(
        providerId: null == providerId
            ? _self.providerId
            : providerId // ignore: cast_nullable_to_non_nullable
                  as String,
        username: freezed == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String?,
        displayName: freezed == displayName
            ? _self.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String?,
        email: freezed == email
            ? _self.email
            : email // ignore: cast_nullable_to_non_nullable
                  as String?,
        uid: freezed == uid
            ? _self.uid
            : uid // ignore: cast_nullable_to_non_nullable
                  as String?,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$UserSettingsRequest {
  @JsonKey(name: 'fcmAndroid')
  String? get fcmAndroid;
  @JsonKey(name: 'fcmIos')
  String? get fcmIOS;

  /// Create a copy of UserSettingsRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserSettingsRequestCopyWith<UserSettingsRequest> get copyWith =>
      _$UserSettingsRequestCopyWithImpl<UserSettingsRequest>(
        this as UserSettingsRequest,
        _$identity,
      );

  /// Serializes this UserSettingsRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserSettingsRequest &&
            (identical(other.fcmAndroid, fcmAndroid) ||
                other.fcmAndroid == fcmAndroid) &&
            (identical(other.fcmIOS, fcmIOS) || other.fcmIOS == fcmIOS));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, fcmAndroid, fcmIOS);

  @override
  String toString() {
    return 'UserSettingsRequest(fcmAndroid: $fcmAndroid, fcmIOS: $fcmIOS)';
  }
}

/// @nodoc
abstract mixin class $UserSettingsRequestCopyWith<$Res> {
  factory $UserSettingsRequestCopyWith(
    UserSettingsRequest value,
    $Res Function(UserSettingsRequest) _then,
  ) = _$UserSettingsRequestCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'fcmAndroid') String? fcmAndroid,
    @JsonKey(name: 'fcmIos') String? fcmIOS,
  });
}

/// @nodoc
class _$UserSettingsRequestCopyWithImpl<$Res>
    implements $UserSettingsRequestCopyWith<$Res> {
  _$UserSettingsRequestCopyWithImpl(this._self, this._then);

  final UserSettingsRequest _self;
  final $Res Function(UserSettingsRequest) _then;

  /// Create a copy of UserSettingsRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? fcmAndroid = freezed, Object? fcmIOS = freezed}) {
    return _then(
      _self.copyWith(
        fcmAndroid: freezed == fcmAndroid
            ? _self.fcmAndroid
            : fcmAndroid // ignore: cast_nullable_to_non_nullable
                  as String?,
        fcmIOS: freezed == fcmIOS
            ? _self.fcmIOS
            : fcmIOS // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _UserSettingsRequest implements UserSettingsRequest {
  const _UserSettingsRequest({
    @JsonKey(name: 'fcmAndroid') this.fcmAndroid,
    @JsonKey(name: 'fcmIos') this.fcmIOS,
  });
  factory _UserSettingsRequest.fromJson(Map<String, dynamic> json) =>
      _$UserSettingsRequestFromJson(json);

  @override
  @JsonKey(name: 'fcmAndroid')
  final String? fcmAndroid;
  @override
  @JsonKey(name: 'fcmIos')
  final String? fcmIOS;

  /// Create a copy of UserSettingsRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserSettingsRequestCopyWith<_UserSettingsRequest> get copyWith =>
      __$UserSettingsRequestCopyWithImpl<_UserSettingsRequest>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$UserSettingsRequestToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserSettingsRequest &&
            (identical(other.fcmAndroid, fcmAndroid) ||
                other.fcmAndroid == fcmAndroid) &&
            (identical(other.fcmIOS, fcmIOS) || other.fcmIOS == fcmIOS));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, fcmAndroid, fcmIOS);

  @override
  String toString() {
    return 'UserSettingsRequest(fcmAndroid: $fcmAndroid, fcmIOS: $fcmIOS)';
  }
}

/// @nodoc
abstract mixin class _$UserSettingsRequestCopyWith<$Res>
    implements $UserSettingsRequestCopyWith<$Res> {
  factory _$UserSettingsRequestCopyWith(
    _UserSettingsRequest value,
    $Res Function(_UserSettingsRequest) _then,
  ) = __$UserSettingsRequestCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'fcmAndroid') String? fcmAndroid,
    @JsonKey(name: 'fcmIos') String? fcmIOS,
  });
}

/// @nodoc
class __$UserSettingsRequestCopyWithImpl<$Res>
    implements _$UserSettingsRequestCopyWith<$Res> {
  __$UserSettingsRequestCopyWithImpl(this._self, this._then);

  final _UserSettingsRequest _self;
  final $Res Function(_UserSettingsRequest) _then;

  /// Create a copy of UserSettingsRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? fcmAndroid = freezed, Object? fcmIOS = freezed}) {
    return _then(
      _UserSettingsRequest(
        fcmAndroid: freezed == fcmAndroid
            ? _self.fcmAndroid
            : fcmAndroid // ignore: cast_nullable_to_non_nullable
                  as String?,
        fcmIOS: freezed == fcmIOS
            ? _self.fcmIOS
            : fcmIOS // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}
