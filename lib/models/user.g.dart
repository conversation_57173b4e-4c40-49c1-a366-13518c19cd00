// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserInfo _$UserInfoFromJson(Map json) => _UserInfo(
  userId: const JsonUserIdConverter().fromJson(json['uniqId'] as String),
  username: json['username'] as String,
  displayName: json['displayName'] as String? ?? '',
  avatar: json['avatar'] as String? ?? '',
  socialAccounts:
      (json['socialAccounts'] as Map?)?.map(
        (k, e) => MapEntry(
          k as String,
          UserSocialAccount.fromJson(Map<String, dynamic>.from(e as Map)),
        ),
      ) ??
      const {},
  followersCount: (json['followersCount'] as num?)?.toInt() ?? 0,
  followingCount: (json['followingCount'] as num?)?.toInt() ?? 0,
  following: json['following'] as bool? ?? false,
  followedBy: json['followedBy'] as bool? ?? false,
);

Map<String, dynamic> _$UserInfoToJson(_UserInfo instance) => <String, dynamic>{
  'uniqId': const JsonUserIdConverter().toJson(instance.userId),
  'username': instance.username,
  'displayName': instance.displayName,
  'avatar': instance.avatar,
  'socialAccounts': instance.socialAccounts.map(
    (k, e) => MapEntry(k, e.toJson()),
  ),
  'followersCount': instance.followersCount,
  'followingCount': instance.followingCount,
  'following': instance.following,
  'followedBy': instance.followedBy,
};

_PersistentUserInfo _$PersistentUserInfoFromJson(Map json) =>
    _PersistentUserInfo(
      userId: const JsonUserIdConverter().fromJson(json['uniqId'] as String),
      username: json['username'] as String,
      displayName: json['displayName'] as String? ?? '',
      avatar: json['avatar'] as String? ?? '',
      walletBlocked: json['isWalletBlocked'] as bool? ?? false,
      activated: json['isActive'] as bool? ?? true,
      walletAddressesByChains: Map<String, String>.from(
        PersistentUserInfo._walletAddressReadValue(
              json,
              'walletAddressesByChains',
            )
            as Map,
      ),
      referCode: json['referCode'] as String? ?? '',
      referCount: (json['referCnt'] as num?)?.toInt() ?? 0,
      socialAccounts:
          (json['socialAccounts'] as Map?)?.map(
            (k, e) => MapEntry(
              k as String,
              UserSocialAccount.fromJson(Map<String, dynamic>.from(e as Map)),
            ),
          ) ??
          const {},
      followersCount: (json['followersCount'] as num?)?.toInt() ?? 0,
      followingCount: (json['followingCount'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$PersistentUserInfoToJson(_PersistentUserInfo instance) =>
    <String, dynamic>{
      'uniqId': const JsonUserIdConverter().toJson(instance.userId),
      'username': instance.username,
      'displayName': instance.displayName,
      'avatar': instance.avatar,
      'isWalletBlocked': instance.walletBlocked,
      'isActive': instance.activated,
      'walletAddressesByChains': instance.walletAddressesByChains,
      'referCode': instance.referCode,
      'referCnt': instance.referCount,
      'socialAccounts': instance.socialAccounts.map(
        (k, e) => MapEntry(k, e.toJson()),
      ),
      'followersCount': instance.followersCount,
      'followingCount': instance.followingCount,
    };

_UserSocialAccount _$UserSocialAccountFromJson(Map json) => _UserSocialAccount(
  platform: json['platform'] as String,
  username: json['username'] as String,
  displayName: json['displayName'] as String? ?? '',
  avatar: json['avatar'] as String? ?? '',
);

Map<String, dynamic> _$UserSocialAccountToJson(_UserSocialAccount instance) =>
    <String, dynamic>{
      'platform': instance.platform,
      'username': instance.username,
      'displayName': instance.displayName,
      'avatar': instance.avatar,
    };

_UserAccountProvider _$UserAccountProviderFromJson(Map json) =>
    _UserAccountProvider(
      providerId: json['providerId'] as String,
      username: json['username'] as String?,
      displayName: json['displayName'] as String?,
      email: json['email'] as String?,
      uid: json['uid'] as String?,
      avatar: json['avatar'] as String? ?? '',
    );

Map<String, dynamic> _$UserAccountProviderToJson(
  _UserAccountProvider instance,
) => <String, dynamic>{
  'providerId': instance.providerId,
  'username': instance.username,
  'displayName': instance.displayName,
  'email': instance.email,
  'uid': instance.uid,
  'avatar': instance.avatar,
};

_UserSettingsRequest _$UserSettingsRequestFromJson(Map json) =>
    _UserSettingsRequest(
      fcmAndroid: json['fcmAndroid'] as String?,
      fcmIOS: json['fcmIos'] as String?,
    );

Map<String, dynamic> _$UserSettingsRequestToJson(
  _UserSettingsRequest instance,
) => <String, dynamic>{
  'fcmAndroid': instance.fcmAndroid,
  'fcmIos': instance.fcmIOS,
};
