// ignore_for_file: unused_element

import 'dart:io' show Platform;

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:me_extensions/me_extensions.dart'
    show MENullableStringExtension;

import '_shared.dart';

part 'user.freezed.dart';

part 'user.g.dart';

@JsonEnum(valueField: 'value')
enum UserLoginMethod {
  guest('guest'),
  x('x'),
  apple('apple'),
  google('google'),
  email('email');

  const UserLoginMethod(this.value);

  factory UserLoginMethod.fromJson(String json) {
    return values.firstWhere((e) => e.value == json, orElse: () => x);
  }

  final String value;
}

abstract class UserInfoBase {
  const UserInfoBase({
    required this.userId,
    required this.username,
    required this.displayName,
    required this.avatar,
  });

  @JsonUserIdConverter()
  @<PERSON><PERSON><PERSON><PERSON>(name: 'uniqId')
  final int userId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'username')
  final String username;

  @Default('')
  @Json<PERSON>ey(name: 'displayName')
  final String displayName;

  @Default('')
  @<PERSON>son<PERSON>ey(name: 'avatar')
  final String avatar;

  Map<String, dynamic> toJson();

  String get rawUserId => userId.toRadixString(16);

  String get name => displayName.or(username);
}

mixin UserInfoBaseWithSocials on UserInfoBase {
  static const platformX = 'twitter';

  Map<String, UserSocialAccount> get socialAccounts;

  int get followersCount;

  int get followingCount;

  UserSocialAccount? socialAccountBy(String platform);
}

@freezed
sealed class UserInfo extends UserInfoBase
    with _$UserInfo
    implements UserInfoBaseWithSocials {
  @Implements<UserInfoBaseWithSocials>()
  const factory UserInfo({
    @JsonUserIdConverter() @JsonKey(name: 'uniqId') required int userId,
    @JsonKey(name: 'username') required String username,
    @JsonKey(name: 'displayName') @Default('') String displayName,
    @JsonKey(name: 'avatar') @Default('') String avatar,
    @JsonKey(name: 'socialAccounts')
    @Default({})
    Map<String, UserSocialAccount> socialAccounts,
    @JsonKey(name: 'followersCount') @Default(0) int followersCount,
    @JsonKey(name: 'followingCount') @Default(0) int followingCount,
    @JsonKey(name: 'following') @Default(false) bool following,
    @JsonKey(name: 'followedBy') @Default(false) bool followedBy,
  }) = _UserInfo;

  const UserInfo._({
    required super.userId,
    required super.username,
    super.displayName = '',
    super.avatar = '',
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) =>
      _$UserInfoFromJson(json);

  @override
  UserSocialAccount? socialAccountBy(String platform) =>
      socialAccounts[platform];
}

@freezed
sealed class PersistentUserInfo extends UserInfoBase
    with _$PersistentUserInfo
    implements UserInfoBaseWithSocials {
  @Implements<UserInfoBaseWithSocials>()
  const factory PersistentUserInfo({
    @JsonUserIdConverter() @JsonKey(name: 'uniqId') required int userId,
    @JsonKey(name: 'username') required String username,
    @JsonKey(name: 'displayName') @Default('') String displayName,
    @JsonKey(name: 'avatar') @Default('') String avatar,
    @JsonKey(name: 'isWalletBlocked') @Default(false) bool walletBlocked,
    @JsonKey(name: 'isActive') @Default(true) bool activated,
    @JsonKey(
      name: PersistentUserInfo._keyWalletAddressesByChains,
      readValue: PersistentUserInfo._walletAddressReadValue,
    )
    required Map<String, String> walletAddressesByChains,
    @JsonKey(name: 'referCode') @Default('') String referCode,
    @JsonKey(name: 'referCnt') @Default(0) int referCount,
    @JsonKey(name: 'socialAccounts')
    @Default({})
    Map<String, UserSocialAccount> socialAccounts,
    @JsonKey(name: 'followersCount') @Default(0) int followersCount,
    @JsonKey(name: 'followingCount') @Default(0) int followingCount,
  }) = _PersistentUserInfo;

  const PersistentUserInfo._({
    required super.userId,
    required super.username,
    super.displayName = '',
    super.avatar = '',
  });

  factory PersistentUserInfo.fromJson(Map<String, dynamic> json) =>
      _$PersistentUserInfoFromJson(json);

  static const _keyWalletAddressesByChains = 'walletAddressesByChains';
  static final _regexWalletAddresses = RegExp(r'^walletAddress(\S+)$');

  static Map<String, String> _walletAddressReadValue(Map json, String key) {
    if (json[_keyWalletAddressesByChains] case final Map map) {
      return map.cast();
    }
    final result = <String, String>{};
    for (final entry in json.entries) {
      final key = entry.key.toString();
      if (_regexWalletAddresses.hasMatch(key)) {
        final k = key.replaceAll('walletAddress', '').toLowerCase();
        result[k] = entry.value.toString();
      }
    }
    return result;
  }

  String walletAddressByChain(Chain chain) =>
      walletAddressesByChains[chain.name] ?? '';

  UserInfo toUserInfo() {
    return UserInfo(
      userId: userId,
      username: username,
      displayName: displayName,
      avatar: avatar,
    );
  }

  @override
  UserSocialAccount? socialAccountBy(String platform) =>
      socialAccounts[platform];
}

@freezed
sealed class UserSocialAccount with _$UserSocialAccount {
  const factory UserSocialAccount({
    @JsonKey(name: 'platform') required String platform,
    @JsonKey(name: 'username') required String username,
    @JsonKey(name: 'displayName') @Default('') String displayName,
    @JsonKey(name: 'avatar') @Default('') String avatar,
  }) = _UserSocialAccount;

  factory UserSocialAccount.fromJson(Map<String, dynamic> json) =>
      _$UserSocialAccountFromJson(json);
}

@freezed
sealed class UserAccountProvider with _$UserAccountProvider {
  const factory UserAccountProvider({
    @JsonKey(name: 'providerId') required String providerId,
    @JsonKey(name: 'username') String? username,
    @JsonKey(name: 'displayName') String? displayName,
    @JsonKey(name: 'email') String? email,
    @JsonKey(name: 'uid') String? uid,
    @JsonKey(name: 'avatar') @Default('') String avatar,
  }) = _UserAccountProvider;

  factory UserAccountProvider.fromJson(Map<String, dynamic> json) =>
      _$UserAccountProviderFromJson(json);
}

@freezed
sealed class UserSettingsRequest with _$UserSettingsRequest {
  const factory UserSettingsRequest({
    @JsonKey(name: 'fcmAndroid') String? fcmAndroid,
    @JsonKey(name: 'fcmIos') String? fcmIOS,
  }) = _UserSettingsRequest;

  factory UserSettingsRequest.fromJson(Map<String, dynamic> json) =>
      _$UserSettingsRequestFromJson(json);

  factory UserSettingsRequest.fromPlatform({required String fcm}) {
    return UserSettingsRequest(
      fcmAndroid: Platform.isAndroid ? fcm : null,
      fcmIOS: Platform.isIOS ? fcm : null,
    );
  }
}
