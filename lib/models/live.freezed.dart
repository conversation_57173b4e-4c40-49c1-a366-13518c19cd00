// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'live.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LiveRoomParticipation {
  @JsonKey(name: 'rtcToken')
  String get rtc;
  @JsonKey(name: 'rtmToken')
  String get rtm;
  @JsonKey(name: 'channel')
  LiveRoom? get room;

  /// Create a copy of LiveRoomParticipation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveRoomParticipationCopyWith<LiveRoomParticipation> get copyWith =>
      _$LiveRoomParticipationCopyWithImpl<LiveRoomParticipation>(
        this as LiveRoomParticipation,
        _$identity,
      );

  /// Serializes this LiveRoomParticipation to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveRoomParticipation &&
            (identical(other.rtc, rtc) || other.rtc == rtc) &&
            (identical(other.rtm, rtm) || other.rtm == rtm) &&
            (identical(other.room, room) || other.room == room));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, rtc, rtm, room);

  @override
  String toString() {
    return 'LiveRoomParticipation(rtc: $rtc, rtm: $rtm, room: $room)';
  }
}

/// @nodoc
abstract mixin class $LiveRoomParticipationCopyWith<$Res> {
  factory $LiveRoomParticipationCopyWith(
    LiveRoomParticipation value,
    $Res Function(LiveRoomParticipation) _then,
  ) = _$LiveRoomParticipationCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'rtcToken') String rtc,
    @JsonKey(name: 'rtmToken') String rtm,
    @JsonKey(name: 'channel') LiveRoom? room,
  });

  $LiveRoomCopyWith<$Res>? get room;
}

/// @nodoc
class _$LiveRoomParticipationCopyWithImpl<$Res>
    implements $LiveRoomParticipationCopyWith<$Res> {
  _$LiveRoomParticipationCopyWithImpl(this._self, this._then);

  final LiveRoomParticipation _self;
  final $Res Function(LiveRoomParticipation) _then;

  /// Create a copy of LiveRoomParticipation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? rtc = null, Object? rtm = null, Object? room = freezed}) {
    return _then(
      _self.copyWith(
        rtc: null == rtc
            ? _self.rtc
            : rtc // ignore: cast_nullable_to_non_nullable
                  as String,
        rtm: null == rtm
            ? _self.rtm
            : rtm // ignore: cast_nullable_to_non_nullable
                  as String,
        room: freezed == room
            ? _self.room
            : room // ignore: cast_nullable_to_non_nullable
                  as LiveRoom?,
      ),
    );
  }

  /// Create a copy of LiveRoomParticipation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LiveRoomCopyWith<$Res>? get room {
    if (_self.room == null) {
      return null;
    }

    return $LiveRoomCopyWith<$Res>(_self.room!, (value) {
      return _then(_self.copyWith(room: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _LiveRoomParticipation implements LiveRoomParticipation {
  const _LiveRoomParticipation({
    @JsonKey(name: 'rtcToken') required this.rtc,
    @JsonKey(name: 'rtmToken') required this.rtm,
    @JsonKey(name: 'channel') this.room,
  });
  factory _LiveRoomParticipation.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomParticipationFromJson(json);

  @override
  @JsonKey(name: 'rtcToken')
  final String rtc;
  @override
  @JsonKey(name: 'rtmToken')
  final String rtm;
  @override
  @JsonKey(name: 'channel')
  final LiveRoom? room;

  /// Create a copy of LiveRoomParticipation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LiveRoomParticipationCopyWith<_LiveRoomParticipation> get copyWith =>
      __$LiveRoomParticipationCopyWithImpl<_LiveRoomParticipation>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$LiveRoomParticipationToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LiveRoomParticipation &&
            (identical(other.rtc, rtc) || other.rtc == rtc) &&
            (identical(other.rtm, rtm) || other.rtm == rtm) &&
            (identical(other.room, room) || other.room == room));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, rtc, rtm, room);

  @override
  String toString() {
    return 'LiveRoomParticipation(rtc: $rtc, rtm: $rtm, room: $room)';
  }
}

/// @nodoc
abstract mixin class _$LiveRoomParticipationCopyWith<$Res>
    implements $LiveRoomParticipationCopyWith<$Res> {
  factory _$LiveRoomParticipationCopyWith(
    _LiveRoomParticipation value,
    $Res Function(_LiveRoomParticipation) _then,
  ) = __$LiveRoomParticipationCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'rtcToken') String rtc,
    @JsonKey(name: 'rtmToken') String rtm,
    @JsonKey(name: 'channel') LiveRoom? room,
  });

  @override
  $LiveRoomCopyWith<$Res>? get room;
}

/// @nodoc
class __$LiveRoomParticipationCopyWithImpl<$Res>
    implements _$LiveRoomParticipationCopyWith<$Res> {
  __$LiveRoomParticipationCopyWithImpl(this._self, this._then);

  final _LiveRoomParticipation _self;
  final $Res Function(_LiveRoomParticipation) _then;

  /// Create a copy of LiveRoomParticipation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? rtc = null, Object? rtm = null, Object? room = freezed}) {
    return _then(
      _LiveRoomParticipation(
        rtc: null == rtc
            ? _self.rtc
            : rtc // ignore: cast_nullable_to_non_nullable
                  as String,
        rtm: null == rtm
            ? _self.rtm
            : rtm // ignore: cast_nullable_to_non_nullable
                  as String,
        room: freezed == room
            ? _self.room
            : room // ignore: cast_nullable_to_non_nullable
                  as LiveRoom?,
      ),
    );
  }

  /// Create a copy of LiveRoomParticipation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LiveRoomCopyWith<$Res>? get room {
    if (_self.room == null) {
      return null;
    }

    return $LiveRoomCopyWith<$Res>(_self.room!, (value) {
      return _then(_self.copyWith(room: value));
    });
  }
}

/// @nodoc
mixin _$LiveRoom {
  @JsonKey(name: 'isPrivate')
  bool get private;
  @JsonKey(name: 'name')
  String get id;
  @JsonKey(name: 'displayName')
  String get displayName;
  @JsonKey(name: 'tokenAddress')
  String? get tokenMintAddress;
  @JsonKey(name: 'tokenChain')
  String? get tokenChain;
  @JsonKey(name: 'tokenLogo')
  String? get tokenLogo;
  @JsonKey(name: 'tokenName')
  String? get tokenName;
  @JsonKey(name: 'tokenSymbol')
  String? get tokenSymbol;
  @JsonKey(name: 'groupId')
  String? get rawGroupId;
  @JsonKey(name: 'group')
  LiveRoomGroup? get group;
  @JsonKey(name: 'username')
  String get username;
  @JsonKey(name: 'userDisplayName')
  String get userDisplayName;
  @JsonKey(name: 'userAvatar')
  String get userAvatar;
  @JsonKey(name: 'userCount')
  int get userCount;
  @JsonUserIdConverter()
  @JsonKey(name: 'userUniqId')
  int get userId;
  @JsonKey(name: 'broadcasters')
  List<UserInfo> get broadcasters;
  @JsonUserIdsConverter()
  @JsonKey(name: 'broadcasterIds')
  List<int> get broadcasterIds;
  @EpochDateTimeConverter()
  @JsonKey(name: 'openTime')
  DateTime get openTime;
  @EpochDateTimeNullableConverter()
  @JsonKey(name: 'closeTime')
  DateTime? get closeTime;
  @JsonKey(name: 'chatEnabled')
  bool get chatEnabled;

  /// Create a copy of LiveRoom
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveRoomCopyWith<LiveRoom> get copyWith =>
      _$LiveRoomCopyWithImpl<LiveRoom>(this as LiveRoom, _$identity);

  /// Serializes this LiveRoom to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveRoom &&
            (identical(other.private, private) || other.private == private) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.tokenMintAddress, tokenMintAddress) ||
                other.tokenMintAddress == tokenMintAddress) &&
            (identical(other.tokenChain, tokenChain) ||
                other.tokenChain == tokenChain) &&
            (identical(other.tokenLogo, tokenLogo) ||
                other.tokenLogo == tokenLogo) &&
            (identical(other.tokenName, tokenName) ||
                other.tokenName == tokenName) &&
            (identical(other.tokenSymbol, tokenSymbol) ||
                other.tokenSymbol == tokenSymbol) &&
            (identical(other.rawGroupId, rawGroupId) ||
                other.rawGroupId == rawGroupId) &&
            (identical(other.group, group) || other.group == group) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userDisplayName, userDisplayName) ||
                other.userDisplayName == userDisplayName) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.userCount, userCount) ||
                other.userCount == userCount) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            const DeepCollectionEquality().equals(
              other.broadcasters,
              broadcasters,
            ) &&
            const DeepCollectionEquality().equals(
              other.broadcasterIds,
              broadcasterIds,
            ) &&
            (identical(other.openTime, openTime) ||
                other.openTime == openTime) &&
            (identical(other.closeTime, closeTime) ||
                other.closeTime == closeTime) &&
            (identical(other.chatEnabled, chatEnabled) ||
                other.chatEnabled == chatEnabled));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    private,
    id,
    displayName,
    tokenMintAddress,
    tokenChain,
    tokenLogo,
    tokenName,
    tokenSymbol,
    rawGroupId,
    group,
    username,
    userDisplayName,
    userAvatar,
    userCount,
    userId,
    const DeepCollectionEquality().hash(broadcasters),
    const DeepCollectionEquality().hash(broadcasterIds),
    openTime,
    closeTime,
    chatEnabled,
  ]);

  @override
  String toString() {
    return 'LiveRoom(private: $private, id: $id, displayName: $displayName, tokenMintAddress: $tokenMintAddress, tokenChain: $tokenChain, tokenLogo: $tokenLogo, tokenName: $tokenName, tokenSymbol: $tokenSymbol, rawGroupId: $rawGroupId, group: $group, username: $username, userDisplayName: $userDisplayName, userAvatar: $userAvatar, userCount: $userCount, userId: $userId, broadcasters: $broadcasters, broadcasterIds: $broadcasterIds, openTime: $openTime, closeTime: $closeTime, chatEnabled: $chatEnabled)';
  }
}

/// @nodoc
abstract mixin class $LiveRoomCopyWith<$Res> {
  factory $LiveRoomCopyWith(LiveRoom value, $Res Function(LiveRoom) _then) =
      _$LiveRoomCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'isPrivate') bool private,
    @JsonKey(name: 'name') String id,
    @JsonKey(name: 'displayName') String displayName,
    @JsonKey(name: 'tokenAddress') String? tokenMintAddress,
    @JsonKey(name: 'tokenChain') String? tokenChain,
    @JsonKey(name: 'tokenLogo') String? tokenLogo,
    @JsonKey(name: 'tokenName') String? tokenName,
    @JsonKey(name: 'tokenSymbol') String? tokenSymbol,
    @JsonKey(name: 'groupId') String? rawGroupId,
    @JsonKey(name: 'group') LiveRoomGroup? group,
    @JsonKey(name: 'username') String username,
    @JsonKey(name: 'userDisplayName') String userDisplayName,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'userCount') int userCount,
    @JsonUserIdConverter() @JsonKey(name: 'userUniqId') int userId,
    @JsonKey(name: 'broadcasters') List<UserInfo> broadcasters,
    @JsonUserIdsConverter()
    @JsonKey(name: 'broadcasterIds')
    List<int> broadcasterIds,
    @EpochDateTimeConverter() @JsonKey(name: 'openTime') DateTime openTime,
    @EpochDateTimeNullableConverter()
    @JsonKey(name: 'closeTime')
    DateTime? closeTime,
    @JsonKey(name: 'chatEnabled') bool chatEnabled,
  });

  $LiveRoomGroupCopyWith<$Res>? get group;
}

/// @nodoc
class _$LiveRoomCopyWithImpl<$Res> implements $LiveRoomCopyWith<$Res> {
  _$LiveRoomCopyWithImpl(this._self, this._then);

  final LiveRoom _self;
  final $Res Function(LiveRoom) _then;

  /// Create a copy of LiveRoom
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? private = null,
    Object? id = null,
    Object? displayName = null,
    Object? tokenMintAddress = freezed,
    Object? tokenChain = freezed,
    Object? tokenLogo = freezed,
    Object? tokenName = freezed,
    Object? tokenSymbol = freezed,
    Object? rawGroupId = freezed,
    Object? group = freezed,
    Object? username = null,
    Object? userDisplayName = null,
    Object? userAvatar = null,
    Object? userCount = null,
    Object? userId = null,
    Object? broadcasters = null,
    Object? broadcasterIds = null,
    Object? openTime = null,
    Object? closeTime = freezed,
    Object? chatEnabled = null,
  }) {
    return _then(
      _self.copyWith(
        private: null == private
            ? _self.private
            : private // ignore: cast_nullable_to_non_nullable
                  as bool,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        displayName: null == displayName
            ? _self.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String,
        tokenMintAddress: freezed == tokenMintAddress
            ? _self.tokenMintAddress
            : tokenMintAddress // ignore: cast_nullable_to_non_nullable
                  as String?,
        tokenChain: freezed == tokenChain
            ? _self.tokenChain
            : tokenChain // ignore: cast_nullable_to_non_nullable
                  as String?,
        tokenLogo: freezed == tokenLogo
            ? _self.tokenLogo
            : tokenLogo // ignore: cast_nullable_to_non_nullable
                  as String?,
        tokenName: freezed == tokenName
            ? _self.tokenName
            : tokenName // ignore: cast_nullable_to_non_nullable
                  as String?,
        tokenSymbol: freezed == tokenSymbol
            ? _self.tokenSymbol
            : tokenSymbol // ignore: cast_nullable_to_non_nullable
                  as String?,
        rawGroupId: freezed == rawGroupId
            ? _self.rawGroupId
            : rawGroupId // ignore: cast_nullable_to_non_nullable
                  as String?,
        group: freezed == group
            ? _self.group
            : group // ignore: cast_nullable_to_non_nullable
                  as LiveRoomGroup?,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userDisplayName: null == userDisplayName
            ? _self.userDisplayName
            : userDisplayName // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        userCount: null == userCount
            ? _self.userCount
            : userCount // ignore: cast_nullable_to_non_nullable
                  as int,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        broadcasters: null == broadcasters
            ? _self.broadcasters
            : broadcasters // ignore: cast_nullable_to_non_nullable
                  as List<UserInfo>,
        broadcasterIds: null == broadcasterIds
            ? _self.broadcasterIds
            : broadcasterIds // ignore: cast_nullable_to_non_nullable
                  as List<int>,
        openTime: null == openTime
            ? _self.openTime
            : openTime // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        closeTime: freezed == closeTime
            ? _self.closeTime
            : closeTime // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        chatEnabled: null == chatEnabled
            ? _self.chatEnabled
            : chatEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }

  /// Create a copy of LiveRoom
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LiveRoomGroupCopyWith<$Res>? get group {
    if (_self.group == null) {
      return null;
    }

    return $LiveRoomGroupCopyWith<$Res>(_self.group!, (value) {
      return _then(_self.copyWith(group: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _LiveRoom extends LiveRoom {
  const _LiveRoom({
    @JsonKey(name: 'isPrivate') this.private = false,
    @JsonKey(name: 'name') required this.id,
    @JsonKey(name: 'displayName') required this.displayName,
    @JsonKey(name: 'tokenAddress') this.tokenMintAddress,
    @JsonKey(name: 'tokenChain') this.tokenChain,
    @JsonKey(name: 'tokenLogo') this.tokenLogo,
    @JsonKey(name: 'tokenName') this.tokenName,
    @JsonKey(name: 'tokenSymbol') this.tokenSymbol,
    @JsonKey(name: 'groupId') this.rawGroupId,
    @JsonKey(name: 'group') this.group,
    @JsonKey(name: 'username') required this.username,
    @JsonKey(name: 'userDisplayName') this.userDisplayName = '',
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    @JsonKey(name: 'userCount') this.userCount = 0,
    @JsonUserIdConverter() @JsonKey(name: 'userUniqId') this.userId = 0,
    @JsonKey(name: 'broadcasters') final List<UserInfo> broadcasters = const [],
    @JsonUserIdsConverter()
    @JsonKey(name: 'broadcasterIds')
    final List<int> broadcasterIds = const [],
    @EpochDateTimeConverter() @JsonKey(name: 'openTime') required this.openTime,
    @EpochDateTimeNullableConverter()
    @JsonKey(name: 'closeTime')
    this.closeTime,
    @JsonKey(name: 'chatEnabled') this.chatEnabled = true,
  }) : _broadcasters = broadcasters,
       _broadcasterIds = broadcasterIds,
       super._();
  factory _LiveRoom.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomFromJson(json);

  @override
  @JsonKey(name: 'isPrivate')
  final bool private;
  @override
  @JsonKey(name: 'name')
  final String id;
  @override
  @JsonKey(name: 'displayName')
  final String displayName;
  @override
  @JsonKey(name: 'tokenAddress')
  final String? tokenMintAddress;
  @override
  @JsonKey(name: 'tokenChain')
  final String? tokenChain;
  @override
  @JsonKey(name: 'tokenLogo')
  final String? tokenLogo;
  @override
  @JsonKey(name: 'tokenName')
  final String? tokenName;
  @override
  @JsonKey(name: 'tokenSymbol')
  final String? tokenSymbol;
  @override
  @JsonKey(name: 'groupId')
  final String? rawGroupId;
  @override
  @JsonKey(name: 'group')
  final LiveRoomGroup? group;
  @override
  @JsonKey(name: 'username')
  final String username;
  @override
  @JsonKey(name: 'userDisplayName')
  final String userDisplayName;
  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;
  @override
  @JsonKey(name: 'userCount')
  final int userCount;
  @override
  @JsonUserIdConverter()
  @JsonKey(name: 'userUniqId')
  final int userId;
  final List<UserInfo> _broadcasters;
  @override
  @JsonKey(name: 'broadcasters')
  List<UserInfo> get broadcasters {
    if (_broadcasters is EqualUnmodifiableListView) return _broadcasters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_broadcasters);
  }

  final List<int> _broadcasterIds;
  @override
  @JsonUserIdsConverter()
  @JsonKey(name: 'broadcasterIds')
  List<int> get broadcasterIds {
    if (_broadcasterIds is EqualUnmodifiableListView) return _broadcasterIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_broadcasterIds);
  }

  @override
  @EpochDateTimeConverter()
  @JsonKey(name: 'openTime')
  final DateTime openTime;
  @override
  @EpochDateTimeNullableConverter()
  @JsonKey(name: 'closeTime')
  final DateTime? closeTime;
  @override
  @JsonKey(name: 'chatEnabled')
  final bool chatEnabled;

  /// Create a copy of LiveRoom
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LiveRoomCopyWith<_LiveRoom> get copyWith =>
      __$LiveRoomCopyWithImpl<_LiveRoom>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveRoomToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LiveRoom &&
            (identical(other.private, private) || other.private == private) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.tokenMintAddress, tokenMintAddress) ||
                other.tokenMintAddress == tokenMintAddress) &&
            (identical(other.tokenChain, tokenChain) ||
                other.tokenChain == tokenChain) &&
            (identical(other.tokenLogo, tokenLogo) ||
                other.tokenLogo == tokenLogo) &&
            (identical(other.tokenName, tokenName) ||
                other.tokenName == tokenName) &&
            (identical(other.tokenSymbol, tokenSymbol) ||
                other.tokenSymbol == tokenSymbol) &&
            (identical(other.rawGroupId, rawGroupId) ||
                other.rawGroupId == rawGroupId) &&
            (identical(other.group, group) || other.group == group) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userDisplayName, userDisplayName) ||
                other.userDisplayName == userDisplayName) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.userCount, userCount) ||
                other.userCount == userCount) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            const DeepCollectionEquality().equals(
              other._broadcasters,
              _broadcasters,
            ) &&
            const DeepCollectionEquality().equals(
              other._broadcasterIds,
              _broadcasterIds,
            ) &&
            (identical(other.openTime, openTime) ||
                other.openTime == openTime) &&
            (identical(other.closeTime, closeTime) ||
                other.closeTime == closeTime) &&
            (identical(other.chatEnabled, chatEnabled) ||
                other.chatEnabled == chatEnabled));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
    runtimeType,
    private,
    id,
    displayName,
    tokenMintAddress,
    tokenChain,
    tokenLogo,
    tokenName,
    tokenSymbol,
    rawGroupId,
    group,
    username,
    userDisplayName,
    userAvatar,
    userCount,
    userId,
    const DeepCollectionEquality().hash(_broadcasters),
    const DeepCollectionEquality().hash(_broadcasterIds),
    openTime,
    closeTime,
    chatEnabled,
  ]);

  @override
  String toString() {
    return 'LiveRoom(private: $private, id: $id, displayName: $displayName, tokenMintAddress: $tokenMintAddress, tokenChain: $tokenChain, tokenLogo: $tokenLogo, tokenName: $tokenName, tokenSymbol: $tokenSymbol, rawGroupId: $rawGroupId, group: $group, username: $username, userDisplayName: $userDisplayName, userAvatar: $userAvatar, userCount: $userCount, userId: $userId, broadcasters: $broadcasters, broadcasterIds: $broadcasterIds, openTime: $openTime, closeTime: $closeTime, chatEnabled: $chatEnabled)';
  }
}

/// @nodoc
abstract mixin class _$LiveRoomCopyWith<$Res>
    implements $LiveRoomCopyWith<$Res> {
  factory _$LiveRoomCopyWith(_LiveRoom value, $Res Function(_LiveRoom) _then) =
      __$LiveRoomCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'isPrivate') bool private,
    @JsonKey(name: 'name') String id,
    @JsonKey(name: 'displayName') String displayName,
    @JsonKey(name: 'tokenAddress') String? tokenMintAddress,
    @JsonKey(name: 'tokenChain') String? tokenChain,
    @JsonKey(name: 'tokenLogo') String? tokenLogo,
    @JsonKey(name: 'tokenName') String? tokenName,
    @JsonKey(name: 'tokenSymbol') String? tokenSymbol,
    @JsonKey(name: 'groupId') String? rawGroupId,
    @JsonKey(name: 'group') LiveRoomGroup? group,
    @JsonKey(name: 'username') String username,
    @JsonKey(name: 'userDisplayName') String userDisplayName,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'userCount') int userCount,
    @JsonUserIdConverter() @JsonKey(name: 'userUniqId') int userId,
    @JsonKey(name: 'broadcasters') List<UserInfo> broadcasters,
    @JsonUserIdsConverter()
    @JsonKey(name: 'broadcasterIds')
    List<int> broadcasterIds,
    @EpochDateTimeConverter() @JsonKey(name: 'openTime') DateTime openTime,
    @EpochDateTimeNullableConverter()
    @JsonKey(name: 'closeTime')
    DateTime? closeTime,
    @JsonKey(name: 'chatEnabled') bool chatEnabled,
  });

  @override
  $LiveRoomGroupCopyWith<$Res>? get group;
}

/// @nodoc
class __$LiveRoomCopyWithImpl<$Res> implements _$LiveRoomCopyWith<$Res> {
  __$LiveRoomCopyWithImpl(this._self, this._then);

  final _LiveRoom _self;
  final $Res Function(_LiveRoom) _then;

  /// Create a copy of LiveRoom
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? private = null,
    Object? id = null,
    Object? displayName = null,
    Object? tokenMintAddress = freezed,
    Object? tokenChain = freezed,
    Object? tokenLogo = freezed,
    Object? tokenName = freezed,
    Object? tokenSymbol = freezed,
    Object? rawGroupId = freezed,
    Object? group = freezed,
    Object? username = null,
    Object? userDisplayName = null,
    Object? userAvatar = null,
    Object? userCount = null,
    Object? userId = null,
    Object? broadcasters = null,
    Object? broadcasterIds = null,
    Object? openTime = null,
    Object? closeTime = freezed,
    Object? chatEnabled = null,
  }) {
    return _then(
      _LiveRoom(
        private: null == private
            ? _self.private
            : private // ignore: cast_nullable_to_non_nullable
                  as bool,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        displayName: null == displayName
            ? _self.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String,
        tokenMintAddress: freezed == tokenMintAddress
            ? _self.tokenMintAddress
            : tokenMintAddress // ignore: cast_nullable_to_non_nullable
                  as String?,
        tokenChain: freezed == tokenChain
            ? _self.tokenChain
            : tokenChain // ignore: cast_nullable_to_non_nullable
                  as String?,
        tokenLogo: freezed == tokenLogo
            ? _self.tokenLogo
            : tokenLogo // ignore: cast_nullable_to_non_nullable
                  as String?,
        tokenName: freezed == tokenName
            ? _self.tokenName
            : tokenName // ignore: cast_nullable_to_non_nullable
                  as String?,
        tokenSymbol: freezed == tokenSymbol
            ? _self.tokenSymbol
            : tokenSymbol // ignore: cast_nullable_to_non_nullable
                  as String?,
        rawGroupId: freezed == rawGroupId
            ? _self.rawGroupId
            : rawGroupId // ignore: cast_nullable_to_non_nullable
                  as String?,
        group: freezed == group
            ? _self.group
            : group // ignore: cast_nullable_to_non_nullable
                  as LiveRoomGroup?,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userDisplayName: null == userDisplayName
            ? _self.userDisplayName
            : userDisplayName // ignore: cast_nullable_to_non_nullable
                  as String,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        userCount: null == userCount
            ? _self.userCount
            : userCount // ignore: cast_nullable_to_non_nullable
                  as int,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        broadcasters: null == broadcasters
            ? _self._broadcasters
            : broadcasters // ignore: cast_nullable_to_non_nullable
                  as List<UserInfo>,
        broadcasterIds: null == broadcasterIds
            ? _self._broadcasterIds
            : broadcasterIds // ignore: cast_nullable_to_non_nullable
                  as List<int>,
        openTime: null == openTime
            ? _self.openTime
            : openTime // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        closeTime: freezed == closeTime
            ? _self.closeTime
            : closeTime // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        chatEnabled: null == chatEnabled
            ? _self.chatEnabled
            : chatEnabled // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }

  /// Create a copy of LiveRoom
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LiveRoomGroupCopyWith<$Res>? get group {
    if (_self.group == null) {
      return null;
    }

    return $LiveRoomGroupCopyWith<$Res>(_self.group!, (value) {
      return _then(_self.copyWith(group: value));
    });
  }
}

/// @nodoc
mixin _$LiveRoomGroup {
  @JsonKey(name: 'uniqId')
  String get id;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'description')
  String get description;
  @JsonKey(name: 'logo')
  String get logo;
  @JsonKey(name: 'isOpen')
  bool get opening;
  @JsonKey(name: 'isJoined')
  bool get isJoined;
  @JsonKey(name: 'hasApplied')
  bool get hasApplied;
  @JsonKey(name: 'needConfirm')
  bool get needConfirm;

  /// Create a copy of LiveRoomGroup
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveRoomGroupCopyWith<LiveRoomGroup> get copyWith =>
      _$LiveRoomGroupCopyWithImpl<LiveRoomGroup>(
        this as LiveRoomGroup,
        _$identity,
      );

  /// Serializes this LiveRoomGroup to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveRoomGroup &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.opening, opening) || other.opening == opening) &&
            (identical(other.isJoined, isJoined) ||
                other.isJoined == isJoined) &&
            (identical(other.hasApplied, hasApplied) ||
                other.hasApplied == hasApplied) &&
            (identical(other.needConfirm, needConfirm) ||
                other.needConfirm == needConfirm));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    logo,
    opening,
    isJoined,
    hasApplied,
    needConfirm,
  );

  @override
  String toString() {
    return 'LiveRoomGroup(id: $id, name: $name, description: $description, logo: $logo, opening: $opening, isJoined: $isJoined, hasApplied: $hasApplied, needConfirm: $needConfirm)';
  }
}

/// @nodoc
abstract mixin class $LiveRoomGroupCopyWith<$Res> {
  factory $LiveRoomGroupCopyWith(
    LiveRoomGroup value,
    $Res Function(LiveRoomGroup) _then,
  ) = _$LiveRoomGroupCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'uniqId') String id,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'logo') String logo,
    @JsonKey(name: 'isOpen') bool opening,
    @JsonKey(name: 'isJoined') bool isJoined,
    @JsonKey(name: 'hasApplied') bool hasApplied,
    @JsonKey(name: 'needConfirm') bool needConfirm,
  });
}

/// @nodoc
class _$LiveRoomGroupCopyWithImpl<$Res>
    implements $LiveRoomGroupCopyWith<$Res> {
  _$LiveRoomGroupCopyWithImpl(this._self, this._then);

  final LiveRoomGroup _self;
  final $Res Function(LiveRoomGroup) _then;

  /// Create a copy of LiveRoomGroup
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? logo = null,
    Object? opening = null,
    Object? isJoined = null,
    Object? hasApplied = null,
    Object? needConfirm = null,
  }) {
    return _then(
      _self.copyWith(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        logo: null == logo
            ? _self.logo
            : logo // ignore: cast_nullable_to_non_nullable
                  as String,
        opening: null == opening
            ? _self.opening
            : opening // ignore: cast_nullable_to_non_nullable
                  as bool,
        isJoined: null == isJoined
            ? _self.isJoined
            : isJoined // ignore: cast_nullable_to_non_nullable
                  as bool,
        hasApplied: null == hasApplied
            ? _self.hasApplied
            : hasApplied // ignore: cast_nullable_to_non_nullable
                  as bool,
        needConfirm: null == needConfirm
            ? _self.needConfirm
            : needConfirm // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _LiveRoomGroup implements LiveRoomGroup {
  const _LiveRoomGroup({
    @JsonKey(name: 'uniqId') required this.id,
    @JsonKey(name: 'name') required this.name,
    @JsonKey(name: 'description') required this.description,
    @JsonKey(name: 'logo') this.logo = '',
    @JsonKey(name: 'isOpen') this.opening = true,
    @JsonKey(name: 'isJoined') this.isJoined = true,
    @JsonKey(name: 'hasApplied') this.hasApplied = false,
    @JsonKey(name: 'needConfirm') this.needConfirm = false,
  });
  factory _LiveRoomGroup.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomGroupFromJson(json);

  @override
  @JsonKey(name: 'uniqId')
  final String id;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'description')
  final String description;
  @override
  @JsonKey(name: 'logo')
  final String logo;
  @override
  @JsonKey(name: 'isOpen')
  final bool opening;
  @override
  @JsonKey(name: 'isJoined')
  final bool isJoined;
  @override
  @JsonKey(name: 'hasApplied')
  final bool hasApplied;
  @override
  @JsonKey(name: 'needConfirm')
  final bool needConfirm;

  /// Create a copy of LiveRoomGroup
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LiveRoomGroupCopyWith<_LiveRoomGroup> get copyWith =>
      __$LiveRoomGroupCopyWithImpl<_LiveRoomGroup>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveRoomGroupToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LiveRoomGroup &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.logo, logo) || other.logo == logo) &&
            (identical(other.opening, opening) || other.opening == opening) &&
            (identical(other.isJoined, isJoined) ||
                other.isJoined == isJoined) &&
            (identical(other.hasApplied, hasApplied) ||
                other.hasApplied == hasApplied) &&
            (identical(other.needConfirm, needConfirm) ||
                other.needConfirm == needConfirm));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    name,
    description,
    logo,
    opening,
    isJoined,
    hasApplied,
    needConfirm,
  );

  @override
  String toString() {
    return 'LiveRoomGroup(id: $id, name: $name, description: $description, logo: $logo, opening: $opening, isJoined: $isJoined, hasApplied: $hasApplied, needConfirm: $needConfirm)';
  }
}

/// @nodoc
abstract mixin class _$LiveRoomGroupCopyWith<$Res>
    implements $LiveRoomGroupCopyWith<$Res> {
  factory _$LiveRoomGroupCopyWith(
    _LiveRoomGroup value,
    $Res Function(_LiveRoomGroup) _then,
  ) = __$LiveRoomGroupCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'uniqId') String id,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'logo') String logo,
    @JsonKey(name: 'isOpen') bool opening,
    @JsonKey(name: 'isJoined') bool isJoined,
    @JsonKey(name: 'hasApplied') bool hasApplied,
    @JsonKey(name: 'needConfirm') bool needConfirm,
  });
}

/// @nodoc
class __$LiveRoomGroupCopyWithImpl<$Res>
    implements _$LiveRoomGroupCopyWith<$Res> {
  __$LiveRoomGroupCopyWithImpl(this._self, this._then);

  final _LiveRoomGroup _self;
  final $Res Function(_LiveRoomGroup) _then;

  /// Create a copy of LiveRoomGroup
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? description = null,
    Object? logo = null,
    Object? opening = null,
    Object? isJoined = null,
    Object? hasApplied = null,
    Object? needConfirm = null,
  }) {
    return _then(
      _LiveRoomGroup(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        logo: null == logo
            ? _self.logo
            : logo // ignore: cast_nullable_to_non_nullable
                  as String,
        opening: null == opening
            ? _self.opening
            : opening // ignore: cast_nullable_to_non_nullable
                  as bool,
        isJoined: null == isJoined
            ? _self.isJoined
            : isJoined // ignore: cast_nullable_to_non_nullable
                  as bool,
        hasApplied: null == hasApplied
            ? _self.hasApplied
            : hasApplied // ignore: cast_nullable_to_non_nullable
                  as bool,
        needConfirm: null == needConfirm
            ? _self.needConfirm
            : needConfirm // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
mixin _$LiveRoomUsers {
  @JsonKey(name: 'users')
  List<LiveRoomUser> get users;

  /// Create a copy of LiveRoomUsers
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveRoomUsersCopyWith<LiveRoomUsers> get copyWith =>
      _$LiveRoomUsersCopyWithImpl<LiveRoomUsers>(
        this as LiveRoomUsers,
        _$identity,
      );

  /// Serializes this LiveRoomUsers to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveRoomUsers &&
            const DeepCollectionEquality().equals(other.users, users));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(users));

  @override
  String toString() {
    return 'LiveRoomUsers(users: $users)';
  }
}

/// @nodoc
abstract mixin class $LiveRoomUsersCopyWith<$Res> {
  factory $LiveRoomUsersCopyWith(
    LiveRoomUsers value,
    $Res Function(LiveRoomUsers) _then,
  ) = _$LiveRoomUsersCopyWithImpl;
  @useResult
  $Res call({@JsonKey(name: 'users') List<LiveRoomUser> users});
}

/// @nodoc
class _$LiveRoomUsersCopyWithImpl<$Res>
    implements $LiveRoomUsersCopyWith<$Res> {
  _$LiveRoomUsersCopyWithImpl(this._self, this._then);

  final LiveRoomUsers _self;
  final $Res Function(LiveRoomUsers) _then;

  /// Create a copy of LiveRoomUsers
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? users = null}) {
    return _then(
      _self.copyWith(
        users: null == users
            ? _self.users
            : users // ignore: cast_nullable_to_non_nullable
                  as List<LiveRoomUser>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _LiveRoomUsers extends LiveRoomUsers {
  const _LiveRoomUsers({
    @JsonKey(name: 'users') required final List<LiveRoomUser> users,
  }) : _users = users,
       super._();
  factory _LiveRoomUsers.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomUsersFromJson(json);

  final List<LiveRoomUser> _users;
  @override
  @JsonKey(name: 'users')
  List<LiveRoomUser> get users {
    if (_users is EqualUnmodifiableListView) return _users;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_users);
  }

  /// Create a copy of LiveRoomUsers
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LiveRoomUsersCopyWith<_LiveRoomUsers> get copyWith =>
      __$LiveRoomUsersCopyWithImpl<_LiveRoomUsers>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveRoomUsersToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LiveRoomUsers &&
            const DeepCollectionEquality().equals(other._users, _users));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_users));

  @override
  String toString() {
    return 'LiveRoomUsers(users: $users)';
  }
}

/// @nodoc
abstract mixin class _$LiveRoomUsersCopyWith<$Res>
    implements $LiveRoomUsersCopyWith<$Res> {
  factory _$LiveRoomUsersCopyWith(
    _LiveRoomUsers value,
    $Res Function(_LiveRoomUsers) _then,
  ) = __$LiveRoomUsersCopyWithImpl;
  @override
  @useResult
  $Res call({@JsonKey(name: 'users') List<LiveRoomUser> users});
}

/// @nodoc
class __$LiveRoomUsersCopyWithImpl<$Res>
    implements _$LiveRoomUsersCopyWith<$Res> {
  __$LiveRoomUsersCopyWithImpl(this._self, this._then);

  final _LiveRoomUsers _self;
  final $Res Function(_LiveRoomUsers) _then;

  /// Create a copy of LiveRoomUsers
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? users = null}) {
    return _then(
      _LiveRoomUsers(
        users: null == users
            ? _self._users
            : users // ignore: cast_nullable_to_non_nullable
                  as List<LiveRoomUser>,
      ),
    );
  }
}

/// @nodoc
mixin _$LiveRoomUser {
  @JsonUserIdConverter()
  @JsonKey(name: 'uniqId')
  int get userId;
  @JsonKey(name: 'username')
  String get username;
  @JsonKey(name: 'displayName')
  String get displayName;
  @JsonKey(name: 'avatar')
  String get avatar;
  @LiveRoomUserRoleConverter()
  @JsonKey(name: 'role')
  LiveRoomUserRole get role;

  /// For [LiveRoomUserRole.player].
  @JsonKey(name: 'isPaused')
  bool get isPaused;

  /// Create a copy of LiveRoomUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveRoomUserCopyWith<LiveRoomUser> get copyWith =>
      _$LiveRoomUserCopyWithImpl<LiveRoomUser>(
        this as LiveRoomUser,
        _$identity,
      );

  /// Serializes this LiveRoomUser to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveRoomUser &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.isPaused, isPaused) ||
                other.isPaused == isPaused));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    username,
    displayName,
    avatar,
    role,
    isPaused,
  );

  @override
  String toString() {
    return 'LiveRoomUser(userId: $userId, username: $username, displayName: $displayName, avatar: $avatar, role: $role, isPaused: $isPaused)';
  }
}

/// @nodoc
abstract mixin class $LiveRoomUserCopyWith<$Res> {
  factory $LiveRoomUserCopyWith(
    LiveRoomUser value,
    $Res Function(LiveRoomUser) _then,
  ) = _$LiveRoomUserCopyWithImpl;
  @useResult
  $Res call({
    @JsonUserIdConverter() @JsonKey(name: 'uniqId') int userId,
    @JsonKey(name: 'username') String username,
    @JsonKey(name: 'displayName') String displayName,
    @JsonKey(name: 'avatar') String avatar,
    @LiveRoomUserRoleConverter() @JsonKey(name: 'role') LiveRoomUserRole role,
    @JsonKey(name: 'isPaused') bool isPaused,
  });
}

/// @nodoc
class _$LiveRoomUserCopyWithImpl<$Res> implements $LiveRoomUserCopyWith<$Res> {
  _$LiveRoomUserCopyWithImpl(this._self, this._then);

  final LiveRoomUser _self;
  final $Res Function(LiveRoomUser) _then;

  /// Create a copy of LiveRoomUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = null,
    Object? username = null,
    Object? displayName = null,
    Object? avatar = null,
    Object? role = null,
    Object? isPaused = null,
  }) {
    return _then(
      _self.copyWith(
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        displayName: null == displayName
            ? _self.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        role: null == role
            ? _self.role
            : role // ignore: cast_nullable_to_non_nullable
                  as LiveRoomUserRole,
        isPaused: null == isPaused
            ? _self.isPaused
            : isPaused // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _LiveRoomUser extends LiveRoomUser {
  const _LiveRoomUser({
    @JsonUserIdConverter() @JsonKey(name: 'uniqId') required final int userId,
    @JsonKey(name: 'username') required final String username,
    @JsonKey(name: 'displayName') final String displayName = '',
    @JsonKey(name: 'avatar') final String avatar = '',
    @LiveRoomUserRoleConverter() @JsonKey(name: 'role') required this.role,
    @JsonKey(name: 'isPaused') this.isPaused = false,
  }) : super._(
         userId: userId,
         username: username,
         displayName: displayName,
         avatar: avatar,
       );
  factory _LiveRoomUser.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomUserFromJson(json);

  @override
  @LiveRoomUserRoleConverter()
  @JsonKey(name: 'role')
  final LiveRoomUserRole role;

  /// For [LiveRoomUserRole.player].
  @override
  @JsonKey(name: 'isPaused')
  final bool isPaused;

  /// Create a copy of LiveRoomUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LiveRoomUserCopyWith<_LiveRoomUser> get copyWith =>
      __$LiveRoomUserCopyWithImpl<_LiveRoomUser>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveRoomUserToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LiveRoomUser &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.avatar, avatar) || other.avatar == avatar) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.isPaused, isPaused) ||
                other.isPaused == isPaused));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    userId,
    username,
    displayName,
    avatar,
    role,
    isPaused,
  );

  @override
  String toString() {
    return 'LiveRoomUser(userId: $userId, username: $username, displayName: $displayName, avatar: $avatar, role: $role, isPaused: $isPaused)';
  }
}

/// @nodoc
abstract mixin class _$LiveRoomUserCopyWith<$Res>
    implements $LiveRoomUserCopyWith<$Res> {
  factory _$LiveRoomUserCopyWith(
    _LiveRoomUser value,
    $Res Function(_LiveRoomUser) _then,
  ) = __$LiveRoomUserCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonUserIdConverter() @JsonKey(name: 'uniqId') int userId,
    @JsonKey(name: 'username') String username,
    @JsonKey(name: 'displayName') String displayName,
    @JsonKey(name: 'avatar') String avatar,
    @LiveRoomUserRoleConverter() @JsonKey(name: 'role') LiveRoomUserRole role,
    @JsonKey(name: 'isPaused') bool isPaused,
  });
}

/// @nodoc
class __$LiveRoomUserCopyWithImpl<$Res>
    implements _$LiveRoomUserCopyWith<$Res> {
  __$LiveRoomUserCopyWithImpl(this._self, this._then);

  final _LiveRoomUser _self;
  final $Res Function(_LiveRoomUser) _then;

  /// Create a copy of LiveRoomUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? userId = null,
    Object? username = null,
    Object? displayName = null,
    Object? avatar = null,
    Object? role = null,
    Object? isPaused = null,
  }) {
    return _then(
      _LiveRoomUser(
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        displayName: null == displayName
            ? _self.displayName
            : displayName // ignore: cast_nullable_to_non_nullable
                  as String,
        avatar: null == avatar
            ? _self.avatar
            : avatar // ignore: cast_nullable_to_non_nullable
                  as String,
        role: null == role
            ? _self.role
            : role // ignore: cast_nullable_to_non_nullable
                  as LiveRoomUserRole,
        isPaused: null == isPaused
            ? _self.isPaused
            : isPaused // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
mixin _$LiveRoomUsersFiltered {
  // Creator could went offline.
  LiveRoomUser? get creator;
  List<LiveRoomUser> get broadcasters;
  List<LiveRoomUser> get players;
  List<LiveRoomUser> get users;

  /// Create a copy of LiveRoomUsersFiltered
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveRoomUsersFilteredCopyWith<LiveRoomUsersFiltered> get copyWith =>
      _$LiveRoomUsersFilteredCopyWithImpl<LiveRoomUsersFiltered>(
        this as LiveRoomUsersFiltered,
        _$identity,
      );

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveRoomUsersFiltered &&
            (identical(other.creator, creator) || other.creator == creator) &&
            const DeepCollectionEquality().equals(
              other.broadcasters,
              broadcasters,
            ) &&
            const DeepCollectionEquality().equals(other.players, players) &&
            const DeepCollectionEquality().equals(other.users, users));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    creator,
    const DeepCollectionEquality().hash(broadcasters),
    const DeepCollectionEquality().hash(players),
    const DeepCollectionEquality().hash(users),
  );

  @override
  String toString() {
    return 'LiveRoomUsersFiltered(creator: $creator, broadcasters: $broadcasters, players: $players, users: $users)';
  }
}

/// @nodoc
abstract mixin class $LiveRoomUsersFilteredCopyWith<$Res> {
  factory $LiveRoomUsersFilteredCopyWith(
    LiveRoomUsersFiltered value,
    $Res Function(LiveRoomUsersFiltered) _then,
  ) = _$LiveRoomUsersFilteredCopyWithImpl;
  @useResult
  $Res call({
    LiveRoomUser? creator,
    List<LiveRoomUser> broadcasters,
    List<LiveRoomUser> players,
    List<LiveRoomUser> users,
  });

  $LiveRoomUserCopyWith<$Res>? get creator;
}

/// @nodoc
class _$LiveRoomUsersFilteredCopyWithImpl<$Res>
    implements $LiveRoomUsersFilteredCopyWith<$Res> {
  _$LiveRoomUsersFilteredCopyWithImpl(this._self, this._then);

  final LiveRoomUsersFiltered _self;
  final $Res Function(LiveRoomUsersFiltered) _then;

  /// Create a copy of LiveRoomUsersFiltered
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? creator = freezed,
    Object? broadcasters = null,
    Object? players = null,
    Object? users = null,
  }) {
    return _then(
      _self.copyWith(
        creator: freezed == creator
            ? _self.creator
            : creator // ignore: cast_nullable_to_non_nullable
                  as LiveRoomUser?,
        broadcasters: null == broadcasters
            ? _self.broadcasters
            : broadcasters // ignore: cast_nullable_to_non_nullable
                  as List<LiveRoomUser>,
        players: null == players
            ? _self.players
            : players // ignore: cast_nullable_to_non_nullable
                  as List<LiveRoomUser>,
        users: null == users
            ? _self.users
            : users // ignore: cast_nullable_to_non_nullable
                  as List<LiveRoomUser>,
      ),
    );
  }

  /// Create a copy of LiveRoomUsersFiltered
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LiveRoomUserCopyWith<$Res>? get creator {
    if (_self.creator == null) {
      return null;
    }

    return $LiveRoomUserCopyWith<$Res>(_self.creator!, (value) {
      return _then(_self.copyWith(creator: value));
    });
  }
}

/// @nodoc

class _LiveRoomUsersFiltered extends LiveRoomUsersFiltered {
  const _LiveRoomUsersFiltered({
    required this.creator,
    required final List<LiveRoomUser> broadcasters,
    required final List<LiveRoomUser> players,
    required final List<LiveRoomUser> users,
  }) : _broadcasters = broadcasters,
       _players = players,
       _users = users,
       super._();

  // Creator could went offline.
  @override
  final LiveRoomUser? creator;
  final List<LiveRoomUser> _broadcasters;
  @override
  List<LiveRoomUser> get broadcasters {
    if (_broadcasters is EqualUnmodifiableListView) return _broadcasters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_broadcasters);
  }

  final List<LiveRoomUser> _players;
  @override
  List<LiveRoomUser> get players {
    if (_players is EqualUnmodifiableListView) return _players;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_players);
  }

  final List<LiveRoomUser> _users;
  @override
  List<LiveRoomUser> get users {
    if (_users is EqualUnmodifiableListView) return _users;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_users);
  }

  /// Create a copy of LiveRoomUsersFiltered
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LiveRoomUsersFilteredCopyWith<_LiveRoomUsersFiltered> get copyWith =>
      __$LiveRoomUsersFilteredCopyWithImpl<_LiveRoomUsersFiltered>(
        this,
        _$identity,
      );

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LiveRoomUsersFiltered &&
            (identical(other.creator, creator) || other.creator == creator) &&
            const DeepCollectionEquality().equals(
              other._broadcasters,
              _broadcasters,
            ) &&
            const DeepCollectionEquality().equals(other._players, _players) &&
            const DeepCollectionEquality().equals(other._users, _users));
  }

  @override
  int get hashCode => Object.hash(
    runtimeType,
    creator,
    const DeepCollectionEquality().hash(_broadcasters),
    const DeepCollectionEquality().hash(_players),
    const DeepCollectionEquality().hash(_users),
  );

  @override
  String toString() {
    return 'LiveRoomUsersFiltered(creator: $creator, broadcasters: $broadcasters, players: $players, users: $users)';
  }
}

/// @nodoc
abstract mixin class _$LiveRoomUsersFilteredCopyWith<$Res>
    implements $LiveRoomUsersFilteredCopyWith<$Res> {
  factory _$LiveRoomUsersFilteredCopyWith(
    _LiveRoomUsersFiltered value,
    $Res Function(_LiveRoomUsersFiltered) _then,
  ) = __$LiveRoomUsersFilteredCopyWithImpl;
  @override
  @useResult
  $Res call({
    LiveRoomUser? creator,
    List<LiveRoomUser> broadcasters,
    List<LiveRoomUser> players,
    List<LiveRoomUser> users,
  });

  @override
  $LiveRoomUserCopyWith<$Res>? get creator;
}

/// @nodoc
class __$LiveRoomUsersFilteredCopyWithImpl<$Res>
    implements _$LiveRoomUsersFilteredCopyWith<$Res> {
  __$LiveRoomUsersFilteredCopyWithImpl(this._self, this._then);

  final _LiveRoomUsersFiltered _self;
  final $Res Function(_LiveRoomUsersFiltered) _then;

  /// Create a copy of LiveRoomUsersFiltered
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? creator = freezed,
    Object? broadcasters = null,
    Object? players = null,
    Object? users = null,
  }) {
    return _then(
      _LiveRoomUsersFiltered(
        creator: freezed == creator
            ? _self.creator
            : creator // ignore: cast_nullable_to_non_nullable
                  as LiveRoomUser?,
        broadcasters: null == broadcasters
            ? _self._broadcasters
            : broadcasters // ignore: cast_nullable_to_non_nullable
                  as List<LiveRoomUser>,
        players: null == players
            ? _self._players
            : players // ignore: cast_nullable_to_non_nullable
                  as List<LiveRoomUser>,
        users: null == users
            ? _self._users
            : users // ignore: cast_nullable_to_non_nullable
                  as List<LiveRoomUser>,
      ),
    );
  }

  /// Create a copy of LiveRoomUsersFiltered
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LiveRoomUserCopyWith<$Res>? get creator {
    if (_self.creator == null) {
      return null;
    }

    return $LiveRoomUserCopyWith<$Res>(_self.creator!, (value) {
      return _then(_self.copyWith(creator: value));
    });
  }
}

/// @nodoc
mixin _$LiveRoomToken {
  @JsonKey(name: 'isPinned')
  bool get pinned;
  @JsonKey(name: 'address')
  String get address;
  @JsonKey(name: 'chain')
  String get chain;
  @JsonKey(name: 'logoUri')
  String get icon;
  @JsonKey(name: 'name')
  String? get name;
  @JsonKey(name: 'symbol')
  String? get symbol;

  /// Create a copy of LiveRoomToken
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveRoomTokenCopyWith<LiveRoomToken> get copyWith =>
      _$LiveRoomTokenCopyWithImpl<LiveRoomToken>(
        this as LiveRoomToken,
        _$identity,
      );

  /// Serializes this LiveRoomToken to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveRoomToken &&
            (identical(other.pinned, pinned) || other.pinned == pinned) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.chain, chain) || other.chain == chain) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.symbol, symbol) || other.symbol == symbol));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, pinned, address, chain, icon, name, symbol);

  @override
  String toString() {
    return 'LiveRoomToken(pinned: $pinned, address: $address, chain: $chain, icon: $icon, name: $name, symbol: $symbol)';
  }
}

/// @nodoc
abstract mixin class $LiveRoomTokenCopyWith<$Res> {
  factory $LiveRoomTokenCopyWith(
    LiveRoomToken value,
    $Res Function(LiveRoomToken) _then,
  ) = _$LiveRoomTokenCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'isPinned') bool pinned,
    @JsonKey(name: 'address') String address,
    @JsonKey(name: 'chain') String chain,
    @JsonKey(name: 'logoUri') String icon,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: 'symbol') String? symbol,
  });
}

/// @nodoc
class _$LiveRoomTokenCopyWithImpl<$Res>
    implements $LiveRoomTokenCopyWith<$Res> {
  _$LiveRoomTokenCopyWithImpl(this._self, this._then);

  final LiveRoomToken _self;
  final $Res Function(LiveRoomToken) _then;

  /// Create a copy of LiveRoomToken
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pinned = null,
    Object? address = null,
    Object? chain = null,
    Object? icon = null,
    Object? name = freezed,
    Object? symbol = freezed,
  }) {
    return _then(
      _self.copyWith(
        pinned: null == pinned
            ? _self.pinned
            : pinned // ignore: cast_nullable_to_non_nullable
                  as bool,
        address: null == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        chain: null == chain
            ? _self.chain
            : chain // ignore: cast_nullable_to_non_nullable
                  as String,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
        name: freezed == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String?,
        symbol: freezed == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _LiveRoomToken implements LiveRoomToken {
  const _LiveRoomToken({
    @JsonKey(name: 'isPinned') this.pinned = false,
    @JsonKey(name: 'address') required this.address,
    @JsonKey(name: 'chain') required this.chain,
    @JsonKey(name: 'logoUri') this.icon = '',
    @JsonKey(name: 'name') this.name,
    @JsonKey(name: 'symbol') this.symbol,
  });
  factory _LiveRoomToken.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomTokenFromJson(json);

  @override
  @JsonKey(name: 'isPinned')
  final bool pinned;
  @override
  @JsonKey(name: 'address')
  final String address;
  @override
  @JsonKey(name: 'chain')
  final String chain;
  @override
  @JsonKey(name: 'logoUri')
  final String icon;
  @override
  @JsonKey(name: 'name')
  final String? name;
  @override
  @JsonKey(name: 'symbol')
  final String? symbol;

  /// Create a copy of LiveRoomToken
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LiveRoomTokenCopyWith<_LiveRoomToken> get copyWith =>
      __$LiveRoomTokenCopyWithImpl<_LiveRoomToken>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveRoomTokenToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LiveRoomToken &&
            (identical(other.pinned, pinned) || other.pinned == pinned) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.chain, chain) || other.chain == chain) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.symbol, symbol) || other.symbol == symbol));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, pinned, address, chain, icon, name, symbol);

  @override
  String toString() {
    return 'LiveRoomToken(pinned: $pinned, address: $address, chain: $chain, icon: $icon, name: $name, symbol: $symbol)';
  }
}

/// @nodoc
abstract mixin class _$LiveRoomTokenCopyWith<$Res>
    implements $LiveRoomTokenCopyWith<$Res> {
  factory _$LiveRoomTokenCopyWith(
    _LiveRoomToken value,
    $Res Function(_LiveRoomToken) _then,
  ) = __$LiveRoomTokenCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'isPinned') bool pinned,
    @JsonKey(name: 'address') String address,
    @JsonKey(name: 'chain') String chain,
    @JsonKey(name: 'logoUri') String icon,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: 'symbol') String? symbol,
  });
}

/// @nodoc
class __$LiveRoomTokenCopyWithImpl<$Res>
    implements _$LiveRoomTokenCopyWith<$Res> {
  __$LiveRoomTokenCopyWithImpl(this._self, this._then);

  final _LiveRoomToken _self;
  final $Res Function(_LiveRoomToken) _then;

  /// Create a copy of LiveRoomToken
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? pinned = null,
    Object? address = null,
    Object? chain = null,
    Object? icon = null,
    Object? name = freezed,
    Object? symbol = freezed,
  }) {
    return _then(
      _LiveRoomToken(
        pinned: null == pinned
            ? _self.pinned
            : pinned // ignore: cast_nullable_to_non_nullable
                  as bool,
        address: null == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        chain: null == chain
            ? _self.chain
            : chain // ignore: cast_nullable_to_non_nullable
                  as String,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
        name: freezed == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String?,
        symbol: freezed == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
mixin _$LiveRoomSummary {
  @JsonKey(name: 'isPrivate')
  bool get private;
  @JsonKey(name: 'uniqId')
  String get id;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'group')
  LiveRoomGroup? get group;
  @JsonKey(name: 'totalViewer')
  int get viewerCount;
  @JsonKey(name: 'mentionCa')
  int get caMentionedCount;
  @JsonKey(name: 'duration')
  int? get duration;
  @EpochDateTimeConverter()
  @JsonKey(name: 'openTime')
  DateTime get openTime;
  @EpochDateTimeConverter()
  @JsonKey(name: 'closeTime')
  DateTime? get closeTime;
  @JsonKey(name: 'userAvatars')
  List<String> get userAvatars;
  @JsonKey(name: 'tokenLogos')
  List<String> get tokenLogos;

  /// Create a copy of LiveRoomSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveRoomSummaryCopyWith<LiveRoomSummary> get copyWith =>
      _$LiveRoomSummaryCopyWithImpl<LiveRoomSummary>(
        this as LiveRoomSummary,
        _$identity,
      );

  /// Serializes this LiveRoomSummary to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveRoomSummary &&
            (identical(other.private, private) || other.private == private) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.group, group) || other.group == group) &&
            (identical(other.viewerCount, viewerCount) ||
                other.viewerCount == viewerCount) &&
            (identical(other.caMentionedCount, caMentionedCount) ||
                other.caMentionedCount == caMentionedCount) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.openTime, openTime) ||
                other.openTime == openTime) &&
            (identical(other.closeTime, closeTime) ||
                other.closeTime == closeTime) &&
            const DeepCollectionEquality().equals(
              other.userAvatars,
              userAvatars,
            ) &&
            const DeepCollectionEquality().equals(
              other.tokenLogos,
              tokenLogos,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    private,
    id,
    name,
    group,
    viewerCount,
    caMentionedCount,
    duration,
    openTime,
    closeTime,
    const DeepCollectionEquality().hash(userAvatars),
    const DeepCollectionEquality().hash(tokenLogos),
  );

  @override
  String toString() {
    return 'LiveRoomSummary(private: $private, id: $id, name: $name, group: $group, viewerCount: $viewerCount, caMentionedCount: $caMentionedCount, duration: $duration, openTime: $openTime, closeTime: $closeTime, userAvatars: $userAvatars, tokenLogos: $tokenLogos)';
  }
}

/// @nodoc
abstract mixin class $LiveRoomSummaryCopyWith<$Res> {
  factory $LiveRoomSummaryCopyWith(
    LiveRoomSummary value,
    $Res Function(LiveRoomSummary) _then,
  ) = _$LiveRoomSummaryCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'isPrivate') bool private,
    @JsonKey(name: 'uniqId') String id,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'group') LiveRoomGroup? group,
    @JsonKey(name: 'totalViewer') int viewerCount,
    @JsonKey(name: 'mentionCa') int caMentionedCount,
    @JsonKey(name: 'duration') int? duration,
    @EpochDateTimeConverter() @JsonKey(name: 'openTime') DateTime openTime,
    @EpochDateTimeConverter() @JsonKey(name: 'closeTime') DateTime? closeTime,
    @JsonKey(name: 'userAvatars') List<String> userAvatars,
    @JsonKey(name: 'tokenLogos') List<String> tokenLogos,
  });

  $LiveRoomGroupCopyWith<$Res>? get group;
}

/// @nodoc
class _$LiveRoomSummaryCopyWithImpl<$Res>
    implements $LiveRoomSummaryCopyWith<$Res> {
  _$LiveRoomSummaryCopyWithImpl(this._self, this._then);

  final LiveRoomSummary _self;
  final $Res Function(LiveRoomSummary) _then;

  /// Create a copy of LiveRoomSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? private = null,
    Object? id = null,
    Object? name = null,
    Object? group = freezed,
    Object? viewerCount = null,
    Object? caMentionedCount = null,
    Object? duration = freezed,
    Object? openTime = null,
    Object? closeTime = freezed,
    Object? userAvatars = null,
    Object? tokenLogos = null,
  }) {
    return _then(
      _self.copyWith(
        private: null == private
            ? _self.private
            : private // ignore: cast_nullable_to_non_nullable
                  as bool,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        group: freezed == group
            ? _self.group
            : group // ignore: cast_nullable_to_non_nullable
                  as LiveRoomGroup?,
        viewerCount: null == viewerCount
            ? _self.viewerCount
            : viewerCount // ignore: cast_nullable_to_non_nullable
                  as int,
        caMentionedCount: null == caMentionedCount
            ? _self.caMentionedCount
            : caMentionedCount // ignore: cast_nullable_to_non_nullable
                  as int,
        duration: freezed == duration
            ? _self.duration
            : duration // ignore: cast_nullable_to_non_nullable
                  as int?,
        openTime: null == openTime
            ? _self.openTime
            : openTime // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        closeTime: freezed == closeTime
            ? _self.closeTime
            : closeTime // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        userAvatars: null == userAvatars
            ? _self.userAvatars
            : userAvatars // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        tokenLogos: null == tokenLogos
            ? _self.tokenLogos
            : tokenLogos // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }

  /// Create a copy of LiveRoomSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LiveRoomGroupCopyWith<$Res>? get group {
    if (_self.group == null) {
      return null;
    }

    return $LiveRoomGroupCopyWith<$Res>(_self.group!, (value) {
      return _then(_self.copyWith(group: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _LiveRoomSummary implements LiveRoomSummary {
  const _LiveRoomSummary({
    @JsonKey(name: 'isPrivate') this.private = false,
    @JsonKey(name: 'uniqId') required this.id,
    @JsonKey(name: 'name') this.name = '',
    @JsonKey(name: 'group') this.group,
    @JsonKey(name: 'totalViewer') this.viewerCount = 1,
    @JsonKey(name: 'mentionCa') this.caMentionedCount = 1,
    @JsonKey(name: 'duration') this.duration,
    @EpochDateTimeConverter() @JsonKey(name: 'openTime') required this.openTime,
    @EpochDateTimeConverter() @JsonKey(name: 'closeTime') this.closeTime,
    @JsonKey(name: 'userAvatars') final List<String> userAvatars = const [],
    @JsonKey(name: 'tokenLogos') final List<String> tokenLogos = const [],
  }) : _userAvatars = userAvatars,
       _tokenLogos = tokenLogos;
  factory _LiveRoomSummary.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomSummaryFromJson(json);

  @override
  @JsonKey(name: 'isPrivate')
  final bool private;
  @override
  @JsonKey(name: 'uniqId')
  final String id;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'group')
  final LiveRoomGroup? group;
  @override
  @JsonKey(name: 'totalViewer')
  final int viewerCount;
  @override
  @JsonKey(name: 'mentionCa')
  final int caMentionedCount;
  @override
  @JsonKey(name: 'duration')
  final int? duration;
  @override
  @EpochDateTimeConverter()
  @JsonKey(name: 'openTime')
  final DateTime openTime;
  @override
  @EpochDateTimeConverter()
  @JsonKey(name: 'closeTime')
  final DateTime? closeTime;
  final List<String> _userAvatars;
  @override
  @JsonKey(name: 'userAvatars')
  List<String> get userAvatars {
    if (_userAvatars is EqualUnmodifiableListView) return _userAvatars;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_userAvatars);
  }

  final List<String> _tokenLogos;
  @override
  @JsonKey(name: 'tokenLogos')
  List<String> get tokenLogos {
    if (_tokenLogos is EqualUnmodifiableListView) return _tokenLogos;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tokenLogos);
  }

  /// Create a copy of LiveRoomSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LiveRoomSummaryCopyWith<_LiveRoomSummary> get copyWith =>
      __$LiveRoomSummaryCopyWithImpl<_LiveRoomSummary>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveRoomSummaryToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LiveRoomSummary &&
            (identical(other.private, private) || other.private == private) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.group, group) || other.group == group) &&
            (identical(other.viewerCount, viewerCount) ||
                other.viewerCount == viewerCount) &&
            (identical(other.caMentionedCount, caMentionedCount) ||
                other.caMentionedCount == caMentionedCount) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.openTime, openTime) ||
                other.openTime == openTime) &&
            (identical(other.closeTime, closeTime) ||
                other.closeTime == closeTime) &&
            const DeepCollectionEquality().equals(
              other._userAvatars,
              _userAvatars,
            ) &&
            const DeepCollectionEquality().equals(
              other._tokenLogos,
              _tokenLogos,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    private,
    id,
    name,
    group,
    viewerCount,
    caMentionedCount,
    duration,
    openTime,
    closeTime,
    const DeepCollectionEquality().hash(_userAvatars),
    const DeepCollectionEquality().hash(_tokenLogos),
  );

  @override
  String toString() {
    return 'LiveRoomSummary(private: $private, id: $id, name: $name, group: $group, viewerCount: $viewerCount, caMentionedCount: $caMentionedCount, duration: $duration, openTime: $openTime, closeTime: $closeTime, userAvatars: $userAvatars, tokenLogos: $tokenLogos)';
  }
}

/// @nodoc
abstract mixin class _$LiveRoomSummaryCopyWith<$Res>
    implements $LiveRoomSummaryCopyWith<$Res> {
  factory _$LiveRoomSummaryCopyWith(
    _LiveRoomSummary value,
    $Res Function(_LiveRoomSummary) _then,
  ) = __$LiveRoomSummaryCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'isPrivate') bool private,
    @JsonKey(name: 'uniqId') String id,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'group') LiveRoomGroup? group,
    @JsonKey(name: 'totalViewer') int viewerCount,
    @JsonKey(name: 'mentionCa') int caMentionedCount,
    @JsonKey(name: 'duration') int? duration,
    @EpochDateTimeConverter() @JsonKey(name: 'openTime') DateTime openTime,
    @EpochDateTimeConverter() @JsonKey(name: 'closeTime') DateTime? closeTime,
    @JsonKey(name: 'userAvatars') List<String> userAvatars,
    @JsonKey(name: 'tokenLogos') List<String> tokenLogos,
  });

  @override
  $LiveRoomGroupCopyWith<$Res>? get group;
}

/// @nodoc
class __$LiveRoomSummaryCopyWithImpl<$Res>
    implements _$LiveRoomSummaryCopyWith<$Res> {
  __$LiveRoomSummaryCopyWithImpl(this._self, this._then);

  final _LiveRoomSummary _self;
  final $Res Function(_LiveRoomSummary) _then;

  /// Create a copy of LiveRoomSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? private = null,
    Object? id = null,
    Object? name = null,
    Object? group = freezed,
    Object? viewerCount = null,
    Object? caMentionedCount = null,
    Object? duration = freezed,
    Object? openTime = null,
    Object? closeTime = freezed,
    Object? userAvatars = null,
    Object? tokenLogos = null,
  }) {
    return _then(
      _LiveRoomSummary(
        private: null == private
            ? _self.private
            : private // ignore: cast_nullable_to_non_nullable
                  as bool,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        group: freezed == group
            ? _self.group
            : group // ignore: cast_nullable_to_non_nullable
                  as LiveRoomGroup?,
        viewerCount: null == viewerCount
            ? _self.viewerCount
            : viewerCount // ignore: cast_nullable_to_non_nullable
                  as int,
        caMentionedCount: null == caMentionedCount
            ? _self.caMentionedCount
            : caMentionedCount // ignore: cast_nullable_to_non_nullable
                  as int,
        duration: freezed == duration
            ? _self.duration
            : duration // ignore: cast_nullable_to_non_nullable
                  as int?,
        openTime: null == openTime
            ? _self.openTime
            : openTime // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        closeTime: freezed == closeTime
            ? _self.closeTime
            : closeTime // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
        userAvatars: null == userAvatars
            ? _self._userAvatars
            : userAvatars // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        tokenLogos: null == tokenLogos
            ? _self._tokenLogos
            : tokenLogos // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }

  /// Create a copy of LiveRoomSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LiveRoomGroupCopyWith<$Res>? get group {
    if (_self.group == null) {
      return null;
    }

    return $LiveRoomGroupCopyWith<$Res>(_self.group!, (value) {
      return _then(_self.copyWith(group: value));
    });
  }
}

/// @nodoc
mixin _$LiveRoomLink {
  @JsonKey(name: 'isPinned')
  bool get pinned;
  @JsonKey(name: 'uniqId')
  String get id;
  @JsonKey(name: 'href')
  String get href;
  @JsonKey(name: 'siteName')
  String get siteName;
  @JsonKey(name: 'title')
  String get title;
  @JsonKey(name: 'description')
  String get description;
  @JsonKey(name: 'favicon')
  String get icon;

  /// Create a copy of LiveRoomLink
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveRoomLinkCopyWith<LiveRoomLink> get copyWith =>
      _$LiveRoomLinkCopyWithImpl<LiveRoomLink>(
        this as LiveRoomLink,
        _$identity,
      );

  /// Serializes this LiveRoomLink to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveRoomLink &&
            (identical(other.pinned, pinned) || other.pinned == pinned) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.href, href) || other.href == href) &&
            (identical(other.siteName, siteName) ||
                other.siteName == siteName) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.icon, icon) || other.icon == icon));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    pinned,
    id,
    href,
    siteName,
    title,
    description,
    icon,
  );

  @override
  String toString() {
    return 'LiveRoomLink(pinned: $pinned, id: $id, href: $href, siteName: $siteName, title: $title, description: $description, icon: $icon)';
  }
}

/// @nodoc
abstract mixin class $LiveRoomLinkCopyWith<$Res> {
  factory $LiveRoomLinkCopyWith(
    LiveRoomLink value,
    $Res Function(LiveRoomLink) _then,
  ) = _$LiveRoomLinkCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'isPinned') bool pinned,
    @JsonKey(name: 'uniqId') String id,
    @JsonKey(name: 'href') String href,
    @JsonKey(name: 'siteName') String siteName,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'favicon') String icon,
  });
}

/// @nodoc
class _$LiveRoomLinkCopyWithImpl<$Res> implements $LiveRoomLinkCopyWith<$Res> {
  _$LiveRoomLinkCopyWithImpl(this._self, this._then);

  final LiveRoomLink _self;
  final $Res Function(LiveRoomLink) _then;

  /// Create a copy of LiveRoomLink
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? pinned = null,
    Object? id = null,
    Object? href = null,
    Object? siteName = null,
    Object? title = null,
    Object? description = null,
    Object? icon = null,
  }) {
    return _then(
      _self.copyWith(
        pinned: null == pinned
            ? _self.pinned
            : pinned // ignore: cast_nullable_to_non_nullable
                  as bool,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        href: null == href
            ? _self.href
            : href // ignore: cast_nullable_to_non_nullable
                  as String,
        siteName: null == siteName
            ? _self.siteName
            : siteName // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _LiveRoomLink implements LiveRoomLink {
  const _LiveRoomLink({
    @JsonKey(name: 'isPinned') this.pinned = false,
    @JsonKey(name: 'uniqId') required this.id,
    @JsonKey(name: 'href') required this.href,
    @JsonKey(name: 'siteName') required this.siteName,
    @JsonKey(name: 'title') required this.title,
    @JsonKey(name: 'description') this.description = '',
    @JsonKey(name: 'favicon') this.icon = '',
  });
  factory _LiveRoomLink.fromJson(Map<String, dynamic> json) =>
      _$LiveRoomLinkFromJson(json);

  @override
  @JsonKey(name: 'isPinned')
  final bool pinned;
  @override
  @JsonKey(name: 'uniqId')
  final String id;
  @override
  @JsonKey(name: 'href')
  final String href;
  @override
  @JsonKey(name: 'siteName')
  final String siteName;
  @override
  @JsonKey(name: 'title')
  final String title;
  @override
  @JsonKey(name: 'description')
  final String description;
  @override
  @JsonKey(name: 'favicon')
  final String icon;

  /// Create a copy of LiveRoomLink
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LiveRoomLinkCopyWith<_LiveRoomLink> get copyWith =>
      __$LiveRoomLinkCopyWithImpl<_LiveRoomLink>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveRoomLinkToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LiveRoomLink &&
            (identical(other.pinned, pinned) || other.pinned == pinned) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.href, href) || other.href == href) &&
            (identical(other.siteName, siteName) ||
                other.siteName == siteName) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.icon, icon) || other.icon == icon));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    pinned,
    id,
    href,
    siteName,
    title,
    description,
    icon,
  );

  @override
  String toString() {
    return 'LiveRoomLink(pinned: $pinned, id: $id, href: $href, siteName: $siteName, title: $title, description: $description, icon: $icon)';
  }
}

/// @nodoc
abstract mixin class _$LiveRoomLinkCopyWith<$Res>
    implements $LiveRoomLinkCopyWith<$Res> {
  factory _$LiveRoomLinkCopyWith(
    _LiveRoomLink value,
    $Res Function(_LiveRoomLink) _then,
  ) = __$LiveRoomLinkCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'isPinned') bool pinned,
    @JsonKey(name: 'uniqId') String id,
    @JsonKey(name: 'href') String href,
    @JsonKey(name: 'siteName') String siteName,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'favicon') String icon,
  });
}

/// @nodoc
class __$LiveRoomLinkCopyWithImpl<$Res>
    implements _$LiveRoomLinkCopyWith<$Res> {
  __$LiveRoomLinkCopyWithImpl(this._self, this._then);

  final _LiveRoomLink _self;
  final $Res Function(_LiveRoomLink) _then;

  /// Create a copy of LiveRoomLink
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? pinned = null,
    Object? id = null,
    Object? href = null,
    Object? siteName = null,
    Object? title = null,
    Object? description = null,
    Object? icon = null,
  }) {
    return _then(
      _LiveRoomLink(
        pinned: null == pinned
            ? _self.pinned
            : pinned // ignore: cast_nullable_to_non_nullable
                  as bool,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        href: null == href
            ? _self.href
            : href // ignore: cast_nullable_to_non_nullable
                  as String,
        siteName: null == siteName
            ? _self.siteName
            : siteName // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$LiveRPCRequest {
  @JsonKey(name: 'v')
  String get version;
  @EpochDateTimeConverter()
  @JsonKey(name: 't')
  DateTime get timestamp;
  @JsonKey(name: 'n')
  String get nonce;
  @LiveMethodConverter()
  @JsonKey(name: 'm')
  LiveRPCMethod? get method;
  @JsonKey(name: 'p')
  Map<String, Object?> get params;

  /// Create a copy of LiveRPCRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveRPCRequestCopyWith<LiveRPCRequest> get copyWith =>
      _$LiveRPCRequestCopyWithImpl<LiveRPCRequest>(
        this as LiveRPCRequest,
        _$identity,
      );

  /// Serializes this LiveRPCRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveRPCRequest &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.nonce, nonce) || other.nonce == nonce) &&
            (identical(other.method, method) || other.method == method) &&
            const DeepCollectionEquality().equals(other.params, params));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    version,
    timestamp,
    nonce,
    method,
    const DeepCollectionEquality().hash(params),
  );

  @override
  String toString() {
    return 'LiveRPCRequest(version: $version, timestamp: $timestamp, nonce: $nonce, method: $method, params: $params)';
  }
}

/// @nodoc
abstract mixin class $LiveRPCRequestCopyWith<$Res> {
  factory $LiveRPCRequestCopyWith(
    LiveRPCRequest value,
    $Res Function(LiveRPCRequest) _then,
  ) = _$LiveRPCRequestCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'v') String version,
    @EpochDateTimeConverter() @JsonKey(name: 't') DateTime timestamp,
    @JsonKey(name: 'n') String nonce,
    @LiveMethodConverter() @JsonKey(name: 'm') LiveRPCMethod? method,
    @JsonKey(name: 'p') Map<String, Object?> params,
  });
}

/// @nodoc
class _$LiveRPCRequestCopyWithImpl<$Res>
    implements $LiveRPCRequestCopyWith<$Res> {
  _$LiveRPCRequestCopyWithImpl(this._self, this._then);

  final LiveRPCRequest _self;
  final $Res Function(LiveRPCRequest) _then;

  /// Create a copy of LiveRPCRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? version = null,
    Object? timestamp = null,
    Object? nonce = null,
    Object? method = freezed,
    Object? params = null,
  }) {
    return _then(
      _self.copyWith(
        version: null == version
            ? _self.version
            : version // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _self.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        nonce: null == nonce
            ? _self.nonce
            : nonce // ignore: cast_nullable_to_non_nullable
                  as String,
        method: freezed == method
            ? _self.method
            : method // ignore: cast_nullable_to_non_nullable
                  as LiveRPCMethod?,
        params: null == params
            ? _self.params
            : params // ignore: cast_nullable_to_non_nullable
                  as Map<String, Object?>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _LiveRPCRequest extends LiveRPCRequest {
  const _LiveRPCRequest({
    @JsonKey(name: 'v') required this.version,
    @EpochDateTimeConverter() @JsonKey(name: 't') required this.timestamp,
    @JsonKey(name: 'n') required this.nonce,
    @LiveMethodConverter() @JsonKey(name: 'm') required this.method,
    @JsonKey(name: 'p') required final Map<String, Object?> params,
  }) : _params = params,
       super._();
  factory _LiveRPCRequest.fromJson(Map<String, dynamic> json) =>
      _$LiveRPCRequestFromJson(json);

  @override
  @JsonKey(name: 'v')
  final String version;
  @override
  @EpochDateTimeConverter()
  @JsonKey(name: 't')
  final DateTime timestamp;
  @override
  @JsonKey(name: 'n')
  final String nonce;
  @override
  @LiveMethodConverter()
  @JsonKey(name: 'm')
  final LiveRPCMethod? method;
  final Map<String, Object?> _params;
  @override
  @JsonKey(name: 'p')
  Map<String, Object?> get params {
    if (_params is EqualUnmodifiableMapView) return _params;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_params);
  }

  /// Create a copy of LiveRPCRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LiveRPCRequestCopyWith<_LiveRPCRequest> get copyWith =>
      __$LiveRPCRequestCopyWithImpl<_LiveRPCRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveRPCRequestToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LiveRPCRequest &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.nonce, nonce) || other.nonce == nonce) &&
            (identical(other.method, method) || other.method == method) &&
            const DeepCollectionEquality().equals(other._params, _params));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    version,
    timestamp,
    nonce,
    method,
    const DeepCollectionEquality().hash(_params),
  );

  @override
  String toString() {
    return 'LiveRPCRequest(version: $version, timestamp: $timestamp, nonce: $nonce, method: $method, params: $params)';
  }
}

/// @nodoc
abstract mixin class _$LiveRPCRequestCopyWith<$Res>
    implements $LiveRPCRequestCopyWith<$Res> {
  factory _$LiveRPCRequestCopyWith(
    _LiveRPCRequest value,
    $Res Function(_LiveRPCRequest) _then,
  ) = __$LiveRPCRequestCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'v') String version,
    @EpochDateTimeConverter() @JsonKey(name: 't') DateTime timestamp,
    @JsonKey(name: 'n') String nonce,
    @LiveMethodConverter() @JsonKey(name: 'm') LiveRPCMethod? method,
    @JsonKey(name: 'p') Map<String, Object?> params,
  });
}

/// @nodoc
class __$LiveRPCRequestCopyWithImpl<$Res>
    implements _$LiveRPCRequestCopyWith<$Res> {
  __$LiveRPCRequestCopyWithImpl(this._self, this._then);

  final _LiveRPCRequest _self;
  final $Res Function(_LiveRPCRequest) _then;

  /// Create a copy of LiveRPCRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? version = null,
    Object? timestamp = null,
    Object? nonce = null,
    Object? method = freezed,
    Object? params = null,
  }) {
    return _then(
      _LiveRPCRequest(
        version: null == version
            ? _self.version
            : version // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _self.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        nonce: null == nonce
            ? _self.nonce
            : nonce // ignore: cast_nullable_to_non_nullable
                  as String,
        method: freezed == method
            ? _self.method
            : method // ignore: cast_nullable_to_non_nullable
                  as LiveRPCMethod?,
        params: null == params
            ? _self._params
            : params // ignore: cast_nullable_to_non_nullable
                  as Map<String, Object?>,
      ),
    );
  }
}

LiveControl _$LiveControlFromJson(Map<String, dynamic> json) {
  switch (json['type']) {
    case 'closeRoom':
      return LiveControlCloseRoom.fromJson(json);
    case 'kickUser':
      return LiveControlKickUser.fromJson(json);
    case 'pinnedCA':
      return LiveControlPinnedCA.fromJson(json);
    case 'unpinnedCA':
      return LiveControlUnpinnedCA.fromJson(json);
    case 'pushedCA':
      return LiveControlPushedCA.fromJson(json);
    case 'pinnedLink':
      return LiveControlPinnedLink.fromJson(json);
    case 'unpinnedLink':
      return LiveControlUnpinnedLink.fromJson(json);
    case 'pushedLink':
      return LiveControlPushedLink.fromJson(json);
    case 'requestMicrophone':
      return LiveControlRequestMicrophone.fromJson(json);
    case 'updateMicrophone':
      return LiveControlUpdateMicrophone.fromJson(json);
    case 'updateChatAbility':
      return LiveControlUpdateChatAbility.fromJson(json);

    default:
      throw CheckedFromJsonException(
        json,
        'type',
        'LiveControl',
        'Invalid union type "${json['type']}"!',
      );
  }
}

/// @nodoc
mixin _$LiveControl {
  @EpochDateTimeConverter()
  @JsonKey(name: 'createAt')
  DateTime get createAt;
  @JsonKey(name: 'username')
  String get username;
  @JsonUserIdConverter()
  @JsonKey(name: 'userId')
  int get userId;
  @JsonKey(name: 'userAvatar')
  String get userAvatar;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveControlCopyWith<LiveControl> get copyWith =>
      _$LiveControlCopyWithImpl<LiveControl>(this as LiveControl, _$identity);

  /// Serializes this LiveControl to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveControl &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createAt, username, userId, userAvatar);

  @override
  String toString() {
    return 'LiveControl(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar)';
  }
}

/// @nodoc
abstract mixin class $LiveControlCopyWith<$Res> {
  factory $LiveControlCopyWith(
    LiveControl value,
    $Res Function(LiveControl) _then,
  ) = _$LiveControlCopyWithImpl;
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
  });
}

/// @nodoc
class _$LiveControlCopyWithImpl<$Res> implements $LiveControlCopyWith<$Res> {
  _$LiveControlCopyWithImpl(this._self, this._then);

  final LiveControl _self;
  final $Res Function(LiveControl) _then;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
  }) {
    return _then(
      _self.copyWith(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class LiveControlCloseRoom extends LiveControl {
  const LiveControlCloseRoom({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required final DateTime createAt,
    @JsonKey(name: 'username') required final String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required final int userId,
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    final String? $type,
  }) : $type = $type ?? 'closeRoom',
       super._(createAt: createAt, username: username, userId: userId);
  factory LiveControlCloseRoom.fromJson(Map<String, dynamic> json) =>
      _$LiveControlCloseRoomFromJson(json);

  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveControlCloseRoomCopyWith<LiveControlCloseRoom> get copyWith =>
      _$LiveControlCloseRoomCopyWithImpl<LiveControlCloseRoom>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$LiveControlCloseRoomToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveControlCloseRoom &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createAt, username, userId, userAvatar);

  @override
  String toString() {
    return 'LiveControl.closeRoom(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar)';
  }
}

/// @nodoc
abstract mixin class $LiveControlCloseRoomCopyWith<$Res>
    implements $LiveControlCopyWith<$Res> {
  factory $LiveControlCloseRoomCopyWith(
    LiveControlCloseRoom value,
    $Res Function(LiveControlCloseRoom) _then,
  ) = _$LiveControlCloseRoomCopyWithImpl;
  @override
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
  });
}

/// @nodoc
class _$LiveControlCloseRoomCopyWithImpl<$Res>
    implements $LiveControlCloseRoomCopyWith<$Res> {
  _$LiveControlCloseRoomCopyWithImpl(this._self, this._then);

  final LiveControlCloseRoom _self;
  final $Res Function(LiveControlCloseRoom) _then;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
  }) {
    return _then(
      LiveControlCloseRoom(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class LiveControlKickUser extends LiveControl {
  const LiveControlKickUser({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required final DateTime createAt,
    @JsonKey(name: 'username') required final String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required final int userId,
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    @JsonKey(name: 'uuid') required this.uuid,
    final String? $type,
  }) : $type = $type ?? 'kickUser',
       super._(createAt: createAt, username: username, userId: userId);
  factory LiveControlKickUser.fromJson(Map<String, dynamic> json) =>
      _$LiveControlKickUserFromJson(json);

  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;
  @JsonKey(name: 'uuid')
  final String uuid;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveControlKickUserCopyWith<LiveControlKickUser> get copyWith =>
      _$LiveControlKickUserCopyWithImpl<LiveControlKickUser>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveControlKickUserToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveControlKickUser &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.uuid, uuid) || other.uuid == uuid));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createAt, username, userId, userAvatar, uuid);

  @override
  String toString() {
    return 'LiveControl.kickUser(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar, uuid: $uuid)';
  }
}

/// @nodoc
abstract mixin class $LiveControlKickUserCopyWith<$Res>
    implements $LiveControlCopyWith<$Res> {
  factory $LiveControlKickUserCopyWith(
    LiveControlKickUser value,
    $Res Function(LiveControlKickUser) _then,
  ) = _$LiveControlKickUserCopyWithImpl;
  @override
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'uuid') String uuid,
  });
}

/// @nodoc
class _$LiveControlKickUserCopyWithImpl<$Res>
    implements $LiveControlKickUserCopyWith<$Res> {
  _$LiveControlKickUserCopyWithImpl(this._self, this._then);

  final LiveControlKickUser _self;
  final $Res Function(LiveControlKickUser) _then;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
    Object? uuid = null,
  }) {
    return _then(
      LiveControlKickUser(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        uuid: null == uuid
            ? _self.uuid
            : uuid // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class LiveControlPinnedCA extends LiveControl {
  const LiveControlPinnedCA({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required final DateTime createAt,
    @JsonKey(name: 'username') required final String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required final int userId,
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    @JsonKey(name: 'address') required this.address,
    @JsonKey(name: 'chain') required this.chain,
    @JsonKey(name: 'icon') this.icon = '',
    @JsonKey(name: 'name') this.name,
    @JsonKey(name: 'symbol') this.symbol,
    final String? $type,
  }) : $type = $type ?? 'pinnedCA',
       super._(createAt: createAt, username: username, userId: userId);
  factory LiveControlPinnedCA.fromJson(Map<String, dynamic> json) =>
      _$LiveControlPinnedCAFromJson(json);

  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;
  @JsonKey(name: 'address')
  final String address;
  @JsonKey(name: 'chain')
  final String chain;
  @JsonKey(name: 'icon')
  final String icon;
  @JsonKey(name: 'name')
  final String? name;
  @JsonKey(name: 'symbol')
  final String? symbol;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveControlPinnedCACopyWith<LiveControlPinnedCA> get copyWith =>
      _$LiveControlPinnedCACopyWithImpl<LiveControlPinnedCA>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveControlPinnedCAToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveControlPinnedCA &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.chain, chain) || other.chain == chain) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.symbol, symbol) || other.symbol == symbol));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    createAt,
    username,
    userId,
    userAvatar,
    address,
    chain,
    icon,
    name,
    symbol,
  );

  @override
  String toString() {
    return 'LiveControl.pinnedCA(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar, address: $address, chain: $chain, icon: $icon, name: $name, symbol: $symbol)';
  }
}

/// @nodoc
abstract mixin class $LiveControlPinnedCACopyWith<$Res>
    implements $LiveControlCopyWith<$Res> {
  factory $LiveControlPinnedCACopyWith(
    LiveControlPinnedCA value,
    $Res Function(LiveControlPinnedCA) _then,
  ) = _$LiveControlPinnedCACopyWithImpl;
  @override
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'address') String address,
    @JsonKey(name: 'chain') String chain,
    @JsonKey(name: 'icon') String icon,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: 'symbol') String? symbol,
  });
}

/// @nodoc
class _$LiveControlPinnedCACopyWithImpl<$Res>
    implements $LiveControlPinnedCACopyWith<$Res> {
  _$LiveControlPinnedCACopyWithImpl(this._self, this._then);

  final LiveControlPinnedCA _self;
  final $Res Function(LiveControlPinnedCA) _then;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
    Object? address = null,
    Object? chain = null,
    Object? icon = null,
    Object? name = freezed,
    Object? symbol = freezed,
  }) {
    return _then(
      LiveControlPinnedCA(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        address: null == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        chain: null == chain
            ? _self.chain
            : chain // ignore: cast_nullable_to_non_nullable
                  as String,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
        name: freezed == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String?,
        symbol: freezed == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class LiveControlUnpinnedCA extends LiveControl {
  const LiveControlUnpinnedCA({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required final DateTime createAt,
    @JsonKey(name: 'username') required final String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required final int userId,
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    @JsonKey(name: 'address') required this.address,
    @JsonKey(name: 'chain') required this.chain,
    @JsonKey(name: 'icon') this.icon = '',
    @JsonKey(name: 'name') this.name,
    @JsonKey(name: 'symbol') this.symbol,
    final String? $type,
  }) : $type = $type ?? 'unpinnedCA',
       super._(createAt: createAt, username: username, userId: userId);
  factory LiveControlUnpinnedCA.fromJson(Map<String, dynamic> json) =>
      _$LiveControlUnpinnedCAFromJson(json);

  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;
  @JsonKey(name: 'address')
  final String address;
  @JsonKey(name: 'chain')
  final String chain;
  @JsonKey(name: 'icon')
  final String icon;
  @JsonKey(name: 'name')
  final String? name;
  @JsonKey(name: 'symbol')
  final String? symbol;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveControlUnpinnedCACopyWith<LiveControlUnpinnedCA> get copyWith =>
      _$LiveControlUnpinnedCACopyWithImpl<LiveControlUnpinnedCA>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$LiveControlUnpinnedCAToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveControlUnpinnedCA &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.chain, chain) || other.chain == chain) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.symbol, symbol) || other.symbol == symbol));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    createAt,
    username,
    userId,
    userAvatar,
    address,
    chain,
    icon,
    name,
    symbol,
  );

  @override
  String toString() {
    return 'LiveControl.unpinnedCA(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar, address: $address, chain: $chain, icon: $icon, name: $name, symbol: $symbol)';
  }
}

/// @nodoc
abstract mixin class $LiveControlUnpinnedCACopyWith<$Res>
    implements $LiveControlCopyWith<$Res> {
  factory $LiveControlUnpinnedCACopyWith(
    LiveControlUnpinnedCA value,
    $Res Function(LiveControlUnpinnedCA) _then,
  ) = _$LiveControlUnpinnedCACopyWithImpl;
  @override
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'address') String address,
    @JsonKey(name: 'chain') String chain,
    @JsonKey(name: 'icon') String icon,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: 'symbol') String? symbol,
  });
}

/// @nodoc
class _$LiveControlUnpinnedCACopyWithImpl<$Res>
    implements $LiveControlUnpinnedCACopyWith<$Res> {
  _$LiveControlUnpinnedCACopyWithImpl(this._self, this._then);

  final LiveControlUnpinnedCA _self;
  final $Res Function(LiveControlUnpinnedCA) _then;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
    Object? address = null,
    Object? chain = null,
    Object? icon = null,
    Object? name = freezed,
    Object? symbol = freezed,
  }) {
    return _then(
      LiveControlUnpinnedCA(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        address: null == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        chain: null == chain
            ? _self.chain
            : chain // ignore: cast_nullable_to_non_nullable
                  as String,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
        name: freezed == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String?,
        symbol: freezed == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class LiveControlPushedCA extends LiveControl {
  const LiveControlPushedCA({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required final DateTime createAt,
    @JsonKey(name: 'username') required final String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required final int userId,
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    @JsonKey(name: 'address') required this.address,
    @JsonKey(name: 'chain') required this.chain,
    @JsonKey(name: 'icon') this.icon = '',
    @JsonKey(name: 'name') this.name,
    @JsonKey(name: 'symbol') this.symbol,
    final String? $type,
  }) : $type = $type ?? 'pushedCA',
       super._(createAt: createAt, username: username, userId: userId);
  factory LiveControlPushedCA.fromJson(Map<String, dynamic> json) =>
      _$LiveControlPushedCAFromJson(json);

  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;
  @JsonKey(name: 'address')
  final String address;
  @JsonKey(name: 'chain')
  final String chain;
  @JsonKey(name: 'icon')
  final String icon;
  @JsonKey(name: 'name')
  final String? name;
  @JsonKey(name: 'symbol')
  final String? symbol;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveControlPushedCACopyWith<LiveControlPushedCA> get copyWith =>
      _$LiveControlPushedCACopyWithImpl<LiveControlPushedCA>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveControlPushedCAToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveControlPushedCA &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.chain, chain) || other.chain == chain) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.symbol, symbol) || other.symbol == symbol));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    createAt,
    username,
    userId,
    userAvatar,
    address,
    chain,
    icon,
    name,
    symbol,
  );

  @override
  String toString() {
    return 'LiveControl.pushedCA(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar, address: $address, chain: $chain, icon: $icon, name: $name, symbol: $symbol)';
  }
}

/// @nodoc
abstract mixin class $LiveControlPushedCACopyWith<$Res>
    implements $LiveControlCopyWith<$Res> {
  factory $LiveControlPushedCACopyWith(
    LiveControlPushedCA value,
    $Res Function(LiveControlPushedCA) _then,
  ) = _$LiveControlPushedCACopyWithImpl;
  @override
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'address') String address,
    @JsonKey(name: 'chain') String chain,
    @JsonKey(name: 'icon') String icon,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: 'symbol') String? symbol,
  });
}

/// @nodoc
class _$LiveControlPushedCACopyWithImpl<$Res>
    implements $LiveControlPushedCACopyWith<$Res> {
  _$LiveControlPushedCACopyWithImpl(this._self, this._then);

  final LiveControlPushedCA _self;
  final $Res Function(LiveControlPushedCA) _then;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
    Object? address = null,
    Object? chain = null,
    Object? icon = null,
    Object? name = freezed,
    Object? symbol = freezed,
  }) {
    return _then(
      LiveControlPushedCA(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        address: null == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        chain: null == chain
            ? _self.chain
            : chain // ignore: cast_nullable_to_non_nullable
                  as String,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
        name: freezed == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String?,
        symbol: freezed == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class LiveControlPinnedLink extends LiveControl {
  const LiveControlPinnedLink({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required final DateTime createAt,
    @JsonKey(name: 'username') required final String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required final int userId,
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    @JsonKey(name: 'url') required this.url,
    @JsonKey(name: 'icon') this.icon = '',
    @JsonKey(name: 'title') this.title = '',
    @JsonKey(name: 'description') this.description = '',
    @JsonKey(name: 'siteName') this.siteName = '',
    final String? $type,
  }) : $type = $type ?? 'pinnedLink',
       super._(createAt: createAt, username: username, userId: userId);
  factory LiveControlPinnedLink.fromJson(Map<String, dynamic> json) =>
      _$LiveControlPinnedLinkFromJson(json);

  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;
  @JsonKey(name: 'url')
  final String url;
  @JsonKey(name: 'icon')
  final String icon;
  @JsonKey(name: 'title')
  final String title;
  @JsonKey(name: 'description')
  final String description;
  @JsonKey(name: 'siteName')
  final String siteName;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveControlPinnedLinkCopyWith<LiveControlPinnedLink> get copyWith =>
      _$LiveControlPinnedLinkCopyWithImpl<LiveControlPinnedLink>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$LiveControlPinnedLinkToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveControlPinnedLink &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.siteName, siteName) ||
                other.siteName == siteName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    createAt,
    username,
    userId,
    userAvatar,
    url,
    icon,
    title,
    description,
    siteName,
  );

  @override
  String toString() {
    return 'LiveControl.pinnedLink(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar, url: $url, icon: $icon, title: $title, description: $description, siteName: $siteName)';
  }
}

/// @nodoc
abstract mixin class $LiveControlPinnedLinkCopyWith<$Res>
    implements $LiveControlCopyWith<$Res> {
  factory $LiveControlPinnedLinkCopyWith(
    LiveControlPinnedLink value,
    $Res Function(LiveControlPinnedLink) _then,
  ) = _$LiveControlPinnedLinkCopyWithImpl;
  @override
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'url') String url,
    @JsonKey(name: 'icon') String icon,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'siteName') String siteName,
  });
}

/// @nodoc
class _$LiveControlPinnedLinkCopyWithImpl<$Res>
    implements $LiveControlPinnedLinkCopyWith<$Res> {
  _$LiveControlPinnedLinkCopyWithImpl(this._self, this._then);

  final LiveControlPinnedLink _self;
  final $Res Function(LiveControlPinnedLink) _then;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
    Object? url = null,
    Object? icon = null,
    Object? title = null,
    Object? description = null,
    Object? siteName = null,
  }) {
    return _then(
      LiveControlPinnedLink(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        url: null == url
            ? _self.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        siteName: null == siteName
            ? _self.siteName
            : siteName // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class LiveControlUnpinnedLink extends LiveControl {
  const LiveControlUnpinnedLink({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required final DateTime createAt,
    @JsonKey(name: 'username') required final String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required final int userId,
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    @JsonKey(name: 'url') required this.url,
    @JsonKey(name: 'icon') this.icon = '',
    @JsonKey(name: 'title') this.title = '',
    @JsonKey(name: 'description') this.description = '',
    @JsonKey(name: 'siteName') this.siteName = '',
    final String? $type,
  }) : $type = $type ?? 'unpinnedLink',
       super._(createAt: createAt, username: username, userId: userId);
  factory LiveControlUnpinnedLink.fromJson(Map<String, dynamic> json) =>
      _$LiveControlUnpinnedLinkFromJson(json);

  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;
  @JsonKey(name: 'url')
  final String url;
  @JsonKey(name: 'icon')
  final String icon;
  @JsonKey(name: 'title')
  final String title;
  @JsonKey(name: 'description')
  final String description;
  @JsonKey(name: 'siteName')
  final String siteName;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveControlUnpinnedLinkCopyWith<LiveControlUnpinnedLink> get copyWith =>
      _$LiveControlUnpinnedLinkCopyWithImpl<LiveControlUnpinnedLink>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$LiveControlUnpinnedLinkToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveControlUnpinnedLink &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.siteName, siteName) ||
                other.siteName == siteName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    createAt,
    username,
    userId,
    userAvatar,
    url,
    icon,
    title,
    description,
    siteName,
  );

  @override
  String toString() {
    return 'LiveControl.unpinnedLink(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar, url: $url, icon: $icon, title: $title, description: $description, siteName: $siteName)';
  }
}

/// @nodoc
abstract mixin class $LiveControlUnpinnedLinkCopyWith<$Res>
    implements $LiveControlCopyWith<$Res> {
  factory $LiveControlUnpinnedLinkCopyWith(
    LiveControlUnpinnedLink value,
    $Res Function(LiveControlUnpinnedLink) _then,
  ) = _$LiveControlUnpinnedLinkCopyWithImpl;
  @override
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'url') String url,
    @JsonKey(name: 'icon') String icon,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'siteName') String siteName,
  });
}

/// @nodoc
class _$LiveControlUnpinnedLinkCopyWithImpl<$Res>
    implements $LiveControlUnpinnedLinkCopyWith<$Res> {
  _$LiveControlUnpinnedLinkCopyWithImpl(this._self, this._then);

  final LiveControlUnpinnedLink _self;
  final $Res Function(LiveControlUnpinnedLink) _then;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
    Object? url = null,
    Object? icon = null,
    Object? title = null,
    Object? description = null,
    Object? siteName = null,
  }) {
    return _then(
      LiveControlUnpinnedLink(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        url: null == url
            ? _self.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        siteName: null == siteName
            ? _self.siteName
            : siteName // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class LiveControlPushedLink extends LiveControl {
  const LiveControlPushedLink({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required final DateTime createAt,
    @JsonKey(name: 'username') required final String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required final int userId,
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    @JsonKey(name: 'url') required this.url,
    @JsonKey(name: 'icon') this.icon = '',
    @JsonKey(name: 'title') this.title = '',
    @JsonKey(name: 'description') this.description = '',
    @JsonKey(name: 'siteName') this.siteName = '',
    final String? $type,
  }) : $type = $type ?? 'pushedLink',
       super._(createAt: createAt, username: username, userId: userId);
  factory LiveControlPushedLink.fromJson(Map<String, dynamic> json) =>
      _$LiveControlPushedLinkFromJson(json);

  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;
  @JsonKey(name: 'url')
  final String url;
  @JsonKey(name: 'icon')
  final String icon;
  @JsonKey(name: 'title')
  final String title;
  @JsonKey(name: 'description')
  final String description;
  @JsonKey(name: 'siteName')
  final String siteName;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveControlPushedLinkCopyWith<LiveControlPushedLink> get copyWith =>
      _$LiveControlPushedLinkCopyWithImpl<LiveControlPushedLink>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$LiveControlPushedLinkToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveControlPushedLink &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.siteName, siteName) ||
                other.siteName == siteName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    createAt,
    username,
    userId,
    userAvatar,
    url,
    icon,
    title,
    description,
    siteName,
  );

  @override
  String toString() {
    return 'LiveControl.pushedLink(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar, url: $url, icon: $icon, title: $title, description: $description, siteName: $siteName)';
  }
}

/// @nodoc
abstract mixin class $LiveControlPushedLinkCopyWith<$Res>
    implements $LiveControlCopyWith<$Res> {
  factory $LiveControlPushedLinkCopyWith(
    LiveControlPushedLink value,
    $Res Function(LiveControlPushedLink) _then,
  ) = _$LiveControlPushedLinkCopyWithImpl;
  @override
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'url') String url,
    @JsonKey(name: 'icon') String icon,
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'siteName') String siteName,
  });
}

/// @nodoc
class _$LiveControlPushedLinkCopyWithImpl<$Res>
    implements $LiveControlPushedLinkCopyWith<$Res> {
  _$LiveControlPushedLinkCopyWithImpl(this._self, this._then);

  final LiveControlPushedLink _self;
  final $Res Function(LiveControlPushedLink) _then;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
    Object? url = null,
    Object? icon = null,
    Object? title = null,
    Object? description = null,
    Object? siteName = null,
  }) {
    return _then(
      LiveControlPushedLink(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        url: null == url
            ? _self.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        siteName: null == siteName
            ? _self.siteName
            : siteName // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class LiveControlRequestMicrophone extends LiveControl {
  const LiveControlRequestMicrophone({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required final DateTime createAt,
    @JsonKey(name: 'username') required final String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required final int userId,
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    @JsonKey(name: 'userInfo') required this.userInfo,
    final String? $type,
  }) : $type = $type ?? 'requestMicrophone',
       super._(createAt: createAt, username: username, userId: userId);
  factory LiveControlRequestMicrophone.fromJson(Map<String, dynamic> json) =>
      _$LiveControlRequestMicrophoneFromJson(json);

  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;
  @JsonKey(name: 'userInfo')
  final UserInfo userInfo;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveControlRequestMicrophoneCopyWith<LiveControlRequestMicrophone>
  get copyWith =>
      _$LiveControlRequestMicrophoneCopyWithImpl<LiveControlRequestMicrophone>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$LiveControlRequestMicrophoneToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveControlRequestMicrophone &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.userInfo, userInfo) ||
                other.userInfo == userInfo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    createAt,
    username,
    userId,
    userAvatar,
    userInfo,
  );

  @override
  String toString() {
    return 'LiveControl.requestMicrophone(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar, userInfo: $userInfo)';
  }
}

/// @nodoc
abstract mixin class $LiveControlRequestMicrophoneCopyWith<$Res>
    implements $LiveControlCopyWith<$Res> {
  factory $LiveControlRequestMicrophoneCopyWith(
    LiveControlRequestMicrophone value,
    $Res Function(LiveControlRequestMicrophone) _then,
  ) = _$LiveControlRequestMicrophoneCopyWithImpl;
  @override
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'userInfo') UserInfo userInfo,
  });

  $UserInfoCopyWith<$Res> get userInfo;
}

/// @nodoc
class _$LiveControlRequestMicrophoneCopyWithImpl<$Res>
    implements $LiveControlRequestMicrophoneCopyWith<$Res> {
  _$LiveControlRequestMicrophoneCopyWithImpl(this._self, this._then);

  final LiveControlRequestMicrophone _self;
  final $Res Function(LiveControlRequestMicrophone) _then;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
    Object? userInfo = null,
  }) {
    return _then(
      LiveControlRequestMicrophone(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        userInfo: null == userInfo
            ? _self.userInfo
            : userInfo // ignore: cast_nullable_to_non_nullable
                  as UserInfo,
      ),
    );
  }

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserInfoCopyWith<$Res> get userInfo {
    return $UserInfoCopyWith<$Res>(_self.userInfo, (value) {
      return _then(_self.copyWith(userInfo: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class LiveControlUpdateMicrophone extends LiveControl {
  const LiveControlUpdateMicrophone({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required final DateTime createAt,
    @JsonKey(name: 'username') required final String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required final int userId,
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    @JsonKey(name: 'userInfo') required this.userInfo,
    @JsonKey(name: 'enabled') required this.enabled,
    final String? $type,
  }) : $type = $type ?? 'updateMicrophone',
       super._(createAt: createAt, username: username, userId: userId);
  factory LiveControlUpdateMicrophone.fromJson(Map<String, dynamic> json) =>
      _$LiveControlUpdateMicrophoneFromJson(json);

  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;
  @JsonKey(name: 'userInfo')
  final UserInfo userInfo;
  @JsonKey(name: 'enabled')
  final bool enabled;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveControlUpdateMicrophoneCopyWith<LiveControlUpdateMicrophone>
  get copyWith =>
      _$LiveControlUpdateMicrophoneCopyWithImpl<LiveControlUpdateMicrophone>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$LiveControlUpdateMicrophoneToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveControlUpdateMicrophone &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.userInfo, userInfo) ||
                other.userInfo == userInfo) &&
            (identical(other.enabled, enabled) || other.enabled == enabled));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    createAt,
    username,
    userId,
    userAvatar,
    userInfo,
    enabled,
  );

  @override
  String toString() {
    return 'LiveControl.updateMicrophone(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar, userInfo: $userInfo, enabled: $enabled)';
  }
}

/// @nodoc
abstract mixin class $LiveControlUpdateMicrophoneCopyWith<$Res>
    implements $LiveControlCopyWith<$Res> {
  factory $LiveControlUpdateMicrophoneCopyWith(
    LiveControlUpdateMicrophone value,
    $Res Function(LiveControlUpdateMicrophone) _then,
  ) = _$LiveControlUpdateMicrophoneCopyWithImpl;
  @override
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'userInfo') UserInfo userInfo,
    @JsonKey(name: 'enabled') bool enabled,
  });

  $UserInfoCopyWith<$Res> get userInfo;
}

/// @nodoc
class _$LiveControlUpdateMicrophoneCopyWithImpl<$Res>
    implements $LiveControlUpdateMicrophoneCopyWith<$Res> {
  _$LiveControlUpdateMicrophoneCopyWithImpl(this._self, this._then);

  final LiveControlUpdateMicrophone _self;
  final $Res Function(LiveControlUpdateMicrophone) _then;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
    Object? userInfo = null,
    Object? enabled = null,
  }) {
    return _then(
      LiveControlUpdateMicrophone(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        userInfo: null == userInfo
            ? _self.userInfo
            : userInfo // ignore: cast_nullable_to_non_nullable
                  as UserInfo,
        enabled: null == enabled
            ? _self.enabled
            : enabled // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserInfoCopyWith<$Res> get userInfo {
    return $UserInfoCopyWith<$Res>(_self.userInfo, (value) {
      return _then(_self.copyWith(userInfo: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class LiveControlUpdateChatAbility extends LiveControl {
  const LiveControlUpdateChatAbility({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required final DateTime createAt,
    @JsonKey(name: 'username') required final String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required final int userId,
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    @JsonKey(name: 'enabled') required this.enabled,
    final String? $type,
  }) : $type = $type ?? 'updateChatAbility',
       super._(createAt: createAt, username: username, userId: userId);
  factory LiveControlUpdateChatAbility.fromJson(Map<String, dynamic> json) =>
      _$LiveControlUpdateChatAbilityFromJson(json);

  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;
  @JsonKey(name: 'enabled')
  final bool enabled;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveControlUpdateChatAbilityCopyWith<LiveControlUpdateChatAbility>
  get copyWith =>
      _$LiveControlUpdateChatAbilityCopyWithImpl<LiveControlUpdateChatAbility>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$LiveControlUpdateChatAbilityToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveControlUpdateChatAbility &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.enabled, enabled) || other.enabled == enabled));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createAt, username, userId, userAvatar, enabled);

  @override
  String toString() {
    return 'LiveControl.updateChatAbility(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar, enabled: $enabled)';
  }
}

/// @nodoc
abstract mixin class $LiveControlUpdateChatAbilityCopyWith<$Res>
    implements $LiveControlCopyWith<$Res> {
  factory $LiveControlUpdateChatAbilityCopyWith(
    LiveControlUpdateChatAbility value,
    $Res Function(LiveControlUpdateChatAbility) _then,
  ) = _$LiveControlUpdateChatAbilityCopyWithImpl;
  @override
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'enabled') bool enabled,
  });
}

/// @nodoc
class _$LiveControlUpdateChatAbilityCopyWithImpl<$Res>
    implements $LiveControlUpdateChatAbilityCopyWith<$Res> {
  _$LiveControlUpdateChatAbilityCopyWithImpl(this._self, this._then);

  final LiveControlUpdateChatAbility _self;
  final $Res Function(LiveControlUpdateChatAbility) _then;

  /// Create a copy of LiveControl
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
    Object? enabled = null,
  }) {
    return _then(
      LiveControlUpdateChatAbility(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        enabled: null == enabled
            ? _self.enabled
            : enabled // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

LiveMessage _$LiveMessageFromJson(Map<String, dynamic> json) {
  switch (json['type']) {
    case 'plain':
      return LiveMessagePlain.fromJson(json);
    case 'trade':
      return LiveMessageTrade.fromJson(json);
    case 'ca':
      return LiveMessageCA.fromJson(json);

    default:
      throw CheckedFromJsonException(
        json,
        'type',
        'LiveMessage',
        'Invalid union type "${json['type']}"!',
      );
  }
}

/// @nodoc
mixin _$LiveMessage {
  @EpochDateTimeConverter()
  @JsonKey(name: 'createAt')
  DateTime get createAt;
  @JsonKey(name: 'username')
  String get username;
  @JsonUserIdConverter()
  @JsonKey(name: 'userId')
  int get userId;
  @JsonKey(name: 'userAvatar')
  String get userAvatar;

  /// Create a copy of LiveMessage
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveMessageCopyWith<LiveMessage> get copyWith =>
      _$LiveMessageCopyWithImpl<LiveMessage>(this as LiveMessage, _$identity);

  /// Serializes this LiveMessage to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveMessage &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createAt, username, userId, userAvatar);

  @override
  String toString() {
    return 'LiveMessage(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar)';
  }
}

/// @nodoc
abstract mixin class $LiveMessageCopyWith<$Res> {
  factory $LiveMessageCopyWith(
    LiveMessage value,
    $Res Function(LiveMessage) _then,
  ) = _$LiveMessageCopyWithImpl;
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
  });
}

/// @nodoc
class _$LiveMessageCopyWithImpl<$Res> implements $LiveMessageCopyWith<$Res> {
  _$LiveMessageCopyWithImpl(this._self, this._then);

  final LiveMessage _self;
  final $Res Function(LiveMessage) _then;

  /// Create a copy of LiveMessage
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
  }) {
    return _then(
      _self.copyWith(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class LiveMessagePlain extends LiveMessage {
  const LiveMessagePlain({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required final DateTime createAt,
    @JsonKey(name: 'username') required final String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required final int userId,
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    @JsonKey(name: 'text') required this.text,
    final String? $type,
  }) : $type = $type ?? 'plain',
       super._(createAt: createAt, username: username, userId: userId);
  factory LiveMessagePlain.fromJson(Map<String, dynamic> json) =>
      _$LiveMessagePlainFromJson(json);

  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;
  @JsonKey(name: 'text')
  final String text;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of LiveMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveMessagePlainCopyWith<LiveMessagePlain> get copyWith =>
      _$LiveMessagePlainCopyWithImpl<LiveMessagePlain>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveMessagePlainToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveMessagePlain &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.text, text) || other.text == text));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createAt, username, userId, userAvatar, text);

  @override
  String toString() {
    return 'LiveMessage.plain(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar, text: $text)';
  }
}

/// @nodoc
abstract mixin class $LiveMessagePlainCopyWith<$Res>
    implements $LiveMessageCopyWith<$Res> {
  factory $LiveMessagePlainCopyWith(
    LiveMessagePlain value,
    $Res Function(LiveMessagePlain) _then,
  ) = _$LiveMessagePlainCopyWithImpl;
  @override
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'text') String text,
  });
}

/// @nodoc
class _$LiveMessagePlainCopyWithImpl<$Res>
    implements $LiveMessagePlainCopyWith<$Res> {
  _$LiveMessagePlainCopyWithImpl(this._self, this._then);

  final LiveMessagePlain _self;
  final $Res Function(LiveMessagePlain) _then;

  /// Create a copy of LiveMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
    Object? text = null,
  }) {
    return _then(
      LiveMessagePlain(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        text: null == text
            ? _self.text
            : text // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class LiveMessageTrade extends LiveMessage {
  const LiveMessageTrade({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required final DateTime createAt,
    @JsonKey(name: 'username') required final String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required final int userId,
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    @JsonKey(name: 'direction') required this.direction,
    @JsonKey(name: 'tokenLogo') this.tokenLogo = '',
    @JsonKey(name: 'tokenAmount') required this.tokenAmount,
    @JsonKey(name: 'tokenSymbol') required this.tokenSymbol,
    @JsonKey(name: 'tokenAddress') required this.tokenAddress,
    @JsonKey(name: 'spentTokenLogo') this.spentTokenLogo = '',
    @JsonKey(name: 'spentTokenAmount') required this.spentTokenAmount,
    @JsonKey(name: 'spentTokenSymbol') required this.spentTokenSymbol,
    @JsonKey(name: 'chain') required this.chain,
    final String? $type,
  }) : $type = $type ?? 'trade',
       super._(createAt: createAt, username: username, userId: userId);
  factory LiveMessageTrade.fromJson(Map<String, dynamic> json) =>
      _$LiveMessageTradeFromJson(json);

  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;
  @JsonKey(name: 'direction')
  final EnumTokenTradeDirection direction;
  @JsonKey(name: 'tokenLogo')
  final String tokenLogo;
  @JsonKey(name: 'tokenAmount')
  final Decimal tokenAmount;
  @JsonKey(name: 'tokenSymbol')
  final String tokenSymbol;
  @JsonKey(name: 'tokenAddress')
  final String tokenAddress;
  @JsonKey(name: 'spentTokenLogo')
  final String spentTokenLogo;
  @JsonKey(name: 'spentTokenAmount')
  final Decimal spentTokenAmount;
  @JsonKey(name: 'spentTokenSymbol')
  final String spentTokenSymbol;
  @JsonKey(name: 'chain')
  final String chain;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of LiveMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveMessageTradeCopyWith<LiveMessageTrade> get copyWith =>
      _$LiveMessageTradeCopyWithImpl<LiveMessageTrade>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveMessageTradeToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveMessageTrade &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.direction, direction) ||
                other.direction == direction) &&
            (identical(other.tokenLogo, tokenLogo) ||
                other.tokenLogo == tokenLogo) &&
            (identical(other.tokenAmount, tokenAmount) ||
                other.tokenAmount == tokenAmount) &&
            (identical(other.tokenSymbol, tokenSymbol) ||
                other.tokenSymbol == tokenSymbol) &&
            (identical(other.tokenAddress, tokenAddress) ||
                other.tokenAddress == tokenAddress) &&
            (identical(other.spentTokenLogo, spentTokenLogo) ||
                other.spentTokenLogo == spentTokenLogo) &&
            (identical(other.spentTokenAmount, spentTokenAmount) ||
                other.spentTokenAmount == spentTokenAmount) &&
            (identical(other.spentTokenSymbol, spentTokenSymbol) ||
                other.spentTokenSymbol == spentTokenSymbol) &&
            (identical(other.chain, chain) || other.chain == chain));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    createAt,
    username,
    userId,
    userAvatar,
    direction,
    tokenLogo,
    tokenAmount,
    tokenSymbol,
    tokenAddress,
    spentTokenLogo,
    spentTokenAmount,
    spentTokenSymbol,
    chain,
  );

  @override
  String toString() {
    return 'LiveMessage.trade(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar, direction: $direction, tokenLogo: $tokenLogo, tokenAmount: $tokenAmount, tokenSymbol: $tokenSymbol, tokenAddress: $tokenAddress, spentTokenLogo: $spentTokenLogo, spentTokenAmount: $spentTokenAmount, spentTokenSymbol: $spentTokenSymbol, chain: $chain)';
  }
}

/// @nodoc
abstract mixin class $LiveMessageTradeCopyWith<$Res>
    implements $LiveMessageCopyWith<$Res> {
  factory $LiveMessageTradeCopyWith(
    LiveMessageTrade value,
    $Res Function(LiveMessageTrade) _then,
  ) = _$LiveMessageTradeCopyWithImpl;
  @override
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'direction') EnumTokenTradeDirection direction,
    @JsonKey(name: 'tokenLogo') String tokenLogo,
    @JsonKey(name: 'tokenAmount') Decimal tokenAmount,
    @JsonKey(name: 'tokenSymbol') String tokenSymbol,
    @JsonKey(name: 'tokenAddress') String tokenAddress,
    @JsonKey(name: 'spentTokenLogo') String spentTokenLogo,
    @JsonKey(name: 'spentTokenAmount') Decimal spentTokenAmount,
    @JsonKey(name: 'spentTokenSymbol') String spentTokenSymbol,
    @JsonKey(name: 'chain') String chain,
  });
}

/// @nodoc
class _$LiveMessageTradeCopyWithImpl<$Res>
    implements $LiveMessageTradeCopyWith<$Res> {
  _$LiveMessageTradeCopyWithImpl(this._self, this._then);

  final LiveMessageTrade _self;
  final $Res Function(LiveMessageTrade) _then;

  /// Create a copy of LiveMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
    Object? direction = null,
    Object? tokenLogo = null,
    Object? tokenAmount = null,
    Object? tokenSymbol = null,
    Object? tokenAddress = null,
    Object? spentTokenLogo = null,
    Object? spentTokenAmount = null,
    Object? spentTokenSymbol = null,
    Object? chain = null,
  }) {
    return _then(
      LiveMessageTrade(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        direction: null == direction
            ? _self.direction
            : direction // ignore: cast_nullable_to_non_nullable
                  as EnumTokenTradeDirection,
        tokenLogo: null == tokenLogo
            ? _self.tokenLogo
            : tokenLogo // ignore: cast_nullable_to_non_nullable
                  as String,
        tokenAmount: null == tokenAmount
            ? _self.tokenAmount
            : tokenAmount // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        tokenSymbol: null == tokenSymbol
            ? _self.tokenSymbol
            : tokenSymbol // ignore: cast_nullable_to_non_nullable
                  as String,
        tokenAddress: null == tokenAddress
            ? _self.tokenAddress
            : tokenAddress // ignore: cast_nullable_to_non_nullable
                  as String,
        spentTokenLogo: null == spentTokenLogo
            ? _self.spentTokenLogo
            : spentTokenLogo // ignore: cast_nullable_to_non_nullable
                  as String,
        spentTokenAmount: null == spentTokenAmount
            ? _self.spentTokenAmount
            : spentTokenAmount // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        spentTokenSymbol: null == spentTokenSymbol
            ? _self.spentTokenSymbol
            : spentTokenSymbol // ignore: cast_nullable_to_non_nullable
                  as String,
        chain: null == chain
            ? _self.chain
            : chain // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class LiveMessageCA extends LiveMessage {
  const LiveMessageCA({
    @EpochDateTimeConverter()
    @JsonKey(name: 'createAt')
    required final DateTime createAt,
    @JsonKey(name: 'username') required final String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') required final int userId,
    @JsonKey(name: 'userAvatar') this.userAvatar = '',
    @JsonKey(name: 'address') required this.address,
    @JsonKey(name: 'chain') required this.chain,
    @JsonKey(name: 'icon') this.icon = '',
    @JsonKey(name: 'name') this.name,
    @JsonKey(name: 'symbol') this.symbol,
    final String? $type,
  }) : $type = $type ?? 'ca',
       super._(createAt: createAt, username: username, userId: userId);
  factory LiveMessageCA.fromJson(Map<String, dynamic> json) =>
      _$LiveMessageCAFromJson(json);

  @override
  @JsonKey(name: 'userAvatar')
  final String userAvatar;
  @JsonKey(name: 'address')
  final String address;
  @JsonKey(name: 'chain')
  final String chain;
  @JsonKey(name: 'icon')
  final String icon;
  @JsonKey(name: 'name')
  final String? name;
  @JsonKey(name: 'symbol')
  final String? symbol;

  @JsonKey(name: 'type')
  final String $type;

  /// Create a copy of LiveMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LiveMessageCACopyWith<LiveMessageCA> get copyWith =>
      _$LiveMessageCACopyWithImpl<LiveMessageCA>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LiveMessageCAToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LiveMessageCA &&
            (identical(other.createAt, createAt) ||
                other.createAt == createAt) &&
            (identical(other.username, username) ||
                other.username == username) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.userAvatar, userAvatar) ||
                other.userAvatar == userAvatar) &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.chain, chain) || other.chain == chain) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.symbol, symbol) || other.symbol == symbol));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    createAt,
    username,
    userId,
    userAvatar,
    address,
    chain,
    icon,
    name,
    symbol,
  );

  @override
  String toString() {
    return 'LiveMessage.ca(createAt: $createAt, username: $username, userId: $userId, userAvatar: $userAvatar, address: $address, chain: $chain, icon: $icon, name: $name, symbol: $symbol)';
  }
}

/// @nodoc
abstract mixin class $LiveMessageCACopyWith<$Res>
    implements $LiveMessageCopyWith<$Res> {
  factory $LiveMessageCACopyWith(
    LiveMessageCA value,
    $Res Function(LiveMessageCA) _then,
  ) = _$LiveMessageCACopyWithImpl;
  @override
  @useResult
  $Res call({
    @EpochDateTimeConverter() @JsonKey(name: 'createAt') DateTime createAt,
    @JsonKey(name: 'username') String username,
    @JsonUserIdConverter() @JsonKey(name: 'userId') int userId,
    @JsonKey(name: 'userAvatar') String userAvatar,
    @JsonKey(name: 'address') String address,
    @JsonKey(name: 'chain') String chain,
    @JsonKey(name: 'icon') String icon,
    @JsonKey(name: 'name') String? name,
    @JsonKey(name: 'symbol') String? symbol,
  });
}

/// @nodoc
class _$LiveMessageCACopyWithImpl<$Res>
    implements $LiveMessageCACopyWith<$Res> {
  _$LiveMessageCACopyWithImpl(this._self, this._then);

  final LiveMessageCA _self;
  final $Res Function(LiveMessageCA) _then;

  /// Create a copy of LiveMessage
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createAt = null,
    Object? username = null,
    Object? userId = null,
    Object? userAvatar = null,
    Object? address = null,
    Object? chain = null,
    Object? icon = null,
    Object? name = freezed,
    Object? symbol = freezed,
  }) {
    return _then(
      LiveMessageCA(
        createAt: null == createAt
            ? _self.createAt
            : createAt // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        username: null == username
            ? _self.username
            : username // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
        userAvatar: null == userAvatar
            ? _self.userAvatar
            : userAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        address: null == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        chain: null == chain
            ? _self.chain
            : chain // ignore: cast_nullable_to_non_nullable
                  as String,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
        name: freezed == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String?,
        symbol: freezed == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}
