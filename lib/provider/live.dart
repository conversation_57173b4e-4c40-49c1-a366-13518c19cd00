import 'dart:async';
import 'dart:io' as io show File;

import 'package:audio_session/audio_session.dart'
    show AudioDevice, AudioSession;
import 'package:collection/collection.dart';
import 'package:connectycube_flutter_call_kit/connectycube_flutter_call_kit.dart';
import 'package:flutter/widgets.dart' show ChangeNotifier, Route;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_constants/me_constants.dart' show NeverCatchError;
import 'package:me_extensions/me_extensions.dart';
import 'package:me_misc/me_misc.dart'
    show MENavigatorExtension, meNavigator, meNavigatorObserver;
import 'package:me_ui/me_ui.dart' show MEImage, showErrorToast, showToast;
import 'package:me_utils/me_utils.dart' show HapticUtil, LogUtil;
import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:uuid/uuid.dart';
import 'package:video_player/video_player.dart' show VideoPlayerController;

import '/api/room.dart';
import '/constants/constants.dart' show globalDeepCollectionEquality;
import '/constants/envs.dart' show isAuditing;
import '/extensions/build_context_extension.dart';
import '/feat/media/helper.dart';
import '/feat/notification/helper.dart';
import '/feat/share/builder.dart';
import '/internals/methods.dart' show handleExceptions, showToastForError;
import '/internals/variables.dart' show globalClipboardText;
import '/models/live.dart';
import '/models/messaging.dart';
import '/models/tokens.dart' show TokenObject;
import '/models/user.dart' show PersistentUserInfo, UserInfo;
import '/res/assets.gen.dart';
import '/routes/scoop_routes.dart' show Routes;
import '/ui/widgets/app_loading.dart';
import 'api.dart';
import 'token.dart' show fetchTokenObjectProvider;
import 'user.dart';

part 'live.g.dart';

typedef _CallKit = ConnectycubeFlutterCallKit;

@riverpod
Future<LiveRoomUsers> fetchLiveUserList(
  Ref ref, {
  required String id,
  bool all = false,
}) {
  return ref.read(apiServiceProvider).getLiveRoomUsers(id, all: all);
}

bool roomRoutePredicate(Route? route) {
  return route?.settings.name == Routes.liveRoom.name;
}

void navigateLiveRoom() {
  if (meNavigatorObserver.history.any(roomRoutePredicate)) {
    meNavigator.popUntil(roomRoutePredicate);
  } else {
    meNavigator.removeNamedAndPushAndRemoveUntil(
      Routes.liveRoom.name,
      predicate: (_) => true,
    );
  }
}

// These providers are standalone and will be updated frequently.
final roomDataStream = StreamController<LiveRPCSerializable>.broadcast();

final liveItemProvider = Provider.autoDispose<LiveRoom>(
  (_) => throw UnimplementedError(),
);

final liveSummaryItemProvider = Provider.autoDispose<LiveRoomSummary>(
  (_) => throw UnimplementedError(),
);

/// Helps to maintain the state of the connected room.
final class RoomProvider extends ChangeNotifier {
  RoomProvider(this.ref) {
    _init();
  }

  final Ref ref;
  final String uuid = const Uuid().v4();

  String get _tag {
    final buffer = StringBuffer('🎙️ RoomStatusListener');
    if (_room case final room?) {
      buffer.write('(${room.id})');
    }
    return buffer.toString();
  }

  bool get mounted => _mounted;
  bool _mounted = true;

  @override
  void notifyListeners() {
    if (!_mounted) {
      LogUtil.w(
        'RoomStatusListener has been disposed.',
        tag: _tag,
        tagWithTrace: false,
      );
      return;
    }
    super.notifyListeners();
  }

  PersistentUserInfo? get _me => ref.read(userRepoProvider);

  RoomApi get api => ref.read(apiRoomProvider);

  ServiceApi get service => ref.read(apiServiceProvider);

  LiveRoom? get room => _room;
  LiveRoom? _room;

  void updateRoom(LiveRoom? value) {
    if (value == _room) {
      return;
    }
    _room = room;
    _onRoomChanged(value);
  }

  CancelToken? _roomCancelToken;

  bool get isRoomHost => switch ((_room, _me)) {
    (final room?, final user?) => room.isHost(user.userId),
    _ => false,
  };

  Timer? _timerCheckRoom;

  void _setCheckRoomTimer(LiveRoom? newRoom) {
    _timerCheckRoom?.cancel();
    if (newRoom == null) {
      return;
    }
    _timerCheckRoom = Timer.periodic(5.seconds, (timer) {
      if (_room == null || _isLeaving) {
        timer.cancel();
      } else {
        _checkRoom();
      }
    });
  }

  Future<void> _checkRoom({LiveRoom? room}) async {
    room ??= _room;
    if (room == null) {
      return;
    }
    try {
      final newRoom = await service.getLiveRoomInfo(
        room.id,
        cancelToken: _roomCancelToken,
      );
      updateRoom(newRoom);
      _checkMicRequestResult();
    } on ApiException catch (e) {
      if (e.code == ApiException.errorRoomClosed) {
        _timerCheckRoom?.cancel();
        leaveRoom(showEndedPage: true);
        return;
      }
      rethrow;
    } finally {
      notifyListeners();
    }
  }

  (ConnectionStateType, ConnectionChangedReasonType)? get connectionState =>
      _connectionState;
  (ConnectionStateType, ConnectionChangedReasonType)? _connectionState;
  Timer? _connectionStateThreshold;

  (QualityType?, QualityType?)? get qualityType => _qualityType;
  (QualityType?, QualityType?)? _qualityType;
  Timer? _qualityTypeThreshold;

  AudioRoute get audioRoute => _audioRoute;
  AudioRoute _audioRoute = AudioRoute.routeSpeakerphone;

  late final StreamSubscription _subRtcTokenExpire;
  late final StreamSubscription _subRtcConnectionState;
  late final StreamSubscription _subRtcNetworkQuality;
  late final StreamSubscription _subRtcUserJoined;
  late final StreamSubscription _subRtcUserOffline;
  late final StreamSubscription _subRtcLeaveChannel;
  late final StreamSubscription _subRtcAudioRouteChanged;

  late final StreamSubscription _subRtmTokenExpire;
  late final StreamSubscription _subRtmMessage;

  late final StreamSubscription<LiveRPCSerializable> _subData;

  AudioSession get audioSession => _audioSession;
  late AudioSession _audioSession;
  Set<AudioDevice> audioDevices = {};

  String? get currentCallIncomingSession => callIncomingSessionOf(_room?.id);

  String? callIncomingSessionOf(String? roomId) {
    return _callIncomingSessions[roomId];
  }

  /// Mapping room name <=> call session.
  final _callIncomingSessions = <String, String>{};

  void addCallIncomingSession(String roomId, String sessionId) {
    _callIncomingSessions[roomId] = sessionId;
    notifyListeners();
  }

  CallSession? get callOutgoingSession => _callOutgoingSession;
  CallSession? _callOutgoingSession;
  VideoPlayerController? _callOutgoingAudio;

  set callOutgoingSession(CallSession? session) {
    if (session == _callOutgoingSession) {
      return;
    }
    _callOutgoingSession = session;

    // Start the calling audio when
    // 1. The session is ongoing.
    // 2. There is only one people in the room.
    // 3. The audio has not been initialized.
    final users = _usersFiltered?.count ?? 0;
    if (session != null &&
        !session.done &&
        users == 1 &&
        _callOutgoingAudio == null) {
      Future(() async {
        final audio = await MediaHelper.initialize(
          Assets.media.roomCallOutgoing,
        );
        if (_callOutgoingSession == null) {
          audio.dispose();
        } else {
          _callOutgoingAudio?.dispose();
          audio
            ..setLooping(true)
            ..play();
          _callOutgoingAudio = audio;
        }
      });
    } else if (session == null || session.done || users > 1) {
      _callOutgoingAudio?.dispose();
      _callOutgoingAudio = null;
    }
    notifyListeners();
  }

  CallSession? get currentCallOutgoingSession {
    return callOutgoingSessionOf(_room?.id);
  }

  CallSession? callOutgoingSessionOf(String? sessionId) {
    if (sessionId == null) {
      return null;
    }
    if (_callOutgoingSession?.id == sessionId) {
      return _callOutgoingSession;
    }
    return null;
  }

  void _init() {
    _CallKit.instance.init(
      onCallAccepted: _onCallKitAccepted,
      onCallRejected: _onCallKitRejected,
      onCallIncoming: _onCallKitIncoming,
    );
    _CallKit.onCallMuted = _onCallKitMuted;
    final rtcCentral = api.rtcBroadcaster;
    final rtmCentral = api.rtmBroadcaster;
    AudioSession.instance.then((session) async {
      _audioSession = session;
      session.devicesStream.listen((data) {
        audioDevices = data;
        notifyListeners();
      });
    });

    // RTC subscriptions.
    _subRtcTokenExpire = rtcCentral.onTokenPrivilegeWillExpire.stream.listen(
      (event) async {
        String? roomId = event.$1.channelId;
        if (roomId == null || roomId.isEmpty) {
          roomId = _room?.id;
        }
        if (_room == null || roomId == null || roomId.isEmpty) {
          return;
        }
        LogUtil.w(
          'Live token refreshing for room $roomId...',
          tag: _tag,
          tagWithTrace: false,
        );
        final newToken = await service.getLiveTokens(roomId, refresh: true);
        if (_room == null) {
          return;
        }
        api.renewToken(rtc: newToken.rtc, rtm: newToken.rtm);
      },
    );
    _subRtcConnectionState = rtcCentral.onConnectionStateChanged.stream.listen((
      event,
    ) {
      if (event.$1.channelId != _room?.id) {
        return;
      }
      _connectionStateThreshold?.cancel();
      _connectionStateThreshold = Timer(const Duration(seconds: 1), () {
        if (event.$1.channelId != _room?.id) {
          return;
        }
        _connectionState = (event.$2, event.$3);
        notifyListeners();
      });
    });
    _subRtcNetworkQuality = rtcCentral.onNetworkQuality.stream.listen((event) {
      if (event.$1.channelId != _room?.id ||
          event.$2 != _me?.userId && event.$2 != 0) {
        return;
      }

      QualityType? filter(QualityType value) {
        if (value == QualityType.qualityUnknown ||
            value == QualityType.qualityDetecting ||
            value == QualityType.qualityUnsupported) {
          return null;
        }
        return value;
      }

      _qualityTypeThreshold?.cancel();
      _qualityTypeThreshold = Timer(const Duration(seconds: 1), () {
        if (event.$1.channelId != _room?.id ||
            event.$2 != _me?.userId && event.$2 != 0) {
          return;
        }
        _qualityType = (filter(event.$3), filter(event.$4));
        notifyListeners();
      });
    });
    _subRtcUserJoined = rtcCentral.onUserJoined.stream.listen((event) {
      LogUtil.d(
        'User joined ${event.$1.channelId} ${event.$2}',
        tag: _tag,
        tagWithTrace: false,
      );
      if (event.$1.channelId != _room?.id) {
        return;
      }
      _refreshUsers();
    });
    _subRtcUserOffline = rtcCentral.onUserOffline.stream.listen((event) {
      LogUtil.d(
        'User offline ${event.$1.channelId} ${event.$2}',
        tag: _tag,
        tagWithTrace: false,
      );
      if (event.$1.channelId != _room?.id) {
        return;
      }
      _refreshUsers();
    });
    _subRtcLeaveChannel = rtcCentral.onLeaveChannel.stream.listen((event) {
      LogUtil.d(
        'User leave channel ${event.$1.channelId}',
        tag: _tag,
        tagWithTrace: false,
      );
      if (event.$1.channelId == _room?.id) {
        leaveRoom(showEndedPage: true);
      } else {
        LogUtil.w(
          'User leave channel ${event.$1.channelId} '
          'which different from current ${_room?.id}',
          tag: _tag,
          tagWithTrace: false,
          report: true,
        );
      }
    });
    _subRtcAudioRouteChanged = rtcCentral.onAudioRoutingChanged.stream.listen((
      event,
    ) {
      _audioRoute = AudioRouteExt.fromValue(event);
      notifyListeners();
    });
    _subAudioIndications = rtcCentral.onAudioVolumeIndication.stream.listen(
      (event) {
        if (event.$1.channelId != _room?.id) {
          return;
        }
        _handleAudioIndications(event.$2.toList());
      },
    );
    _subMutedUsers = rtcCentral.onUserMuteAudio.stream.listen((event) {
      if (event.$1.channelId != _room?.id) {
        return;
      }
      final room = _room;
      if (room == null) {
        return;
      }
      final (_, int uid, bool muted) = event;
      if (muted) {
        _usersMuted.add(uid);
      } else {
        _usersMuted.remove(uid);
      }
      notifyListeners();
    });

    // RTM subscriptions.
    _subRtmTokenExpire = rtmCentral.token.stream.listen((event) async {
      String? roomId = event.channelName;
      if (roomId.isEmpty) {
        roomId = _room?.id;
      }
      if (_room == null || roomId == null || roomId.isEmpty) {
        return;
      }
      LogUtil.w(
        'Live token refreshing for room $roomId...',
        tag: _tag,
        tagWithTrace: false,
      );
      final newToken = await service.getLiveTokens(roomId, refresh: true);
      if (_room == null) {
        return;
      }
      api.renewToken(rtc: null, rtm: newToken.rtm);
    });
    _subRtmMessage = rtmCentral.message.stream.listen((event) {
      if (event.channelName case final room?
          when room.isNotEmpty && room != _room?.id) {
        LogUtil.w(
          'RTM received message but with a different room: '
          '($room) <=> (${_room?.id})',
          report: true,
        );
        return;
      }
      final bytes = event.message;
      if (bytes == null || bytes.isEmpty) {
        LogUtil.w(
          'Invalid RTM message '
          '[${event.publisher}](${event.timestamp}) '
          'with empty payload.',
          tag: _tag,
          tagWithTrace: false,
        );
        return;
      }
      final deserialized = LiveRPCSerializer.decodeRequest(bytes)?.data;
      if (deserialized == null) {
        return;
      }
      roomDataStream.add(deserialized);
    });
    _subData = roomDataStream.stream.listen(_handleData);
  }

  void _onRoomChanged(LiveRoom? newRoom, {bool refresh = true}) {
    if (_roomCancelToken case final ct?) {
      ct.cancel('outdated');
    }
    _roomCancelToken = CancelToken();
    if (newRoom?.id != _room?.id) {
      _setCheckRoomTimer(newRoom);
      _setRefreshUsersTimer(newRoom);
      _setRefreshPinTimer(newRoom);
      _setCheckOutgoingCallTimer(newRoom);
      _userCount = 1;
      _usersFiltered = null;
      _selfMuted = true;
      _usersMuted.clear();
      _requestingMic = false;
      _usersRequestingMic.clear();
      _messagesCache.clear();
      _caPinned = null;
      _caMentioned = <String, TokenObject?>{};
      _audioIndications = [];
      _callIncomingSessions.remove(_room?.id)?.let((it) async {
        await _CallKit.reportCallEnded(sessionId: it);
        await _CallKit.clearCallData(sessionId: it);
      });
      _callOutgoingSession?.let((it) {
        service.cancelCall(callId: it.id);
        callOutgoingSession = null;
      });
    }
    _room = newRoom;
    if (refresh) {
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _subRtcTokenExpire.cancel();
    _subRtcConnectionState.cancel();
    _connectionStateThreshold?.cancel();
    _subRtcNetworkQuality.cancel();
    _qualityTypeThreshold?.cancel();
    _subRtcUserJoined.cancel();
    _subRtcUserOffline.cancel();
    _subRtcLeaveChannel.cancel();
    _subRtcAudioRouteChanged.cancel();
    _subRtmTokenExpire.cancel();
    _subRtmMessage.cancel();
    _subData.cancel();
    _timerCheckRoom?.cancel();
    _timerRefreshUsers?.cancel();
    _timerRefreshPin?.cancel();
    _timerOutgoingCall?.cancel();
    _subAudioIndications.cancel();
    _audioIndicationZeroThreshold?.cancel();
    _audioVolumeMaxUserThreshold?.cancel();
    _subMutedUsers.cancel();
    _onRoomChanged(null);
    _mounted = false;
    super.dispose();
  }

  bool _isLeaving = false;

  Future<void> leaveRoom({
    bool withLoading = true,
    bool showEndedPage = false,
  }) async {
    if (_isLeaving) {
      return;
    }

    void popIfApplicable() {
      if (meNavigatorObserver.history.any(roomRoutePredicate)) {
        meNavigator.popUntil(roomRoutePredicate);
        meNavigator.pop();
      }
    }

    final room = _room;
    if (room == null) {
      popIfApplicable();
      return;
    }
    _isLeaving = true;
    try {
      final future = Future(() async {
        if (isRoomHost) {
          await service.closeLiveRoom(room.id);
        }
        await api.leaveRoom(id: room.id);
        popIfApplicable();
        updateRoom(null);
        if (showEndedPage) {
          try {
            final summary = await service.getLiveRoomSummary(room.id);
            meNavigator.pushNamed(
              Routes.liveRoomSummary.name,
              arguments: Routes.liveRoomSummary.d(summary),
            );
          } on ApiException catch (e) {
            if (e.code == ApiException.errorRoomClosed) {
              return;
            }
            rethrow;
          }
        }
      });
      if (withLoading) {
        await AppLoading.run(() => future);
      } else {
        await future;
      }
    } finally {
      _isLeaving = false;
    }
  }

  Future<void> leaveAndCreateRoom({
    required bool private,
    String? tokenAddress,
    String? groupId,
  }) async {
    try {
      await leaveRoom(withLoading: false);
      await api.createRoom(
        private: private,
        tokenAddress: tokenAddress,
        groupId: groupId,
      );
    } catch (e) {
      showToastForError(e);
      rethrow;
    }
  }

  Future<void> leaveAndJoinRoom(String id) async {
    try {
      final previous = _room;
      if (previous != null) {
        if (previous.id == id) {
          return;
        }
        await leaveRoom(withLoading: false);
      }
      await api.joinRoom(id);
    } catch (e) {
      showToastForError(e);
      rethrow;
    }
  }

  Future<void> _handleData(LiveRPCSerializable data) async {
    final room = _room;
    if (room == null) {
      return;
    }
    final me = ref.read(userRepoProvider);
    if (me == null) {
      return;
    }
    _handleLocalNotification(data).catchError((e, s) {
      handleExceptions(error: e, stackTrace: s);
    });
    if (data case final LiveControl control) {
      _handleControl(control);
    } else if (data case final LiveMessage message) {
      _handleMessage(message);
    }
  }

  Future<void> _handleLocalNotification(LiveRPCSerializable data) async {
    final room = _room;
    if (room == null) {
      return;
    }

    // Ignore unnecessary controls.
    switch (data) {
      case LiveControlPushedCA():
      case LiveControlPushedLink():
      case LiveControlUpdateChatAbility():
        return;
      default:
        break;
    }

    // Ignore not allowed controls when auditing.
    if (isAuditing &&
        (data is LiveControlPinnedCA || data is LiveControlPinnedLink)) {
      return;
    }

    // Ignore not related controls.
    final userId = _me?.userId;
    if (data is LiveControlRequestMicrophone && !room.isHost(userId)) {
      return;
    }
    if (data is LiveControlUpdateMicrophone && data.userInfo.userId != userId) {
      return;
    }
    if (data is LiveControlKickUser && data.uuid != uuid) {
      return;
    }

    final title = switch (data) {
      LiveControlRequestMicrophone() => data.userInfo.username,
      LiveControlUpdateMicrophone() => globalL10n.titleLiveMicUpdated,
      LiveControlPinnedCA() => globalL10n.titleLiveNotificationCAPinned,
      LiveControlPinnedLink() => globalL10n.titleLiveNotificationLinkPinned,
      LiveControlKickUser() => globalL10n.titleLiveNotificationKicked,
      // LiveMessage() => data.username,
      _ => null,
    };
    final body = switch (data) {
      LiveControlRequestMicrophone() => globalL10n.textRequestingToSpeak,
      LiveControlUpdateMicrophone() =>
        _requestingMic && !data.enabled
            ? globalL10n.toastErrorLiveHostDeclinedMicRequest
            : data.enabled
            ? globalL10n.toastLiveNowSpeaker
            : globalL10n.toastLiveNoLongerSpeaker,
      LiveControlPinnedCA() => globalL10n.textLiveNotificationCAPinned(
        data.symbol ?? '(Unavailable)',
        data.username,
      ),
      LiveControlPinnedLink() => globalL10n.textLiveNotificationLinkPinned(
        data.title.or('(Unavailable)'),
        data.username,
      ),
      LiveControlKickUser() => globalL10n.textLiveNotificationKicked,
      // LiveMessagePlain() => data.text,
      // LiveMessageTrade() => data.tokenAmount.toString(),
      // LiveMessageCA() => 'CA: ${data.symbol} (${data.address.asIdentifier})',
      _ => null,
    };
    if (title == null && body == null) {
      return;
    }

    final channel = NotificationHelper.channelLive;
    final io.File? largeIconFile = await switch (data) {
      LiveControlRequestMicrophone() => MEImage.getCachedFileIfExists(
        data.userInfo.avatar,
      ),
      LiveControlPinnedCA() => MEImage.getCachedFileIfExists(data.icon),
      LiveControlPinnedLink() => MEImage.getCachedFileIfExists(data.icon),
      LiveMessageTrade() => MEImage.getCachedFileIfExists(
        data.tokenLogo.or(data.userAvatar),
      ),
      LiveMessageCA() => MEImage.getCachedFileIfExists(data.icon),
      LiveControl() => MEImage.getCachedFileIfExists(data.userAvatar),
      LiveMessage() => MEImage.getCachedFileIfExists(data.userAvatar),
    };
    return NotificationHelper.postNotification(
      channelAndroid: channel,
      title: title,
      body: body,
      payload: NotificationParams(
        nativeAction: NotificationParams.actionJoinLive,
        nativeActionArguments: {'id': room.id},
      ).toWrappedJson().serialize(),
      largeIconAndroid: largeIconFile?.path,
    );
  }

  Future<void> _handleControl(LiveControl control) async {
    final me = ref.read(userRepoProvider);
    if (me == null) {
      return;
    }
    switch (control) {
      case LiveControlRequestMicrophone():
        onUserMicRequestReceived(control.userInfo);
      case LiveControlUpdateMicrophone():
        onUserMicUpdateHandled(control);
      case LiveControlPushedCA():
      case LiveControlPinnedCA():
      case LiveControlUnpinnedCA():
        refreshCA();
      case LiveControlPushedLink():
      case LiveControlPinnedLink():
      case LiveControlUnpinnedLink():
        refreshLink();
      case LiveControlCloseRoom():
        leaveRoom(showEndedPage: true);
      case LiveControlKickUser():
        if (control.uuid == uuid) {
          leaveRoom(showEndedPage: true);
        }
      case LiveControlUpdateChatAbility():
        onChatUpdateHandled(control);
    }
  }

  int get userCount => _userCount;
  int _userCount = 1;

  set userCount(int value) {
    if (value == _userCount) {
      return;
    }
    _userCount = value;
    notifyListeners();
  }

  Timer? _timerRefreshUsers;

  void _setRefreshUsersTimer(LiveRoom? newRoom) {
    _timerRefreshUsers?.cancel();
    if (newRoom == null) {
      return;
    }
    _refreshUsers(room: newRoom);
    _timerRefreshUsers = Timer.periodic(5.seconds, (timer) {
      if (_room == null || _isLeaving) {
        timer.cancel();
      } else {
        _refreshUsers();
      }
    });
  }

  LiveRoomUsersFiltered? get usersFiltered => _usersFiltered;
  LiveRoomUsersFiltered? _usersFiltered;

  LiveRoomUsers? get users => _users;
  LiveRoomUsers? _users;

  Future<void> _refreshUsers({LiveRoom? room}) async {
    room ??= _room;
    if (room == null) {
      return;
    }
    try {
      final users = await service.getLiveRoomUsers(
        room.id,
        cancelToken: _roomCancelToken,
      );
      if (_room == null) {
        return;
      }
      final filtered = users.toFiltered(room: room, fallback: _me);
      _userCount = filtered.count;
      _users = users;
      _usersFiltered = filtered;
      _checkMicRequestResult();
      if (_callOutgoingSession case final session?) {
        final calling = session.users.toList();
        for (final user in users.users) {
          // The user has already joined the room, remove it from the call.
          if (session.users.any((e) => e.userId == user.userId)) {
            calling.removeWhere((e) => e.userId == user.userId);
          }
        }
        if (calling.isEmpty) {
          callOutgoingSession = null;
        } else {
          callOutgoingSession = session.copyWith(users: calling);
        }
      }
    } on ApiException catch (e) {
      if (e.code == ApiException.errorRoomClosed) {
        return;
      }
      rethrow;
    } finally {
      notifyListeners();
    }
  }

  late DateTime _lastAudioIndications = DateTime.now();
  late final StreamSubscription _subAudioIndications;
  Timer? _audioIndicationZeroThreshold;
  Timer? _audioVolumeMaxUserThreshold;

  List<AudioVolumeInfo> get audioIndications => _audioIndications;
  List<AudioVolumeInfo> _audioIndications = [];

  set audioIndications(List<AudioVolumeInfo> value) {
    if (globalDeepCollectionEquality.equals(value, _audioIndications)) {
      return;
    }
    _audioIndications = value;
    notifyListeners();
  }

  LiveRoomUser? get liveRoomUserMaxVolume => _liveRoomUserMaxVolume;
  LiveRoomUser? _liveRoomUserMaxVolume;

  set liveRoomUserMaxVolume(LiveRoomUser? value) {
    if (value == _liveRoomUserMaxVolume) {
      return;
    }
    _liveRoomUserMaxVolume = value;
    notifyListeners();
  }

  bool get selfMuted => _selfMuted;
  bool _selfMuted = true;

  Future<void> muteSelf(bool mute, {bool reportCallKit = true}) async {
    if (!mute) {
      final permission = await Permission.microphone.request();
      if (!permission.isGranted) {
        showErrorToast(globalL10n.toastErrorNoPermissionMicrophoneForLive);
        throw NeverCatchError();
      }
      MediaHelper.oneshotAsset(Assets.media.roomMicOn);
    }
    await api.rtcEngine.enableLocalAudio(!mute);
    if (currentCallIncomingSession case final session? when reportCallKit) {
      _CallKit.reportCallMuted(sessionId: session, muted: mute);
    }
    _selfMuted = mute;
    notifyListeners();
  }

  List<int> get usersMuted => _usersMuted.toList();
  final _usersMuted = <int>{};
  late final StreamSubscription _subMutedUsers;

  bool get requestingMic => _requestingMic;
  bool _requestingMic = false;

  Future<void> requestMic() async {
    if (_isLeaving) {
      return;
    }
    final room = _room;
    if (room == null) {
      return;
    }
    final me = _me;
    if (me == null) {
      return;
    }

    _requestingMic = true;
    notifyListeners();
    showToast(globalL10n.toastLiveMicWaiting, duration: 3.seconds);
    try {
      await api.rtmSend(
        room,
        LiveControlRequestMicrophone(
          createAt: DateTime.now(),
          username: me.name,
          userId: me.userId,
          userAvatar: me.avatar,
          userInfo: me.toUserInfo(),
        ),
      );
    } catch (e) {
      _requestingMic = false;
      rethrow;
    } finally {
      notifyListeners();
    }
  }

  /// Reset the current mic request if the user is now a speaker.
  void _checkMicRequestResult() {
    final room = _room;
    if (room == null) {
      return;
    }
    final canSpeak =
        _usersFiltered?.canSpeakOf(_me?.userId) ?? room.canSpeakOf(_me?.userId);
    if (canSpeak && _requestingMic) {
      _requestingMic = false;
    }
    notifyListeners();
  }

  Future<void> onUserMicUpdateHandled(
    LiveControlUpdateMicrophone control,
  ) async {
    final room = _room;
    if (room == null) {
      return;
    }
    final user = _me;
    if (user == null) {
      return;
    }
    if (control.userInfo.userId != user.userId) {
      return;
    }
    if (_requestingMic && !control.enabled) {
      HapticUtil.notifyFailure(force: true);
      showErrorToast(
        globalL10n.toastErrorLiveHostDeclinedMicRequest,
        duration: 3.seconds,
      );
      _requestingMic = false;
      notifyListeners();
      return;
    }
    await AppLoading.run(
      () async {
        final (:rtc, :rtm, :role) = await service.getLiveTokens(
          room.id,
          refresh: true,
        );
        ClientRoleType? effectiveRole = role;
        if (effectiveRole == null) {
          final info = await service.getLiveRoomInfo(room.id);
          effectiveRole = info.canSpeakOf(user.userId)
              ? ClientRoleType.clientRoleBroadcaster
              : ClientRoleType.clientRoleAudience;
        }
        await Future.wait([
          api.rtcEngine.updateChannelMediaOptions(
            ChannelMediaOptions(token: rtc, clientRoleType: role),
          ),
          api.rtmClient.renewToken(rtm).then((r) => r.$1.throwsIfError()),
          if (!control.enabled) muteSelf(true),
          _refreshUsers(),
        ]);
        if (control.enabled) {
          HapticUtil.notifySuccess(force: true);
          showToast(globalL10n.toastLiveNowSpeaker);
        } else {
          HapticUtil.notifyFailure(force: true);
          showToast(globalL10n.toastLiveNoLongerSpeaker, duration: 3.seconds);
        }
      },
    ).whenComplete(() {
      _requestingMic = false;
      notifyListeners();
    });
  }

  List<UserInfo> get usersRequestingMic => _usersRequestingMic.toList();
  final _usersRequestingMic = <UserInfo>{};

  void onUserMicRequestReceived(UserInfo user) {
    final room = this.room;
    if (room == null) {
      return;
    }
    if (!isRoomHost) {
      return;
    }
    if (_usersRequestingMic.add(user)) {
      if (_me case final user? when room.isHost(user.userId)) {
        HapticUtil.notifyFailure(force: true);
        MediaHelper.oneshotAsset(Assets.media.roomMicRequest);
      }
      notifyListeners();
    }
  }

  bool onUserMicRequestHandled(UserInfo user) {
    final result = _usersRequestingMic.remove(user);
    if (result) {
      notifyListeners();
    }
    return result;
  }

  Future<void> toggleUserCanSpeak(UserInfo user, bool enabled) async {
    final room = _room;
    if (room == null) {
      return;
    }
    final me = ref.read(userRepoProvider);
    if (me == null) {
      return;
    }
    await service.switchLiveRoomRoleForUser(
      room.id,
      user.rawUserId,
      enabled
          ? ClientRoleType.clientRoleBroadcaster
          : ClientRoleType.clientRoleAudience,
    );
    await toggleUserMuted(user, !enabled);
    _refreshUsers();
    onUserMicRequestHandled(user);
  }

  Future<void> toggleUserMuted(UserInfo user, bool muted) async {
    final room = _room;
    if (room == null) {
      return;
    }
    final me = ref.read(userRepoProvider);
    if (me == null) {
      return;
    }
    return api.rtmSend(
      room,
      LiveControlUpdateMicrophone(
        createAt: DateTime.now(),
        username: me.name,
        userId: me.userId,
        userAvatar: me.avatar,
        userInfo: user,
        enabled: !muted,
      ),
    );
  }

  final messagesMaxSize = 100;

  List<LiveMessage> get messages => _messagesCache;
  final _messagesCache = <LiveMessage>[];

  void _handleMessage(LiveMessage message) {
    // Ignore messages from users other than the creator
    // when the chat is not enabled.
    if (_room?.chatEnabled != true && message.userId != _room?.creator.userId) {
      return;
    }
    switch (message) {
      case LiveMessageCA():
        MediaHelper.oneshotAsset(Assets.media.roomMessageCa);
      case LiveMessagePlain():
      case LiveMessageTrade():
        break;
    }
    if (_messagesCache.length == messagesMaxSize) {
      _messagesCache.removeAt(0);
    }
    _messagesCache.add(message);
    notifyListeners();
  }

  void _handleAudioIndications(List<AudioVolumeInfo> indications) {
    if (indications.every(
      (e) => e.run((it) => it.volume == 0 && it.vad == 0 && it.voicePitch == 0),
    )) {
      indications.clear();
    }
    final previous = _audioIndications.toList();
    if (indications.isEmpty && previous.isEmpty) {
      return;
    }
    if (globalDeepCollectionEquality.equals(previous, indications)) {
      return;
    }
    if (indications.isEmpty &&
        _lastAudioIndications.difference(DateTime.now()) <
            rtcRoomAudioVolumeIndicationInterval) {
      _audioIndicationZeroThreshold?.cancel();
      _audioIndicationZeroThreshold = Timer(
        rtcRoomAudioVolumeIndicationInterval * 2,
        () {
          audioIndications = [];
        },
      );
      return;
    }
    indications.sort((a, b) => b.volume?.compareTo(a.volume ?? 0) ?? 0);
    _lastAudioIndications = DateTime.now();
    _audioIndications = indications;
    final max = indications.first;
    if (max.volume case final volume? when volume > 15.0) {
      final uid = switch (max.uid) {
        0 => _me?.userId,
        final uid => uid,
      };
      _liveRoomUserMaxVolume = _usersFiltered?.of(uid);
      _audioVolumeMaxUserThreshold?.cancel();
      _audioVolumeMaxUserThreshold = Timer(
        rtcRoomAudioVolumeMaxUserInterval,
        () {
          liveRoomUserMaxVolume = null;
        },
      );
    }
    notifyListeners();
  }

  Timer? _timerRefreshPin;

  void _setRefreshPinTimer(LiveRoom? newRoom) {
    _timerRefreshPin?.cancel();
    if (newRoom == null) {
      return;
    }
    refreshCA(room: newRoom);
    refreshLink(room: newRoom);
    _timerRefreshPin = Timer.periodic(15.seconds, (timer) {
      if (_room == null || _isLeaving) {
        timer.cancel();
      } else {
        refreshCA();
        refreshLink();
      }
    });
  }

  TokenObject? get caPinned => _caPinned;
  TokenObject? _caPinned;

  Map<String, TokenObject?> get caMentioned => _caMentioned;
  Map<String, TokenObject?> _caMentioned = <String, TokenObject?>{};

  Future<void> refreshCA({LiveRoom? room}) async {
    if (isAuditing) {
      return;
    }
    room ??= _room;
    if (room == null) {
      return;
    }

    final mentioned = (await service.getLiveRoomCAs(
      room.id,
      cancelToken: _roomCancelToken,
    )).toList();

    if (_room == null) {
      return;
    }

    // Put the pinned token to the top.
    mentioned.sort((a, b) {
      if (a.pinned) {
        return -1;
      }
      if (b.pinned) {
        return 1;
      }
      return 0;
    });

    // Fetch the pinned token if exists.
    TokenObject? pinned;
    if (mentioned.firstOrNull case final token? when token.pinned) {
      pinned = await ref.read(
        fetchTokenObjectProvider(address: token.address).future,
      );
      // Merge the matched token icon.
      String effectiveLogo = '';
      if (token.icon case final logo when logo.isNotEmpty) {
        effectiveLogo = logo;
      }
      if (pinned?.logo case final logo? when logo.isNotEmpty) {
        effectiveLogo = logo;
      }
      pinned = pinned?.copyWith(logo: effectiveLogo);
    }

    if (_room == null) {
      return;
    }

    _caPinned = pinned;
    final newMentioned = <String, TokenObject?>{};
    for (final token in mentioned) {
      final address = token.address;
      final previous = _caMentioned[address];
      if (previous == null) {
        ref.read(apiDexScreenerProvider).getToken(address).then((r) {
          if (caMentioned.containsKey(address)) {
            caMentioned[address] = r?.copyWith(logo: r.logo.or(token.icon));
            notifyListeners();
          }
        });
      }
      newMentioned[address] = previous;
    }
    _caMentioned = newMentioned;
    notifyListeners();
  }

  LiveRoomLink? get linkPinned => _linkPinned;
  LiveRoomLink? _linkPinned;

  Future<void> refreshLink({LiveRoom? room}) async {
    if (isAuditing) {
      return;
    }
    room ??= _room;
    if (room == null) {
      return;
    }

    final links = await service.getLiveRoomLinks(
      room.id,
      cancelToken: _roomCancelToken,
    );
    if (_room == null) {
      return;
    }

    final pinned = links.firstWhereOrNull((e) => e.pinned);
    _linkPinned = pinned;
    notifyListeners();
  }

  Future<void> toggleChatAbility(bool enabled) async {
    final room = _room;
    if (room == null) {
      return;
    }
    final me = _me;
    if (me == null) {
      return;
    }
    return api.rtmSend(
      room,
      LiveControlUpdateChatAbility(
        createAt: DateTime.now(),
        username: me.name,
        userId: me.userId,
        userAvatar: me.avatar,
        enabled: enabled,
      ),
    );
  }

  void onChatUpdateHandled(LiveControlUpdateChatAbility control) {
    final room = _room;
    if (room == null || room.chatEnabled == control.enabled) {
      return;
    }
    _room = room.copyWith(chatEnabled: control.enabled);
    notifyListeners();
  }

  Timer? _timerOutgoingCall;

  void _setCheckOutgoingCallTimer(LiveRoom? newRoom) {
    _timerOutgoingCall?.cancel();
    if (newRoom == null) {
      return;
    }
    _timerOutgoingCall = Timer.periodic(5.seconds, (timer) {
      if (_room == null || _isLeaving) {
        timer.cancel();
      } else {
        _checkOutgoingCall();
      }
    });
  }

  Future<void> _checkOutgoingCall({LiveRoom? room}) async {
    room ??= _room;
    if (room == null) {
      return;
    }
    final call = _callOutgoingSession;
    if (call == null || call.done) {
      return;
    }
    final result = await service.getCallInfo(
      callId: call.id,
      cancelToken: _roomCancelToken,
    );
    if (result.accepted) {
      callOutgoingSession = null;
    } else {
      callOutgoingSession = result;
    }
  }

  String? _getChannelFromCallEvent(CallEvent event) {
    final channel = event.userInfo?['channel']?.trim();
    if (channel == null || channel.isEmpty) {
      return null;
    }
    return channel;
  }

  Future<void> _onCallKitAccepted(CallEvent event) async {
    if (ref.read(userRepoProvider) == null) {
      return;
    }
    if (_getChannelFromCallEvent(event) case final name?) {
      await AppLoading.run(() async {
        try {
          await leaveAndJoinRoom(name);
          _callIncomingSessions[name] = event.sessionId;
          meNavigator.removeNamedAndPushAndRemoveUntil(
            Routes.liveRoom.name,
            predicate: (_) => true,
          );
          _CallKit.reportCallMuted(sessionId: event.sessionId, muted: true);
          service.acceptCall(callId: event.sessionId);
        } catch (e, s) {
          handleExceptions(error: e, stackTrace: s);
        }
      });
    }
  }

  Future<void> _onCallKitRejected(CallEvent event) async {
    if (ref.read(userRepoProvider) == null) {
      return;
    }
    service.declineCall(callId: event.sessionId);
    if (_getChannelFromCallEvent(event) case final id?) {
      final current = _room;
      if (current?.id == id) {
        await leaveRoom(showEndedPage: true);
      }
    }
  }

  Future<void> _onCallKitIncoming(CallEvent event) async {
    LogUtil.d('_onCallIncoming: $event');
  }

  Future<void> _onCallKitMuted(bool isMuted, String sessionId) async {
    final userId = _me?.userId;
    if (userId == null) {
      return;
    }
    final entry = _callIncomingSessions.entries.firstWhereOrNull(
      (e) => e.value == sessionId,
    );
    if (entry == null || entry.key != _room?.id) {
      return;
    }
    final canSpeak =
        _usersFiltered?.canSpeakOf(userId) ??
        _room?.canSpeakOf(userId) ??
        false;
    if (!canSpeak && !isMuted) {
      _CallKit.reportCallMuted(sessionId: sessionId, muted: true);
      await requestMic();
    } else {
      muteSelf(isMuted, reportCallKit: false);
    }
  }
}

final roomProvider = ChangeNotifierProvider<RoomProvider>(RoomProvider.new);

final roomConnectionStatesProvider = Provider.autoDispose(
  (ref) => ref.watch(roomProvider).connectionState,
);
final roomQualityTypeProvider = Provider.autoDispose(
  (ref) => ref.watch(roomProvider).qualityType,
);
final roomInstanceProvider = Provider<LiveRoom?>(
  (ref) => ref.watch(roomProvider).room,
);
final roomUserCountProvider = Provider.autoDispose<int>(
  (ref) => ref.watch(roomProvider).userCount,
);
final roomUsersFilteredProvider = Provider.autoDispose<LiveRoomUsersFiltered?>(
  (ref) => ref.watch(roomProvider).usersFiltered,
);
final roomUserSelfMutedProvider = Provider.autoDispose<bool>(
  (ref) => ref.watch(roomProvider).selfMuted,
);
final roomUsersMutedProvider = Provider.autoDispose<List<int>>(
  (ref) => ref.watch(roomProvider).usersMuted,
);
final roomRequestingMicrophoneProvider = Provider.autoDispose<bool>(
  (ref) => ref.watch(roomProvider).requestingMic,
);
final roomRequestedMicrophoneUsersProvider =
    Provider.autoDispose<List<UserInfo>>(
      (ref) => ref.watch(roomProvider).usersRequestingMic,
    );
final roomCallOutgoingSessionProvider = Provider.autoDispose<CallSession?>(
  (ref) => ref.watch(roomProvider).callOutgoingSession,
);
final roomMessagesProvider = Provider.autoDispose<List<LiveMessage>>(
  (ref) => ref.watch(roomProvider).messages,
);
final roomCAMentionedProvider = Provider.autoDispose<Map<String, TokenObject?>>(
  (ref) => ref.watch(roomProvider).caMentioned,
);
final roomCAPinnedProvider = Provider.autoDispose<TokenObject?>(
  (ref) => ref.watch(roomProvider).caPinned,
);
final roomLinkPinnedProvider = Provider.autoDispose<LiveRoomLink?>(
  (ref) => ref.watch(roomProvider).linkPinned,
);
final roomAudioDevicesProvider = Provider.autoDispose<Set<AudioDevice>>(
  (ref) => ref.watch(roomProvider).audioDevices,
);
final roomAudioRouteProvider = Provider.autoDispose<AudioRoute>(
  (ref) => ref.watch(roomProvider).audioRoute,
);
final roomAudioIndicationsProvider =
    Provider.autoDispose<List<AudioVolumeInfo>>(
      (ref) => ref.watch(roomProvider).audioIndications,
    );
final roomAudioVolumeMaxUserProvider = Provider.autoDispose<LiveRoomUser?>(
  (ref) => ref.watch(roomProvider).liveRoomUserMaxVolume,
);

extension RoomExtension on LiveRoom {
  Future<ShareResult> share(WidgetRef ref) async {
    final user = ref.watch(userRepoProvider);
    if (user == null) {
      return const ShareResult('', ShareResultStatus.dismissed);
    }
    final builder = ShareBuilder()
        .addPath(ShareBuilder.methodLive)
        .addPath(id)
        .addQueryParameter(ShareBuilder.keySource, ShareBuilder.sourceApp)
        .addQueryParameter(ShareBuilder.keyUserId, user.rawUserId);
    if (user.referCode.isNotEmpty) {
      builder.addQueryParameter(ShareBuilder.keyReferCode, user.referCode);
    }
    final share = builder.build();
    try {
      final short = await AppLoading.run(
        () => ref.read(apiScoopProviderProvider).putShortUrl(share.toString()),
      );
      globalClipboardText = short;
      return ShareBuilder.shareUri(Uri.parse(short));
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      globalClipboardText = share.toString();
      return ShareBuilder.shareUri(share);
    }
  }
}
