// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'live.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchLiveUserListHash() => r'f226a15276268a09fba097660741f937401642fc';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchLiveUserList].
@ProviderFor(fetchLiveUserList)
const fetchLiveUserListProvider = FetchLiveUserListFamily();

/// See also [fetchLiveUserList].
class FetchLiveUserListFamily extends Family<AsyncValue<LiveRoomUsers>> {
  /// See also [fetchLiveUserList].
  const FetchLiveUserListFamily();

  /// See also [fetchLiveUserList].
  FetchLiveUserListProvider call({required String id, bool all = false}) {
    return FetchLiveUserListProvider(id: id, all: all);
  }

  @override
  FetchLiveUserListProvider getProviderOverride(
    covariant FetchLiveUserListProvider provider,
  ) {
    return call(id: provider.id, all: provider.all);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchLiveUserListProvider';
}

/// See also [fetchLiveUserList].
class FetchLiveUserListProvider
    extends AutoDisposeFutureProvider<LiveRoomUsers> {
  /// See also [fetchLiveUserList].
  FetchLiveUserListProvider({required String id, bool all = false})
    : this._internal(
        (ref) =>
            fetchLiveUserList(ref as FetchLiveUserListRef, id: id, all: all),
        from: fetchLiveUserListProvider,
        name: r'fetchLiveUserListProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchLiveUserListHash,
        dependencies: FetchLiveUserListFamily._dependencies,
        allTransitiveDependencies:
            FetchLiveUserListFamily._allTransitiveDependencies,
        id: id,
        all: all,
      );

  FetchLiveUserListProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
    required this.all,
  }) : super.internal();

  final String id;
  final bool all;

  @override
  Override overrideWith(
    FutureOr<LiveRoomUsers> Function(FetchLiveUserListRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchLiveUserListProvider._internal(
        (ref) => create(ref as FetchLiveUserListRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
        all: all,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<LiveRoomUsers> createElement() {
    return _FetchLiveUserListProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchLiveUserListProvider &&
        other.id == id &&
        other.all == all;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);
    hash = _SystemHash.combine(hash, all.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchLiveUserListRef on AutoDisposeFutureProviderRef<LiveRoomUsers> {
  /// The parameter `id` of this provider.
  String get id;

  /// The parameter `all` of this provider.
  bool get all;
}

class _FetchLiveUserListProviderElement
    extends AutoDisposeFutureProviderElement<LiveRoomUsers>
    with FetchLiveUserListRef {
  _FetchLiveUserListProviderElement(super.provider);

  @override
  String get id => (origin as FetchLiveUserListProvider).id;
  @override
  bool get all => (origin as FetchLiveUserListProvider).all;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
