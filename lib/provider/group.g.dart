// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchGroupHash() => r'fb619313ce0870c690b35029a9a7d602cb18e41c';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchGroup].
@ProviderFor(fetchGroup)
const fetchGroupProvider = FetchGroupFamily();

/// See also [fetchGroup].
class FetchGroupFamily extends Family<AsyncValue<Group>> {
  /// See also [fetchGroup].
  const FetchGroupFamily();

  /// See also [fetchGroup].
  FetchGroupProvider call({required String id}) {
    return FetchGroupProvider(id: id);
  }

  @override
  FetchGroupProvider getProviderOverride(
    covariant FetchGroupProvider provider,
  ) {
    return call(id: provider.id);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchGroupProvider';
}

/// See also [fetchGroup].
class FetchGroupProvider extends AutoDisposeFutureProvider<Group> {
  /// See also [fetchGroup].
  FetchGroupProvider({required String id})
    : this._internal(
        (ref) => fetchGroup(ref as FetchGroupRef, id: id),
        from: fetchGroupProvider,
        name: r'fetchGroupProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchGroupHash,
        dependencies: FetchGroupFamily._dependencies,
        allTransitiveDependencies: FetchGroupFamily._allTransitiveDependencies,
        id: id,
      );

  FetchGroupProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final String id;

  @override
  Override overrideWith(
    FutureOr<Group> Function(FetchGroupRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchGroupProvider._internal(
        (ref) => create(ref as FetchGroupRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Group> createElement() {
    return _FetchGroupProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchGroupProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchGroupRef on AutoDisposeFutureProviderRef<Group> {
  /// The parameter `id` of this provider.
  String get id;
}

class _FetchGroupProviderElement extends AutoDisposeFutureProviderElement<Group>
    with FetchGroupRef {
  _FetchGroupProviderElement(super.provider);

  @override
  String get id => (origin as FetchGroupProvider).id;
}

String _$fetchGroupListHash() => r'5b6a1c1c80d322fbea115e0ac626c7f3264256dd';

/// See also [fetchGroupList].
@ProviderFor(fetchGroupList)
const fetchGroupListProvider = FetchGroupListFamily();

/// See also [fetchGroupList].
class FetchGroupListFamily extends Family<AsyncValue<Paged<Group>>> {
  /// See also [fetchGroupList].
  const FetchGroupListFamily();

  /// See also [fetchGroupList].
  FetchGroupListProvider call({
    required GroupType type,
    required int page,
    required int size,
  }) {
    return FetchGroupListProvider(type: type, page: page, size: size);
  }

  @override
  FetchGroupListProvider getProviderOverride(
    covariant FetchGroupListProvider provider,
  ) {
    return call(type: provider.type, page: provider.page, size: provider.size);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchGroupListProvider';
}

/// See also [fetchGroupList].
class FetchGroupListProvider extends AutoDisposeFutureProvider<Paged<Group>> {
  /// See also [fetchGroupList].
  FetchGroupListProvider({
    required GroupType type,
    required int page,
    required int size,
  }) : this._internal(
         (ref) => fetchGroupList(
           ref as FetchGroupListRef,
           type: type,
           page: page,
           size: size,
         ),
         from: fetchGroupListProvider,
         name: r'fetchGroupListProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$fetchGroupListHash,
         dependencies: FetchGroupListFamily._dependencies,
         allTransitiveDependencies:
             FetchGroupListFamily._allTransitiveDependencies,
         type: type,
         page: page,
         size: size,
       );

  FetchGroupListProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.type,
    required this.page,
    required this.size,
  }) : super.internal();

  final GroupType type;
  final int page;
  final int size;

  @override
  Override overrideWith(
    FutureOr<Paged<Group>> Function(FetchGroupListRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchGroupListProvider._internal(
        (ref) => create(ref as FetchGroupListRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        type: type,
        page: page,
        size: size,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Paged<Group>> createElement() {
    return _FetchGroupListProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchGroupListProvider &&
        other.type == type &&
        other.page == page &&
        other.size == size;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, type.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, size.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchGroupListRef on AutoDisposeFutureProviderRef<Paged<Group>> {
  /// The parameter `type` of this provider.
  GroupType get type;

  /// The parameter `page` of this provider.
  int get page;

  /// The parameter `size` of this provider.
  int get size;
}

class _FetchGroupListProviderElement
    extends AutoDisposeFutureProviderElement<Paged<Group>>
    with FetchGroupListRef {
  _FetchGroupListProviderElement(super.provider);

  @override
  GroupType get type => (origin as FetchGroupListProvider).type;
  @override
  int get page => (origin as FetchGroupListProvider).page;
  @override
  int get size => (origin as FetchGroupListProvider).size;
}

String _$fetchGroupUserListHash() =>
    r'cc71e9110e44e3cf00face3b45461c5c2b18b253';

/// See also [fetchGroupUserList].
@ProviderFor(fetchGroupUserList)
const fetchGroupUserListProvider = FetchGroupUserListFamily();

/// See also [fetchGroupUserList].
class FetchGroupUserListFamily extends Family<AsyncValue<Paged<UserInfo>>> {
  /// See also [fetchGroupUserList].
  const FetchGroupUserListFamily();

  /// See also [fetchGroupUserList].
  FetchGroupUserListProvider call({
    required String? id,
    required int page,
    required int size,
    required bool includeSelf,
  }) {
    return FetchGroupUserListProvider(
      id: id,
      page: page,
      size: size,
      includeSelf: includeSelf,
    );
  }

  @override
  FetchGroupUserListProvider getProviderOverride(
    covariant FetchGroupUserListProvider provider,
  ) {
    return call(
      id: provider.id,
      page: provider.page,
      size: provider.size,
      includeSelf: provider.includeSelf,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchGroupUserListProvider';
}

/// See also [fetchGroupUserList].
class FetchGroupUserListProvider
    extends AutoDisposeFutureProvider<Paged<UserInfo>> {
  /// See also [fetchGroupUserList].
  FetchGroupUserListProvider({
    required String? id,
    required int page,
    required int size,
    required bool includeSelf,
  }) : this._internal(
         (ref) => fetchGroupUserList(
           ref as FetchGroupUserListRef,
           id: id,
           page: page,
           size: size,
           includeSelf: includeSelf,
         ),
         from: fetchGroupUserListProvider,
         name: r'fetchGroupUserListProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$fetchGroupUserListHash,
         dependencies: FetchGroupUserListFamily._dependencies,
         allTransitiveDependencies:
             FetchGroupUserListFamily._allTransitiveDependencies,
         id: id,
         page: page,
         size: size,
         includeSelf: includeSelf,
       );

  FetchGroupUserListProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
    required this.page,
    required this.size,
    required this.includeSelf,
  }) : super.internal();

  final String? id;
  final int page;
  final int size;
  final bool includeSelf;

  @override
  Override overrideWith(
    FutureOr<Paged<UserInfo>> Function(FetchGroupUserListRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchGroupUserListProvider._internal(
        (ref) => create(ref as FetchGroupUserListRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
        page: page,
        size: size,
        includeSelf: includeSelf,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Paged<UserInfo>> createElement() {
    return _FetchGroupUserListProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchGroupUserListProvider &&
        other.id == id &&
        other.page == page &&
        other.size == size &&
        other.includeSelf == includeSelf;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, size.hashCode);
    hash = _SystemHash.combine(hash, includeSelf.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchGroupUserListRef on AutoDisposeFutureProviderRef<Paged<UserInfo>> {
  /// The parameter `id` of this provider.
  String? get id;

  /// The parameter `page` of this provider.
  int get page;

  /// The parameter `size` of this provider.
  int get size;

  /// The parameter `includeSelf` of this provider.
  bool get includeSelf;
}

class _FetchGroupUserListProviderElement
    extends AutoDisposeFutureProviderElement<Paged<UserInfo>>
    with FetchGroupUserListRef {
  _FetchGroupUserListProviderElement(super.provider);

  @override
  String? get id => (origin as FetchGroupUserListProvider).id;
  @override
  int get page => (origin as FetchGroupUserListProvider).page;
  @override
  int get size => (origin as FetchGroupUserListProvider).size;
  @override
  bool get includeSelf => (origin as FetchGroupUserListProvider).includeSelf;
}

String _$fetchGroupLivesHash() => r'9a3ecc7c2745650559d38f24142e22c25d5f3abc';

/// See also [fetchGroupLives].
@ProviderFor(fetchGroupLives)
const fetchGroupLivesProvider = FetchGroupLivesFamily();

/// See also [fetchGroupLives].
class FetchGroupLivesFamily extends Family<AsyncValue<Paged<GroupLiveRoom>>> {
  /// See also [fetchGroupLives].
  const FetchGroupLivesFamily();

  /// See also [fetchGroupLives].
  FetchGroupLivesProvider call({
    required String id,
    required int page,
    required int size,
  }) {
    return FetchGroupLivesProvider(id: id, page: page, size: size);
  }

  @override
  FetchGroupLivesProvider getProviderOverride(
    covariant FetchGroupLivesProvider provider,
  ) {
    return call(id: provider.id, page: provider.page, size: provider.size);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchGroupLivesProvider';
}

/// See also [fetchGroupLives].
class FetchGroupLivesProvider
    extends AutoDisposeFutureProvider<Paged<GroupLiveRoom>> {
  /// See also [fetchGroupLives].
  FetchGroupLivesProvider({
    required String id,
    required int page,
    required int size,
  }) : this._internal(
         (ref) => fetchGroupLives(
           ref as FetchGroupLivesRef,
           id: id,
           page: page,
           size: size,
         ),
         from: fetchGroupLivesProvider,
         name: r'fetchGroupLivesProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$fetchGroupLivesHash,
         dependencies: FetchGroupLivesFamily._dependencies,
         allTransitiveDependencies:
             FetchGroupLivesFamily._allTransitiveDependencies,
         id: id,
         page: page,
         size: size,
       );

  FetchGroupLivesProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
    required this.page,
    required this.size,
  }) : super.internal();

  final String id;
  final int page;
  final int size;

  @override
  Override overrideWith(
    FutureOr<Paged<GroupLiveRoom>> Function(FetchGroupLivesRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchGroupLivesProvider._internal(
        (ref) => create(ref as FetchGroupLivesRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
        page: page,
        size: size,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Paged<GroupLiveRoom>> createElement() {
    return _FetchGroupLivesProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchGroupLivesProvider &&
        other.id == id &&
        other.page == page &&
        other.size == size;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);
    hash = _SystemHash.combine(hash, page.hashCode);
    hash = _SystemHash.combine(hash, size.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchGroupLivesRef on AutoDisposeFutureProviderRef<Paged<GroupLiveRoom>> {
  /// The parameter `id` of this provider.
  String get id;

  /// The parameter `page` of this provider.
  int get page;

  /// The parameter `size` of this provider.
  int get size;
}

class _FetchGroupLivesProviderElement
    extends AutoDisposeFutureProviderElement<Paged<GroupLiveRoom>>
    with FetchGroupLivesRef {
  _FetchGroupLivesProviderElement(super.provider);

  @override
  String get id => (origin as FetchGroupLivesProvider).id;
  @override
  int get page => (origin as FetchGroupLivesProvider).page;
  @override
  int get size => (origin as FetchGroupLivesProvider).size;
}

String _$fetchGroupAppliedUsersHash() =>
    r'ed704cdf54109a77f89a8f40aa05d5bab4f9ebc3';

/// See also [fetchGroupAppliedUsers].
@ProviderFor(fetchGroupAppliedUsers)
const fetchGroupAppliedUsersProvider = FetchGroupAppliedUsersFamily();

/// See also [fetchGroupAppliedUsers].
class FetchGroupAppliedUsersFamily extends Family<AsyncValue<List<UserInfo>>> {
  /// See also [fetchGroupAppliedUsers].
  const FetchGroupAppliedUsersFamily();

  /// See also [fetchGroupAppliedUsers].
  FetchGroupAppliedUsersProvider call({required String id}) {
    return FetchGroupAppliedUsersProvider(id: id);
  }

  @override
  FetchGroupAppliedUsersProvider getProviderOverride(
    covariant FetchGroupAppliedUsersProvider provider,
  ) {
    return call(id: provider.id);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchGroupAppliedUsersProvider';
}

/// See also [fetchGroupAppliedUsers].
class FetchGroupAppliedUsersProvider
    extends AutoDisposeFutureProvider<List<UserInfo>> {
  /// See also [fetchGroupAppliedUsers].
  FetchGroupAppliedUsersProvider({required String id})
    : this._internal(
        (ref) =>
            fetchGroupAppliedUsers(ref as FetchGroupAppliedUsersRef, id: id),
        from: fetchGroupAppliedUsersProvider,
        name: r'fetchGroupAppliedUsersProvider',
        debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
            ? null
            : _$fetchGroupAppliedUsersHash,
        dependencies: FetchGroupAppliedUsersFamily._dependencies,
        allTransitiveDependencies:
            FetchGroupAppliedUsersFamily._allTransitiveDependencies,
        id: id,
      );

  FetchGroupAppliedUsersProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final String id;

  @override
  Override overrideWith(
    FutureOr<List<UserInfo>> Function(FetchGroupAppliedUsersRef provider)
    create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchGroupAppliedUsersProvider._internal(
        (ref) => create(ref as FetchGroupAppliedUsersRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<UserInfo>> createElement() {
    return _FetchGroupAppliedUsersProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchGroupAppliedUsersProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchGroupAppliedUsersRef
    on AutoDisposeFutureProviderRef<List<UserInfo>> {
  /// The parameter `id` of this provider.
  String get id;
}

class _FetchGroupAppliedUsersProviderElement
    extends AutoDisposeFutureProviderElement<List<UserInfo>>
    with FetchGroupAppliedUsersRef {
  _FetchGroupAppliedUsersProviderElement(super.provider);

  @override
  String get id => (origin as FetchGroupAppliedUsersProvider).id;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
