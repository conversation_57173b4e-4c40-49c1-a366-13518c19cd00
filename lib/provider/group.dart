import 'package:flutter_riverpod/flutter_riverpod.dart' show Ref, WidgetRef;
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '/feat/share/builder.dart';
import '/internals/methods.dart' show handleExceptions;
import '/internals/variables.dart' show globalClipboardText;
import '/models/group.dart';
import '/models/user.dart';
import '/provider/api.dart'
    show Paged, apiScoopProviderProvider, apiServiceProvider;
import '/provider/user.dart' show userRepoProvider;
import '/ui/widgets/app_loading.dart';

part 'group.g.dart';

@riverpod
Future<Group> fetchGroup(
  Ref ref, {
  required String id,
}) {
  return ref.read(apiServiceProvider).getGroupInfo(id: id);
}

@riverpod
Future<Paged<Group>> fetchGroupList(
  Ref ref, {
  required GroupType type,
  required int page,
  required int size,
}) {
  return ref
      .read(apiServiceProvider)
      .getGroupList(type: type, page: page, size: size);
}

@riverpod
Future<Paged<UserInfo>> fetchGroupUserList(
  Ref ref, {
  required String? id,
  required int page,
  required int size,
  required bool includeSelf,
}) {
  if (id == null) {
    return Future.value(Paged.empty());
  }
  return ref
      .read(apiServiceProvider)
      .getGroupUsers(id: id, page: page, size: size, includeSelf: includeSelf);
}

@riverpod
Future<Paged<GroupLiveRoom>> fetchGroupLives(
  Ref ref, {
  required String id,
  required int page,
  required int size,
}) {
  return ref
      .read(apiServiceProvider)
      .getGroupLives(id: id, page: page, size: size);
}

@riverpod
Future<List<UserInfo>> fetchGroupAppliedUsers(
  Ref ref, {
  required String id,
}) {
  return ref.read(apiServiceProvider).getGroupAppliedUsers(id: id);
}

final groupItemProvider = Provider.autoDispose<Group>(
  (_) => throw UnimplementedError(),
);

extension GroupExtension on Group {
  Future<ShareResult> share(WidgetRef ref) async {
    final user = ref.watch(userRepoProvider);
    if (user == null) {
      return const ShareResult('', ShareResultStatus.dismissed);
    }
    final builder = ShareBuilder()
        .addPath(ShareBuilder.methodGroup)
        .addPath(id)
        .addQueryParameter(ShareBuilder.keySource, ShareBuilder.sourceApp)
        .addQueryParameter(ShareBuilder.keyUserId, user.rawUserId);
    if (user.referCode.isNotEmpty) {
      builder.addQueryParameter(ShareBuilder.keyReferCode, user.referCode);
    }
    final share = builder.build();
    try {
      final short = await AppLoading.run(
        () => ref.read(apiScoopProviderProvider).putShortUrl(share.toString()),
      );
      globalClipboardText = short;
      return ShareBuilder.shareUri(Uri.parse(short));
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      globalClipboardText = share.toString();
      return ShareBuilder.shareUri(share);
    }
  }
}
