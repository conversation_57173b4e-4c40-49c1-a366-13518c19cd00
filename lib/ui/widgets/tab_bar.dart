import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_ui/me_ui.dart';

import 'divider.dart';

class AppTabBar<T> extends StatelessWidget {
  const AppTabBar({
    super.key,
    required this.items,
    required this.selected,
    required this.onSelected,
    required this.displayText,
    this.withDivider = true,
    this.width = double.infinity,
    this.height = 32.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 24.0),
    this.tabMargin = const EdgeInsetsDirectional.only(end: 24.0),
  });

  final List<T> items;
  final T selected;
  final void Function(T item) onSelected;
  final String Function(BuildContext context, T item) displayText;
  final bool withDivider;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? tabMargin;

  @override
  Widget build(BuildContext context) {
    final padding = this.padding?.resolve(Directionality.of(context));
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: width,
          height: height,
          padding: padding?.copyWith(left: 0.0, right: 0.0),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                if (padding?.left case final p?) Gap.h(p),
                ...items.map((item) {
                  final isSelected = item == selected;
                  return Builder(
                    builder: (context) => Tapper(
                      onTap: () {
                        onSelected(item);
                        Scrollable.ensureVisible(
                          context,
                          duration: kTabScrollDuration,
                        );
                      },
                      child: AnimatedContainer(
                        duration: kTabScrollDuration,
                        margin: tabMargin,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: isSelected
                                ? BorderSide(color: context.themeColor)
                                : BorderSide.none,
                          ),
                        ),
                        child: AnimatedDefaultTextStyle(
                          style: context.textTheme.headlineSmall!.copyWith(
                            color: isSelected
                                ? null
                                : context.textTheme.bodySmall?.color,
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                          duration: kTabScrollDuration,
                          child: PlaceholderText(displayText(context, item)),
                        ),
                      ),
                    ),
                  );
                }),
                if (padding?.right case final p?) Gap.h(p),
              ],
            ),
          ),
        ),
        if (withDivider) const GradientDivider(),
      ],
    );
  }
}
