import 'package:flutter/material.dart';
import 'package:scoop/exports.dart';

@FFAutoImport()
import 'package:scoop/models/group.dart' show Group;

import 'package:sliver_tools/sliver_tools.dart';

import '/models/group.dart' hide Group;
import '/models/user.dart';
import '/provider/api.dart';
import '/provider/group.dart';
import '/provider/live.dart';
import '/provider/token.dart';
import 'info.dart';

Widget _buildTitleText(BuildContext context, String text) {
  return Padding(
    padding: const EdgeInsets.only(bottom: 10.0),
    child: Text(
      text,
      style: TextStyle(
        color: context.iconTheme.color,
        fontSize: 18.0,
        height: 1.0,
      ),
    ),
  );
}

Future<void> _goLive(WidgetRef ref, Group group) async {
  final current = ref.read(roomInstanceProvider);
  if (current?.groupId == group.id) {
    meNavigator.removeNamedAndPushAndRemoveUntil(
      Routes.liveRoom.name,
      predicate: (_) => true,
    );
    return;
  }
  await AppLoading.run(
    () => ref
        .read(roomProvider)
        .leaveAndCreateRoom(private: false, groupId: group.id),
  );
  ref.invalidate(fetchGroupLivesProvider);
  meNavigator.removeNamedAndPushAndRemoveUntil(
    Routes.liveRoom.name,
    predicate: (_) => true,
  );
}

@FFRoute(name: '/group/detail')
class GroupDetailPage extends ConsumerStatefulWidget {
  const GroupDetailPage({
    super.key,
    required this.group,
  });

  final Group group;

  @override
  ConsumerState<GroupDetailPage> createState() => _GroupDetailPageState();
}

class _GroupDetailPageState extends ConsumerState<GroupDetailPage> {
  late Group group = widget.group;
  bool _inTheGroup = true;

  @override
  void initState() {
    super.initState();
    _checkInTheGroup();
  }

  Future<void> _checkInTheGroup() async {
    try {
      await Future.wait(
        [
          ref
              .read(fetchGroupProvider(id: group.id).future)
              .then((res) => group = res),
          ref.read(
            fetchGroupUserListProvider(
              id: group.id,
              page: 1,
              size: 6,
              includeSelf: true,
            ).future,
          ),
          ref.read(
            fetchGroupLivesProvider(
              id: group.id,
              page: 1,
              size: 20,
            ).future,
          ),
        ],
        eagerError: true,
      );
    } on ApiException catch (e) {
      if (e.code == ApiException.errorGroupNotJoined) {
        showErrorToast(globalL10n.toastErrorGroupNotJoined);
        if (_inTheGroup && mounted) {
          _inTheGroup = false;
          context.navigator.pop();
        }
        return;
      }
      rethrow;
    } finally {
      safeSetState(() {});
    }
  }

  Future<void> _onRefresh() async {
    ref.invalidate(fetchGroupProvider(id: group.id));
    ref.invalidate(fetchGroupUserListProvider);
    ref.invalidate(fetchGroupAppliedUsersProvider);
    ref.invalidate(fetchGroupLivesProvider);
    await _checkInTheGroup();
  }

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      overrides: [groupItemProvider.overrideWithValue(group)],
      child: RefreshIndicator(
        onRefresh: _onRefresh,
        child: const _Page(),
      ),
    );
  }
}

class _Page extends ConsumerWidget {
  const _Page();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final group = ref.watch(groupItemProvider);
    final me = ref.watch(userRepoProvider);
    return AppScaffold(
      titleBuilder: (context) => Row(
        spacing: 8.0,
        children: [
          if (!isSealed)
            Expanded(
              child: RippleTap(
                onLongPress: () => copyAndToast(group.id),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12.0,
                  vertical: 6.0,
                ),
                child: Text(
                  '(${group.id})',
                  style: TextStyle(
                    color: context.textTheme.bodyMedium?.color?.withValues(
                      alpha: 0.2,
                    ),
                    fontSize: 10.0,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            )
          else
            const Spacer(),
          IconButton(
            onPressed: () => showModalBottomSheet(
              context: context,
              builder: (context) => ProviderScope(
                overrides: [groupItemProvider.overrideWithValue(group)],
                child: const _GroupActionSheet(),
              ),
            ),
            icon: const Icon(Icons.menu_rounded),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: CustomScrollView(
          slivers: [
            const SliverGap.v(16.0),
            const SliverToBoxAdapter(child: GroupInfo(textOverflow: null)),
            const SliverGap.v(30.0),
            const _GroupMembers(),
            if (group.isCreator(me) && group.needConfirm)
              const _GroupAppliedUsers(),
            const SliverGap.v(40.0),
            const _GroupLives(),
          ],
        ),
      ),
      bottomButtonBuilder: (context) => DecoratedBox(
        decoration: bottomBarDecoration(
          context,
          backgroundColor: context.theme.scaffoldBackgroundColor,
        ),
        child: Row(
          spacing: 6.0,
          children: [
            if (group.isJoined)
              Expanded(
                flex: 2,
                child: ThemeTextButton(
                  onPressed: () {
                    group.share(ref);
                  },
                  color: context.textTheme.bodyMedium?.color,
                  textStyle: TextStyle(
                    color: context.theme.scaffoldBackgroundColor,
                  ),
                  child: Row(
                    spacing: 8.0,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Assets.icons.buttonShare.svg(width: 16.0, height: 16.0),
                      Text(context.l10n.labelShare),
                    ],
                  ),
                ),
              ),
            if (group.isCreator(me))
              Expanded(
                flex: 3,
                child: ThemeTextButton(
                  onPressed: () => _goLive(ref, group),
                  text: context.l10n.labelGoLive,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _GroupMembers extends ConsumerStatefulWidget {
  const _GroupMembers();

  @override
  ConsumerState<_GroupMembers> createState() => _GroupMembersState();
}

class _GroupMembersState extends ConsumerState<_GroupMembers>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final group = ref.watch(groupItemProvider);
    const size = 5;
    final result = ref.watch(
      fetchGroupUserListProvider(
        id: group.id,
        page: 1,
        size: size,
        includeSelf: true,
      ),
    );
    return MultiSliver(
      pushPinnedChildren: true,
      children: [
        SliverPinnedHeader(
          child: ColoredBox(
            color: context.theme.scaffoldBackgroundColor,
            child: Row(
              spacing: 6.0,
              children: [
                _buildTitleText(context, context.l10n.titleMembers),
                Container(
                  margin: const EdgeInsets.only(bottom: 10.0),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 4.0,
                    vertical: 2.0,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.0),
                    color: context.theme.cardColor,
                  ),
                  child: Text(
                    '${result.valueOrNull?.total ?? '-'}',
                    style: TextStyle(
                      color: context.themeColor,
                      fontSize: 10.0,
                      fontWeight: FontWeight.bold,
                      height: 1.0,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        SliverToBoxAdapter(
          child: Tapper(
            onTap: () {
              final total = result.valueOrNull?.total;
              if (total == null) {
                return;
              }
              showModalBottomSheet(
                context: context,
                scrollControlDisabledMaxHeightRatio: 0.8,
                useSafeArea: false,
                builder: (context) => _GroupMembersSheet(group),
              );
            },
            child: Row(
              spacing: 4.0,
              children: [
                if (result.valueOrNull case final result?)
                  ...result.list.map(
                    (e) => UserAvatar(
                      user: e,
                      dimension: 46.0,
                      backgroundColor: context.theme.focusColor,
                    ),
                  )
                else
                  ...List.generate(
                    group.userCount.min(size),
                    (_) => MEShimmer(
                      child: Container(
                        width: 46.0,
                        height: 46.0,
                        decoration: BoxDecoration(
                          color: context.theme.cardColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ),
                if (group.userCount - size case final count when count > 0)
                  Container(
                    width: 46.0,
                    height: 46.0,
                    padding: const EdgeInsets.all(2.0),
                    decoration: BoxDecoration(
                      color: context.theme.cardColor,
                      shape: BoxShape.circle,
                    ),
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        '+$count',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          height: 1.0,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _GroupAppliedUsers extends ConsumerWidget {
  const _GroupAppliedUsers();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final group = ref.watch(groupItemProvider);
    final result = ref.watch(fetchGroupAppliedUsersProvider(id: group.id));
    return result.maybeWhen(
      data: (data) {
        if (data.isEmpty) {
          return const SliverToBoxAdapter();
        }
        return MultiSliver(
          children: [
            const SliverGap.v(40.0),
            SliverPinnedHeader(
              child: ColoredBox(
                color: context.theme.scaffoldBackgroundColor,
                child: _buildTitleText(context, context.l10n.titleJoinRequests),
              ),
            ),
            const SliverGap.v(4.0),
            SliverList.list(
              children: data
                  .map(
                    (user) => ProviderScope(
                      overrides: [
                        _groupAppliedUserItemProvider.overrideWithValue(user),
                      ],
                      child: const _GroupAppliedUserItem(),
                    ),
                  )
                  .toList(),
            ),
          ],
        );
      },
      orElse: () => const SliverToBoxAdapter(),
    );
  }
}

final _groupAppliedUserItemProvider = Provider.autoDispose<UserInfo>(
  (_) => throw UnimplementedError(),
);

class _GroupAppliedUserItem extends ConsumerStatefulWidget {
  const _GroupAppliedUserItem();

  @override
  ConsumerState createState() => _GroupAppliedUserItemState();
}

class _GroupAppliedUserItemState extends ConsumerState<_GroupAppliedUserItem> {
  bool _handling = false;

  Future<void> _handleRequest(UserInfo user, bool value) async {
    if (_handling) {
      return;
    }
    setState(() {
      _handling = true;
    });
    try {
      final group = ref.read(groupItemProvider);
      await ref
          .read(apiServiceProvider)
          .updateGroupAppliedUser(
            groupId: group.id,
            userId: user.rawUserId,
            approved: value,
          );
      ref.invalidate(fetchGroupProvider(id: group.id));
      ref.invalidate(fetchGroupUserListProvider);
      ref.invalidate(fetchGroupAppliedUsersProvider);
      ref.invalidate(fetchGroupLivesProvider);
    } finally {
      safeSetState(() {
        _handling = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(_groupAppliedUserItemProvider);
    return Container(
      height: 46.0,
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        borderRadius: RadiusConstants.max,
        color: context.colorScheme.surface,
      ),
      child: Row(
        spacing: 6.0,
        children: [
          AspectRatio(
            aspectRatio: 1.0,
            child: UserAvatar(user: user),
          ),
          Expanded(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              alignment: AlignmentDirectional.centerStart,
              child: Text(
                user.name,
                style: const TextStyle(
                  fontSize: 12.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          ThemeTextButton(
            onPressed: () => _handleRequest(user, false),
            width: 48.0,
            height: 30.0,
            color: context.theme.dividerColor,
            child: const Icon(Icons.clear),
          ),
          ThemeTextButton(
            onPressed: () => _handleRequest(user, true),
            width: 96.0,
            height: 30.0,
            child: _handling
                ? Container(
                    padding: const EdgeInsets.all(8.0),
                    alignment: Alignment.center,
                    child: const AspectRatio(
                      aspectRatio: 1.0,
                      child: CircularProgressIndicator(
                        strokeWidth: 3.0,
                        color: Colors.black,
                      ),
                    ),
                  )
                : const Icon(Icons.check, color: Colors.black),
          ),
        ],
      ),
    );
  }
}

final _groupLiveItemProvider = Provider.autoDispose<GroupLiveRoom>(
  (_) => throw UnimplementedError(),
);

class _GroupLives extends ConsumerWidget {
  const _GroupLives();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final group = ref.watch(groupItemProvider);
    final me = ref.watch(userRepoProvider);
    const size = 20;
    final totalLives = ref.watch(
      fetchGroupLivesProvider(id: group.id, page: 1, size: size),
    );
    return MultiSliver(
      pushPinnedChildren: true,
      children: [
        SliverPinnedHeader(
          child: ColoredBox(
            color: context.theme.scaffoldBackgroundColor,
            child: _buildTitleText(
              context,
              context.l10n.titleRecentLivestreams,
            ),
          ),
        ),
        if (totalLives.valueOrNull?.total == 0 && !totalLives.isLoading)
          if (group.isCreator(me))
            SliverToBoxAdapter(
              child: _buildStartLiveButton(context, ref, group),
            )
          else
            SliverEmptyView(message: context.l10n.textGroupLiveListEmpty),
        SliverList.builder(
          itemCount: totalLives.valueOrNull?.total ?? 3,
          itemBuilder: (context, index) {
            final page = index ~/ size + 1;
            final indexInPage = index % size;
            final result = ref.watch(
              fetchGroupLivesProvider(
                id: group.id,
                page: page,
                size: size,
              ),
            );
            return result.maybeWhen(
              data: (data) {
                if (indexInPage >= data.list.length) {
                  return null;
                }
                final item = data.list[indexInPage];
                return ProviderScope(
                  overrides: [
                    _groupLiveItemProvider.overrideWithValue(item),
                  ],
                  child: const _GroupLiveItem(),
                );
              },
              orElse: () => const _GroupLiveItemShimmer(),
            );
          },
        ),
        const SliverGap.v(24.0),
      ],
    );
  }

  Widget _buildStartLiveButton(
    BuildContext context,
    WidgetRef ref,
    Group group,
  ) {
    return RippleTap(
      onTap: () => _goLive(ref, group),
      height: 86.0,
      color: context.theme.cardColor,
      child: Row(
        spacing: 10.0,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 36.0,
            height: 30.0,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.0),
              color: context.themeColor,
            ),
            child: Assets.icons.buttonMicOn.svg(width: 16.0),
          ),
          Text(
            context.l10n.labelGroupStartLivestream,
            style: TextStyle(
              color: context.themeColor,
              fontSize: 18.0,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

class _GroupLiveItem extends ConsumerWidget {
  const _GroupLiveItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final group = ref.watch(groupItemProvider);
    final live = ref.watch(_groupLiveItemProvider);
    return RippleTap(
      onTap: live.ongoing
          ? () async {
              if (live.id != ref.read(roomInstanceProvider)?.id) {
                await AppLoading.run(
                  () => ref.read(roomProvider).leaveAndJoinRoom(live.id),
                );
              }
              meNavigator.removeNamedAndPushAndRemoveUntil(
                Routes.liveRoom.name,
                predicate: (_) => true,
              );
            }
          : null,
      height: 86.0,
      margin: const EdgeInsets.only(bottom: 10.0),
      padding: const EdgeInsets.symmetric(horizontal: 18.0, vertical: 20.0),
      borderRadius: BorderRadius.circular(20.0),
      color: context.theme.cardColor,
      child: Row(
        spacing: 8.0,
        children: [
          Column(
            spacing: 6.0,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                live.displayName
                    .or(context.l10n.labelLiveDisplayNameFallback(group.name))
                    .censored,
                style: const TextStyle(fontWeight: FontWeight.bold),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              if (live.ongoing)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6.0,
                    vertical: 2.0,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: RadiusConstants.max,
                    color: context.themeColor,
                  ),
                  child: Row(
                    spacing: 4.0,
                    children: [
                      Assets.lottie.voice.lottie(width: 10.0, height: 10.0),
                      Text(
                        context.l10n.labelLiveIndicator,
                        style: const TextStyle(
                          color: Colors.black,
                          fontSize: 12.0,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                )
              else
                Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: live.openTime.format(format: 'MMM d, HH:mm'),
                      ),
                      const TextSpan(text: ' - '),
                      TextSpan(
                        text: live.closeTime!.toLocal().run(
                          (it) => it.format(
                            format: it.isSameDateAs(live.openTime.toLocal())
                                ? 'HH:mm'
                                : 'MMM d, HH:mm',
                          ),
                        ),
                      ),
                    ],
                  ),
                  style: TextStyle(color: context.themeColor, fontSize: 10.0),
                ),
            ],
          ),
          Expanded(
            child: Row(
              spacing: 4.0,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ...live.tokens
                    .apply((it) => it.sublist(0, it.length.min(3)))
                    .map((e) {
                      final result = ref.watch(
                        fetchTokenObjectProvider(
                          address: e.address,
                          chainId: e.chain,
                        ),
                      );
                      final token = result.valueOrNull;
                      return Tapper(
                        onTap: () => context.navigator.pushNamed(
                          Routes.tokenDetail.name,
                          arguments: Routes.tokenDetail.d(
                            token: result.valueOrNull,
                            address: token?.address ?? e.address,
                            chainId: token?.chainId ?? e.chain,
                          ),
                        ),
                        child: result.maybeWhen(
                          data: (data) => TokenIcon(
                            dimension: 48.0,
                            token: data,
                            chainId: data?.chainId ?? e.chain,
                            logo: data?.logo ?? e.icon,
                            symbol: data?.symbol ?? e.symbol,
                          ),
                          orElse: () => const TokenIconShimmer(dimension: 48.0),
                        ),
                      );
                    }),
                if (live.tokens.length case final count when count > 3)
                  AdditionalCounter(count: count - 3),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _GroupLiveItemShimmer extends StatelessWidget {
  const _GroupLiveItemShimmer();

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10.0),
      height: 86.0,
      child: Stack(
        fit: StackFit.expand,
        children: [
          MEShimmer(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.0),
                color: context.theme.cardColor,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 18.0,
              vertical: 20.0,
            ),
            child: Row(
              spacing: 8.0,
              children: [
                Column(
                  spacing: 6.0,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 50.0,
                      height: 22.0,
                      color: context.theme.cardColor,
                    ),
                    Container(
                      width: 68.0,
                      height: 14.0,
                      color: context.theme.cardColor,
                    ),
                  ],
                ),
                Expanded(
                  child: Row(
                    spacing: 4.0,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: List.generate(
                      3,
                      (_) => MEShimmer(
                        child: Container(
                          width: 46.0,
                          height: 46.0,
                          decoration: BoxDecoration(
                            color: context.theme.cardColor,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

final _groupMemberItemProvider = Provider.autoDispose<UserInfo>(
  (_) => throw UnimplementedError(),
);

class _GroupMembersSheet extends ConsumerStatefulWidget {
  const _GroupMembersSheet(this.group);

  final Group group;

  @override
  ConsumerState<_GroupMembersSheet> createState() => _GroupMembersSheetState();
}

class _GroupMembersSheetState extends ConsumerState<_GroupMembersSheet> {
  Group get group => widget.group;

  final int size = 20;
  late final _listProvider = fetchGroupUserListProvider(
    id: group.id,
    page: 1,
    size: size,
    includeSelf: true,
  );

  @override
  void initState() {
    super.initState();
    Future(() async {
      try {
        await ref.read(_listProvider.future);
      } on ApiException catch (e) {
        if (e.code == ApiException.errorGroupNotJoined) {
          showErrorToast(globalL10n.toastErrorGroupNotJoined);
          if (mounted) {
            context.navigator
              ..pop()
              ..pop();
          }
          return;
        }
        rethrow;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    const size = 20;
    final totalResult = ref.watch(_listProvider);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0).add(
        const EdgeInsets.only(top: 8.0),
      ),
      child: Column(
        spacing: 8.0,
        children: [
          Row(
            children: [
              const Spacer(),
              PlaceholderText(
                '**${totalResult.valueOrNull?.total ?? group.userCount}** '
                '${context.l10n.titleMembers}',
                style: const TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Expanded(
                child: Align(
                  alignment: AlignmentDirectional.centerEnd,
                  child: CloseButton(),
                ),
              ),
            ],
          ),
          Expanded(
            child: GridView.builder(
              padding: const EdgeInsets.only(bottom: 72.0),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 1,
                crossAxisSpacing: 4.0,
                mainAxisSpacing: 12.0,
              ),
              itemCount: totalResult.valueOrNull?.total,
              itemBuilder: (context, index) {
                final page = index ~/ size + 1;
                final indexInPage = index % size;
                final result = ref.watch(
                  fetchGroupUserListProvider(
                    id: group.id,
                    page: page,
                    size: size,
                    includeSelf: true,
                  ),
                );
                return result.maybeWhen(
                  data: (data) {
                    if (indexInPage >= data.list.length) {
                      return null;
                    }
                    final user = data.list[indexInPage];
                    return ProviderScope(
                      overrides: [
                        _groupMemberItemProvider.overrideWithValue(user),
                      ],
                      child: const _GroupMemberItem(),
                    );
                  },
                  orElse: () => const _GroupMemberItemShimmer(),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _GroupMemberItem extends ConsumerWidget {
  const _GroupMemberItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(_groupMemberItemProvider);
    final me = ref.watch(userRepoProvider);
    final isSelf = user.userId == me?.userId;

    return RippleTap(
      onTap: () {
        if (isSelf) {
          return;
        }
        context.navigator.pushNamed(
          Routes.userDetail.name,
          arguments: Routes.userDetail.d(user: user),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            UserAvatar(
              user: user,
              dimension: 60.0,
              backgroundColor: context.theme.canvasColor,
            ),
            const Gap.v(8.0),
            Text(
              user.name,
              style: TextStyle(
                color: isSelf ? context.themeColor : null,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _GroupMemberItemShimmer extends StatelessWidget {
  const _GroupMemberItemShimmer();

  @override
  Widget build(BuildContext context) {
    return MEShimmer(
      child: Container(
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          color: context.colorScheme.surface,
        ),
      ),
    );
  }
}

class _GroupActionSheet extends ConsumerStatefulWidget {
  const _GroupActionSheet();

  @override
  ConsumerState<_GroupActionSheet> createState() => _GroupActionSheetState();
}

class _GroupActionSheetState extends ConsumerState<_GroupActionSheet> {
  late final group = ref.read(groupItemProvider);
  late final me = ref.read(userRepoProvider);
  late final isCreator = group.isCreator(me);

  Future<void> _leaveGroup() async {
    final group = ref.read(groupItemProvider);
    meNavigator.pop();
    final confirmed = await showDialog(
      context: context,
      builder: (context) => AlertDialog.adaptive(
        title: Text(context.l10n.titleConfirmation),
        content: PlaceholderText(
          isCreator
              ? context.l10n.textConfirmDismissGroup
              : context.l10n.textConfirmLeaveGroup,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(context.l10n.labelCancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(context.l10n.labelConfirm),
          ),
        ],
      ),
    );
    if (confirmed != true) {
      return;
    }
    await AppLoading.run(
      () => globalContainer.read(apiServiceProvider).leaveGroup(id: group.id),
    );
    globalContainer.invalidate(fetchGroupListProvider);
    meNavigator.pop();
    showToast(globalL10n.textGroupLeft);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isCreator)
            RippleTap(
              onTap: () async {
                meNavigator.pop();
                final result = await meNavigator.pushNamed(
                  Routes.groupCreateOrEdit.name,
                  arguments: Routes.groupCreateOrEdit.d(group: group),
                );
                if (result is Group) {
                  meNavigator.pushReplacementNamed(
                    Routes.groupDetail.name,
                    arguments: Routes.groupDetail.d(group: result),
                  );
                }
              },
              height: 64.0,
              borderRadius: BorderRadius.circular(8.0),
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: Row(
                spacing: 16.0,
                children: [
                  Assets.icons.buttonEdit.svg(width: 24.0, height: 24.0),
                  Expanded(
                    child: Text(
                      context.l10n.labelEditGroup,
                      style: context.textTheme.headlineSmall,
                    ),
                  ),
                ],
              ),
            ),
          RippleTap(
            onTap: () => _leaveGroup(),
            height: 64.0,
            borderRadius: BorderRadius.circular(8.0),
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: Row(
              spacing: 16.0,
              children: [
                Assets.icons.buttonLeave.svg(width: 24.0, height: 24.0),
                Expanded(
                  child: Text(
                    isCreator
                        ? context.l10n.labelDismissGroup
                        : context.l10n.labelLeaveGroup,
                    style: context.textTheme.headlineSmall,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
