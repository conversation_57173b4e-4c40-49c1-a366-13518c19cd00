import 'package:flutter/material.dart';
import 'package:scoop/exports.dart';
import 'package:scoop/provider/live.dart';

@FFAutoImport()
import '/models/live.dart' show LiveRoomSummary, LiveRoomUser;

@FFRoute(name: '/live/room/summary')
class LiveSummaryPage extends ConsumerStatefulWidget {
  const LiveSummaryPage(this.summary, {super.key});

  final LiveRoomSummary summary;

  @override
  ConsumerState<LiveSummaryPage> createState() => _LiveSummaryPageState();
}

class _LiveSummaryPageState extends ConsumerState<LiveSummaryPage> {
  LiveRoomSummary get summary => widget.summary;

  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(1.seconds, (_) {
      if (!mounted) {
        return;
      }
      safeSetState(() {});
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final int duration;
    if (summary.duration case final d?) {
      duration = d;
    } else if (summary.closeTime case final closeTime?) {
      duration = closeTime.difference(summary.openTime).inSeconds;
    } else {
      duration = DateTime.now().difference(summary.openTime).inSeconds;
    }
    return AppScaffold(
      automaticallyImplyLeading: false,
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          spacing: 10.0,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 10.0,
                vertical: 4.0,
              ),
              decoration: BoxDecoration(
                borderRadius: RadiusConstants.max,
                color: context.meTheme.failingColor,
              ),
              child: Text(
                summary.closeTime == null
                    ? context.l10n.titleLiveLeaved
                    : context.l10n.titleLiveEnded,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            AnimatedText.rich(
              TextSpan(
                children: [
                  if (summary.private)
                    WidgetSpan(
                      alignment: PlaceholderAlignment.middle,
                      child: Padding(
                        padding: const EdgeInsetsDirectional.only(end: 4.0),
                        child: Icon(
                          Icons.lock_outline_rounded,
                          color: context.textTheme.bodyMedium?.color,
                        ),
                      ),
                    ),
                  TextSpan(text: summary.name),
                ],
              ),
              style: const TextStyle(
                fontSize: 24.0,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (summary.group case final group?)
              Row(
                spacing: 4.0,
                children: [
                  MEImage(
                    group.logo,
                    width: 16.0,
                    cacheWidth: 16.0.toCache(context),
                    clipOval: true,
                    fit: BoxFit.cover,
                  ),
                  Expanded(
                    child: Text(
                      group.name,
                      overflow: TextOverflow.ellipsis,
                      style: context.textTheme.bodySmall?.copyWith(
                        fontSize: 16.0,
                      ),
                    ),
                  ),
                ],
              ),
            const Gap.v(30.0),
            Expanded(
              child: Column(
                spacing: 40.0,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildRow(
                    context,
                    title: context.l10n.titleLiveEndedListeners,
                    text: '${summary.viewerCount}',
                    images: (summary.viewerCount, summary.userAvatars),
                  ),
                  _buildRow(
                    context,
                    title: context.l10n.titleLiveEndedMentionedCAs,
                    text: '${summary.caMentionedCount}',
                    images: (summary.caMentionedCount, summary.tokenLogos),
                  ),
                  _buildRow(
                    context,
                    title: context.l10n.titleLiveEndedDuration,
                    text: Formatter.toElapsed(Duration(seconds: duration)),
                    textColor: context.themeColor,
                    textTrailing: summary.closeTime == null
                        ? _buildOngoingIndicator(context)
                        : null,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomButtonBuilder: (context) => ThemeTextButton(
        onPressed: () => context.navigator.pop(),
        text: context.l10nME.okButton,
      ),
    );
  }

  Widget _buildTexts(
    BuildContext context, {
    required String title,
    required String text,
    Color? textColor,
    Widget? textTrailing,
  }) {
    return Column(
      spacing: 2.0,
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: context.textTheme.bodySmall?.copyWith(fontSize: 16.0),
        ),
        Flexible(
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: Row(
              spacing: 8.0,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  text,
                  style: TextStyle(
                    color: textColor,
                    fontSize: 30.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (textTrailing != null) textTrailing,
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRow(
    BuildContext context, {
    required String title,
    required String text,
    Color? textColor,
    Widget? textTrailing,
    (int, List<String>)? images,
    VoidCallback? onTap,
  }) {
    return Tapper(
      child: Row(
        spacing: 4.0,
        children: [
          _buildTexts(
            context,
            title: title,
            text: text,
            textColor: textColor,
            textTrailing: textTrailing,
          ),
          if (images != null)
            Expanded(
              child: Row(
                spacing: 4.0,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (images.$1 > 3) AdditionalCounter(count: images.$1 - 3),
                  ...images.$2
                      .apply((it) => it.sublist(0, it.length.min(3)))
                      .map(
                        (image) => MEImage(
                          image,
                          width: 40.0,
                          height: 40.0,
                          clipOval: true,
                          backgroundColor: context.theme.dividerColor,
                        ),
                      ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildOngoingIndicator(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 6.0,
        vertical: 2.0,
      ),
      decoration: BoxDecoration(
        borderRadius: RadiusConstants.max,
        color: context.themeColor,
      ),
      child: Row(
        spacing: 4.0,
        children: [
          Assets.lottie.voice.lottie(width: 10.0, height: 10.0),
          Text(
            context.l10n.labelLiveIndicator,
            style: const TextStyle(
              color: Colors.black,
              fontSize: 12.0,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

final _groupMemberItemProvider = Provider.autoDispose<LiveRoomUser>(
  (_) => throw UnimplementedError(),
);

class _GroupMembersSheet extends ConsumerStatefulWidget {
  const _GroupMembersSheet(this.summary);

  final LiveRoomSummary summary;

  @override
  ConsumerState<_GroupMembersSheet> createState() => _GroupMembersSheetState();
}

class _GroupMembersSheetState extends ConsumerState<_GroupMembersSheet> {
  LiveRoomSummary get summary => widget.summary;

  final int size = 20;
  late final _listProvider = fetchLiveUserListProvider(
    id: summary.name,
    page: 1,
    size: size,
    includeSelf: true,
  );

  @override
  void initState() {
    super.initState();
    Future(() async {
      try {
        await ref.read(_listProvider.future);
      } on ApiException catch (e) {
        if (e.code == ApiException.errorGroupNotJoined) {
          showErrorToast(globalL10n.toastErrorGroupNotJoined);
          if (mounted) {
            context.navigator
              ..pop()
              ..pop();
          }
          return;
        }
        rethrow;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    const size = 20;
    final totalResult = ref.watch(_listProvider);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0).add(
        const EdgeInsets.only(top: 8.0),
      ),
      child: Column(
        spacing: 8.0,
        children: [
          Row(
            children: [
              const Spacer(),
              PlaceholderText(
                '**${totalResult.valueOrNull?.total ?? group.userCount}** '
                '${context.l10n.titleMembers}',
                style: const TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Expanded(
                child: Align(
                  alignment: AlignmentDirectional.centerEnd,
                  child: CloseButton(),
                ),
              ),
            ],
          ),
          Expanded(
            child: GridView.builder(
              padding: const EdgeInsets.only(bottom: 72.0),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 1,
                crossAxisSpacing: 4.0,
                mainAxisSpacing: 12.0,
              ),
              itemCount: totalResult.valueOrNull?.total,
              itemBuilder: (context, index) {
                final page = index ~/ size + 1;
                final indexInPage = index % size;
                final result = ref.watch(
                  fetchGroupUserListProvider(
                    id: group.id,
                    page: page,
                    size: size,
                    includeSelf: true,
                  ),
                );
                return result.maybeWhen(
                  data: (data) {
                    if (indexInPage >= data.list.length) {
                      return null;
                    }
                    final user = data.list[indexInPage];
                    return ProviderScope(
                      overrides: [
                        _groupMemberItemProvider.overrideWithValue(user),
                      ],
                      child: const _GroupMemberItem(),
                    );
                  },
                  orElse: () => const _GroupMemberItemShimmer(),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _GroupMemberItem extends ConsumerWidget {
  const _GroupMemberItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(_groupMemberItemProvider);
    final me = ref.watch(userRepoProvider);
    final isSelf = user.userId == me?.userId;

    return RippleTap(
      onTap: () {
        if (isSelf) {
          return;
        }
        context.navigator.pushNamed(
          Routes.userDetail.name,
          arguments: Routes.userDetail.d(user: user),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            UserAvatar(
              user: user,
              dimension: 60.0,
              backgroundColor: context.theme.canvasColor,
            ),
            const Gap.v(8.0),
            Text(
              user.name,
              style: TextStyle(
                color: isSelf ? context.themeColor : null,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _GroupMemberItemShimmer extends StatelessWidget {
  const _GroupMemberItemShimmer();

  @override
  Widget build(BuildContext context) {
    return MEShimmer(
      child: Container(
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          color: context.colorScheme.surface,
        ),
      ),
    );
  }
}
