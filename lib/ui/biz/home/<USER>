import 'package:flutter/material.dart';
import 'package:scoop/exports.dart';

import '/api/service.dart' show ApiServiceUserToken;
import '/provider/api.dart';
import '/provider/token.dart';
import '/ui/widgets/sub_token_view.dart';
import '../token/chain.dart';

enum _EnumTab {
  tokens
  // activity,
  ;

  String displayName(BuildContext context) {
    return switch (this) {
      _EnumTab.tokens => context.l10n.textTokens,
      // _EnumTab.activity => context.l10n.textActivity,
    };
  }
}

final _selectedTabProvider = StateProvider.autoDispose<_EnumTab>(
  (_) => _EnumTab.tokens,
);

class Wallet extends StatelessWidget {
  const Wallet({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const _Header(),
        const Gap.v(24.0),
        const _Buttons(),
        const Gap.v(24.0),
        const _Tabs(),
        Expanded(
          child: MediaQuery.removePadding(
            context: context,
            removeTop: true,
            removeBottom: true,
            child: Consumer(
              builder: (context, ref, _) =>
                  switch (ref.watch(_selectedTabProvider)) {
                    _EnumTab.tokens => const _Tokens(),
                    // _EnumTab.activity => const _Tokens(),
                  },
            ),
          ),
        ),
      ],
    );
  }
}

class _Header extends ConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final walletResult = ref.watch(fetchWalletPortfolioProvider);
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 24.0,
      ).copyWith(top: context.topPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 64.0,
            alignment: AlignmentDirectional.centerStart,
            child: UserAvatar(
              showBorder: true,
              onTap: () {
                meNavigator.pushNamed(
                  Routes.userDetail.name,
                  arguments: Routes.userDetail.d(
                    user: ref.read(userRepoProvider)!,
                  ),
                );
              },
            ),
          ),
          const Gap.v(20.0),
          Row(
            children: [
              Flexible(
                child: AutoSizeText.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: '\$ ',
                        style: TextStyle(
                          color: context.meTheme.captionTextColor,
                        ),
                      ),
                      TextSpan(
                        text:
                            walletResult.valueOrNull?.totalUsd.toNumerical() ??
                            '-',
                        style: const TextStyle(fontFamily: FontFamily.mMMMono),
                      ),
                    ],
                  ),
                  style: const TextStyle(
                    fontSize: 42.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          // const Gap.v(10.0),
          // ChangeText(0.00, style: const TextStyle(fontSize: 16.0)),
        ],
      ),
    );
  }
}

class _Buttons extends ConsumerWidget {
  const _Buttons();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final chain = ref.watch(chainProvider);
    final slots = <(String, SvgGenImage, VoidCallback)>[
      (
        context.l10nME.sendButton,
        Assets.icons.buttonSend,
        () {
          meNavigator.pushNamed(Routes.walletSend.name);
        },
      ),
      (
        context.l10nME.receiveButton,
        Assets.icons.buttonReceive,
        () {
          meNavigator.pushNamed(Routes.walletReceive.name);
        },
      ),
      (
        context.l10nME.historyButton,
        Assets.icons.buttonBuy,
        () {
          final chain = ref.read(chainProvider);
          final address = ref
              .read(userRepoProvider)
              ?.walletAddressByChain(chain);
          final link =
              'https://web3.okx.com/explorer'
              '/${chain.name}/account/$address';
          launchUrlString(link, mode: LaunchMode.externalApplication);
        },
      ),
    ];
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Row(
        spacing: 6.0,
        children: slots
            .map(
              (slot) => Expanded(
                child: RippleTap(
                  onTap: () {
                    if (chain.tradable) {
                      slot.$3();
                      return;
                    }
                    showToast(context.l10n.textWalletNotAvailableForTheChain);
                  },
                  height: 48.0,
                  borderRadius: context.meTheme.borderRadius,
                  color: chain.tradable
                      ? context.theme.cardColor
                      : context.theme.focusColor,
                  textStyle: TextStyle(
                    color: chain.tradable
                        ? null
                        : context.theme.secondaryHeaderColor,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    spacing: 6.0,
                    children: [
                      Flexible(
                        child: Text(
                          slot.$1,
                          style: const TextStyle(fontSize: 16.0),
                        ),
                      ),
                      slot.$2.svg(
                        width: 12.0,
                        colorFilter: chain.tradable
                            ? null
                            : Color.lerp(
                                context.themeColor,
                                Colors.grey,
                                0.5,
                              )!.filter,
                      ),
                    ],
                  ),
                ),
              ),
            )
            .toList(),
      ),
    );
  }
}

class _Tabs extends ConsumerWidget {
  const _Tabs();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          spacing: 6.0,
          children: [
            Expanded(
              child: AppTabBar(
                items: _EnumTab.values,
                selected: ref.watch(_selectedTabProvider),
                onSelected: (item) =>
                    ref.read(_selectedTabProvider.notifier).state = item,
                displayText: (context, item) => item.displayName(context),
                withDivider: false,
              ),
            ),
            RippleTap(
              onTap: () async {
                final result = await ChainsDialog.show(context: context);
                if (result != null) {
                  ref.watch(chainProvider.notifier).state = result;
                }
              },
              height: 24.0,
              margin: const EdgeInsetsDirectional.only(end: 24.0),
              alignment: Alignment.center,
              shape: CircleBorder(
                side: BorderSide(color: context.theme.dividerColor),
              ),
              child: Consumer(
                builder: (context, ref, _) {
                  final chain = ref.watch(chainProvider);
                  return Padding(
                    padding: EdgeInsets.all(chain.hasExtraPadding ? 5.0 : 3.0),
                    child: MEImage(
                      chain.logo,
                      clipOval: false,
                      fit: BoxFit.scaleDown,
                      alternativeSVG: true,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        const GradientDivider(),
      ],
    );
  }
}

final _tokenItemProvider =
    Provider.autoDispose<MapEntry<IToken, ApiServiceUserToken?>>(
      (ref) => throw UnimplementedError(),
    );
final _refreshKey = Provider.autoDispose<GlobalKey<RefreshIndicatorState>>(
  (ref) => GlobalKey(),
);

class _Tokens extends ConsumerStatefulWidget {
  const _Tokens();

  @override
  ConsumerState<_Tokens> createState() => _TokensState();
}

class _TokensState extends ConsumerState<_Tokens> {
  static Future<void> _onRefresh(WidgetRef ref) async {
    final chain = ref.read(chainProvider);
    final address = ref.read(userRepoProvider)?.walletAddressByChain(chain);
    await writeWalletPortfolioCache(address, null);
    ref.invalidate(fetchRawWalletPortfolioProvider);
    await ref.read(fetchWalletPortfolioProvider.future);
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(userRepoProvider);
    final chain = ref.watch(chainProvider);
    final tradable = chain.tradable;
    final address = user?.walletAddressByChain(chain) ?? '';
    return RefreshIndicator(
      onRefresh: () => _onRefresh(ref),
      child: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          const SliverGap.v(10.0),
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            sliver: SliverToBoxAdapter(
              child: DefaultTextStyle.merge(
                style: context.textTheme.bodySmall?.copyWith(fontSize: 12.0),
                child: Row(
                  children: [
                    const Gap.h(60.0),
                    const Spacer(flex: 5),
                    Expanded(
                      flex: 4,
                      child: Text(
                        context.l10n.textPosition,
                        textAlign: TextAlign.end,
                      ),
                    ),
                    Expanded(
                      flex: 4,
                      child: Text(
                        context.l10n.textPnL,
                        textAlign: TextAlign.end,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (!tradable || address.isEmpty)
            SliverFillRemaining(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 48.0),
                child: Column(
                  spacing: 24.0,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    MEUIAssets.icons.warningYellow.svg(
                      width: 72.0,
                      height: 72.0,
                    ),
                    if (!tradable)
                      FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          context.l10n.textWalletNotAvailableForTheChain,
                          style: context.textTheme.headlineSmall,
                        ),
                      )
                    else ...[
                      Text(
                        context.l10n.textWalletCreateTip,
                        style: context.textTheme.headlineSmall,
                      ),
                      ThemeTextButton(
                        onPressed: () => AppLoading.run(() async {
                          try {
                            await ref
                                .read(apiServiceProvider)
                                .activateWallet(chain);
                            final user = await ref
                                .read(apiServiceProvider)
                                .getSelfUserInfo();
                            await ref
                                .read(userRepoProvider.notifier)
                                .update(user);
                          } on ApiException catch (e) {
                            TinyDialog.show(
                              text: e.message ?? '',
                              captionText: '(${e.code})',
                            );
                            rethrow;
                          } catch (e) {
                            TinyDialog.show(text: e.toString());
                            rethrow;
                          }
                          await _onRefresh(ref);
                        }),
                        text: context.l10n.labelCreate,
                      ),
                    ],
                  ],
                ),
              ),
            )
          else
            const _TokenList(),
        ],
      ),
    );
  }
}

class _TokenList extends ConsumerWidget {
  const _TokenList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final result = ref.watch(fetchWalletPortfolioProvider);
    if (result.hasError && !result.isLoading) {
      handleExceptions(error: result.error, stackTrace: result.stackTrace);
      if (result.error is! ApiException) {
        ref.read(_refreshKey).currentState?.show();
      }
    }
    return result.when(
      data: (data) {
        if (data.items.isEmpty) {
          return SliverEmptyView(
            onTap: () => _TokensState._onRefresh(ref),
            message: context.l10n.textWalletListEmpty,
          );
        }
        return SliverList.builder(
          itemCount: data.items.length,
          itemBuilder: (_, index) {
            final item = data.items.entries.elementAt(index);
            return ProviderScope(
              overrides: [_tokenItemProvider.overrideWithValue(item)],
              child: const _TokenItem(),
            );
          },
        );
      },
      loading: () => SliverList.builder(
        itemCount: 30,
        itemBuilder: (_, _) => const _TokenItemShimmer(),
      ),
      error: (e, s) {
        return SliverList.builder(
          itemCount: 30,
          itemBuilder: (_, _) => const _TokenItemShimmer(),
        );
      },
    );
  }
}

class _TokenItemShimmer extends StatelessWidget {
  const _TokenItemShimmer();

  @override
  Widget build(BuildContext context) {
    return MEShimmer(
      child: Container(
        height: 60.0,
        padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 10.0),
        child: Row(
          children: [
            AspectRatio(
              aspectRatio: 1.0,
              child: Container(
                decoration: BoxDecoration(
                  color: context.themeColor,
                  shape: BoxShape.circle,
                ),
              ),
            ),
            const Gap.h(12.0),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    flex: 5,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Gap.h(
                          86.0,
                          height: 16.0,
                          color: context.theme.cardColor,
                        ),
                        const Gap.v(4.0),
                        Gap.h(
                          56.0,
                          height: 12.0,
                          color: context.theme.cardColor,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 4,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Gap.h(
                          42.0,
                          height: 16.0,
                          color: context.theme.cardColor,
                        ),
                        const Gap.v(4.0),
                        Gap.h(
                          24.0,
                          height: 12.0,
                          color: context.theme.cardColor,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 4,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Gap.h(
                          32.0,
                          height: 16.0,
                          color: context.theme.cardColor,
                        ),
                        const Gap.v(4.0),
                        Gap.h(
                          42.0,
                          height: 12.0,
                          color: context.theme.cardColor,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _TokenItem extends ConsumerWidget {
  const _TokenItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final MapEntry(
      key: IToken token,
      value: ApiServiceUserToken? service,
    ) = ref.watch(
      _tokenItemProvider,
    );
    final chain = ref.watch(chainByNameProvider(token.chainId));
    final tokenBalance = ref.watch(
      tokenBalanceStreamProvider(chain: chain, address: token.address),
    );
    return RippleTap(
      onTap: () {
        if (token.symbol == chain.symbol) {
          context.navigator.pushNamed(
            Routes.walletSendToken.name,
            arguments: Routes.walletSendToken.d(token),
          );
        } else {
          context.navigator.pushNamed(
            Routes.tokenDetail.name,
            arguments: Routes.tokenDetail.d(
              token: null,
              chainId: token.chainId,
              address: token.address,
            ),
          );
        }
      },
      height: 60.0,
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8.0),
      child: SubTokenView(
        address: token.address,
        chainId: token.chainId,
        builder: (key, context, rt, _) {
          final marketCap = rt?.marketCap;
          final price = rt?.priceUsd ?? Decimal.parse('${token.priceUsd}');
          final valueUsd =
              price *
              (tokenBalance.valueOrNull?.realBalance ?? token.realBalance);
          final Decimal? pnlUsd;
          if (service != null) {
            pnlUsd =
                service.pnlRealized +
                price * service.totalQuantity -
                service.costAccumulate;
          } else {
            pnlUsd = null;
          }
          final changed = switch ((pnlUsd, service?.costAccumulate)) {
            (final p?, final c?) when c > Decimal.zero => p.shift(2) / c,
            _ => null,
          };
          return Row(
            children: [
              TokenIcon(
                dimension: 40.0,
                token: null,
                chainId: token.chainId,
                logo: token.logo,
                symbol: token.symbol,
              ),
              const Gap.h(12.0),
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      flex: 5,
                      child: Column(
                        spacing: 4.0,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            token.symbol,
                            style: const TextStyle(
                              fontSize: 16.0,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(
                            height: 18.0,
                            child: DefaultTextStyle.merge(
                              style: context.textTheme.bodySmall?.copyWith(
                                fontSize: 11.0,
                                height: 1.0,
                              ),
                              child: ListView(
                                padding: EdgeInsets.zero,
                                scrollDirection: Axis.horizontal,
                                children:
                                    <List<String>>[
                                          [
                                            'MC: ',
                                            marketCap?.toNumerical() ?? '-',
                                          ],
                                        ]
                                        .map(
                                          (e) => Container(
                                            margin:
                                                const EdgeInsetsDirectional.only(
                                                  end: 2.0,
                                                ),
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 6.0,
                                            ),
                                            decoration: BoxDecoration(
                                              borderRadius: RadiusConstants.max,
                                              color:
                                                  context.colorScheme.surface,
                                            ),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(e[0]),
                                                Text(
                                                  e[1],
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.white,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        )
                                        .toList(),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 4,
                      child: Column(
                        spacing: 4.0,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text.rich(
                            TextSpan(
                              children: [
                                const TextSpan(text: r'$'),
                                TextSpan(text: valueUsd.toNumerical()),
                              ],
                            ),
                            style: const TextStyle(
                              fontSize: 16.0,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            tokenBalance.valueOrNull?.realBalance.toNumerical(
                                  fractionDigits: 4,
                                ) ??
                                token.realBalance.toNumerical(
                                  fractionDigits: 4,
                                ),
                            style: context.textTheme.bodySmall?.copyWith(
                              fontSize: 12.0,
                            ),
                            textAlign: TextAlign.end,
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 4,
                      child: Column(
                        spacing: 4.0,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text.rich(
                            TextSpan(
                              children: [
                                if (pnlUsd case final v?) ...[
                                  if (v < Decimal.zero)
                                    const TextSpan(text: '-'),
                                  const TextSpan(text: r'$'),
                                  TextSpan(text: v.abs().toNumerical()),
                                ] else
                                  const TextSpan(text: r'$-'),
                              ],
                            ),
                            style: const TextStyle(
                              fontSize: 16.0,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          ChangeText(changed?.toDouble()),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
