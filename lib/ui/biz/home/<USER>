import 'dart:async';

import 'package:flutter/material.dart';
import 'package:me_misc/me_misc.dart' show meRouteObserver;
import 'package:scoop/exports.dart';
import 'package:sliver_tools/sliver_tools.dart';
import 'package:waterfall_flow/waterfall_flow.dart';

import '/models/group.dart';
import '/models/live.dart';
import '/provider/api.dart' show apiRoomProvider, apiServiceProvider;
import '/provider/group.dart';
import '/provider/live.dart';
import '../group/item.dart';
import '../live/item.dart';

enum _SocialType {
  live,
  groups;

  String displayName(BuildContext context) => switch (this) {
    _SocialType.live => context.l10n.labelLive,
    _SocialType.groups => context.l10n.labelGroups,
  };
}

final _socialTypeProvider = StateProvider.autoDispose<_SocialType>(
  (_) => _SocialType.live,
);

class Social extends ConsumerWidget {
  const Social({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final type = ref.watch(_socialTypeProvider);
    final height = context.topPadding + 64.0;
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(height),
        child: Material(
          color: context.theme.scaffoldBackgroundColor,
          child: Container(
            padding: MediaQuery.paddingOf(context).copyWith(bottom: 0.0),
            height: height,
            child: const _Header(),
          ),
        ),
      ),
      body: IndexedStack(
        index: type.index,
        children: [
          const _Live(),
          const _Groups(),
        ],
      ),
    );
  }
}

class _Header extends ConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final type = ref.watch(_socialTypeProvider);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Row(
        spacing: 16.0,
        children: [
          UserAvatar(
            showBorder: true,
            onTap: () {
              meNavigator.pushNamed(
                Routes.userDetail.name,
                arguments: Routes.userDetail.d(
                  user: ref.read(userRepoProvider)!,
                ),
              );
            },
          ),
          Expanded(
            child: MESlidingSegmentedControl(
              children: {
                for (final e in _SocialType.values)
                  e: Text(
                    e.displayName(context),
                    style: TextStyle(
                      fontWeight: type == e
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
              },
              groupValue: type,
              padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 4),
              backgroundColor: context.theme.cardColor,
              thumbColor: context.theme.scaffoldBackgroundColor,
              thumbRadius: const Radius.circular(999),
              cornerRadius: const Radius.circular(999),
              onValueChanged: (value) =>
                  ref.read(_socialTypeProvider.notifier).state = value!,
            ),
          ),
          RippleTap(
            onTap: () {
              context.navigator.pushNamed(
                Routes.scan.name,
                arguments: Routes.scan.d(
                  manager: ScanManager({AppLinkScanHandler()}),
                ),
              );
            },
            width: 42.0,
            height: 42.0,
            child: Assets.icons.buttonScan.svg(),
          ),
        ],
      ),
    );
  }
}

final _liveRoomListProvider = FutureProvider.autoDispose<List<LiveRoom>>(
  (ref) {
    if (ref.watch(userRepoProvider) == null) {
      return Future.value([]);
    }
    return ref
        .read(apiServiceProvider)
        .getLiveList(cancelToken: ref.cancelToken());
  },
);

class _Live extends ConsumerStatefulWidget {
  const _Live();

  @override
  ConsumerState<_Live> createState() => _LiveState();
}

class _LiveState extends ConsumerState<_Live>
    with RouteAware, WidgetsBindingObserver {
  final _refreshDuration = const Duration(seconds: 10);
  final _refreshKey = GlobalKey<RefreshIndicatorState>();
  Timer? _refreshTimer;
  DateTime? _lastRefreshTime;

  @override
  @mustCallSuper
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    onResume();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    meRouteObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  @mustCallSuper
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      onResume();
    } else if (state == AppLifecycleState.paused) {
      onPause();
    }
  }

  @override
  @mustCallSuper
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    meRouteObserver.unsubscribe(this);
    _refreshTimer?.cancel();
    super.dispose();
  }

  @override
  @mustCallSuper
  void didPopNext() {
    super.didPopNext();
    onResume();
  }

  @override
  @mustCallSuper
  void didPushNext() {
    super.didPushNext();
    onPause();
  }

  void onResume() {
    if (_lastRefreshTime case final time?
        when DateTime.now().difference(time) >= _refreshDuration) {
      _onRefresh(refreshSummary: false);
    }
    _setRefreshTimer();
  }

  void onPause() {
    _refreshTimer?.cancel();
  }

  void _setRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(_refreshDuration, (_) {
      _onRefresh(refreshSummary: false);
    });
  }

  Future<void> _onRefresh({required bool refreshSummary}) async {
    await Future.delayed(const Duration(milliseconds: 300));
    if (!mounted) {
      return;
    }
    _lastRefreshTime = DateTime.now();
    try {
      ref.invalidate(_liveRoomListProvider);
      bool needRefreshSummary = refreshSummary;
      if (!needRefreshSummary) {
        final p = ref.read(_summaryProvider((1, _summaryPageSize)));
        needRefreshSummary = p.hasError && !p.isReloading;
      }
      if (needRefreshSummary) {
        ref.invalidate(_summaryProvider);
      }
      await Future.wait([
        ref.read(_liveRoomListProvider.future),
        if (needRefreshSummary)
          ref.refresh(_summaryProvider((1, _summaryPageSize)).future),
      ]);
    } on ApiException catch (_) {}
  }

  @override
  Widget build(BuildContext context) {
    final result = ref.watch(_liveRoomListProvider);
    final me = ref.watch(userRepoProvider);
    final current = ref.watch(roomInstanceProvider);
    final showButton = current?.isHost(me?.userId) != true;
    return Stack(
      fit: StackFit.expand,
      children: [
        RefreshIndicator(
          key: _refreshKey,
          onRefresh: () => _onRefresh(refreshSummary: true),
          child: result.when(
            data: (data) => _buildData(context, data),
            error: (e, s) {
              if (result.valueOrNull case final data?) {
                return _buildData(context, data);
              }
              return RefreshableEmptyView(
                onTap: () => _onRefresh(refreshSummary: true),
                message: context.l10n.textLiveListError,
              );
            },
            loading: () => ListView.builder(
              padding: const EdgeInsets.symmetric(
                horizontal: 24.0,
                vertical: 10.0,
              ),
              itemCount: 4,
              itemBuilder: (context, _) => const LiveShimmer(),
            ),
          ),
        ),
        if (showButton)
          Positioned.fill(
            top: null,
            bottom: 24.0,
            child: Center(child: _buildLiveButton(context)),
          ),
      ],
    );
  }

  Widget _buildData(BuildContext context, List<LiveRoom> data) {
    return ProviderScope(
      overrides: [_liveListProvider.overrideWithValue(data)],
      child: const _LiveList(),
    );
  }

  Widget _buildLiveButton(BuildContext context) {
    return ScalableRippleTap(
      onTap: () async {
        final me = ref.read(userRepoProvider);
        if (me == null) {
          return;
        }
        final rooms = ref.read(_liveRoomListProvider).valueOrNull ?? [];
        final hosted = rooms.firstWhereOrNull((e) => e.isHost(me.userId));
        if (hosted != null) {
          await AppLoading.run(
            () => ref.read(apiRoomProvider).joinRoom(hosted.id),
          );
          meNavigator.removeNamedAndPushAndRemoveUntil(
            Routes.liveRoom.name,
            predicate: (_) => true,
          );
          return;
        }
        final result = await showModalBottomSheet(
          context: context,
          isScrollControlled: false,
          builder: (context) => const _GoLiveSheet(),
        );
        if (result == null) {
          return;
        }
        await AppLoading.run(
          () => ref.read(roomProvider).leaveAndCreateRoom(private: result),
        );
        meNavigator.removeNamedAndPushAndRemoveUntil(
          Routes.liveRoom.name,
          predicate: (_) => true,
        );
      },
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 7.0),
      borderRadius: RadiusConstants.max,
      color: context.themeColor,
      textStyle: const TextStyle(
        color: Colors.black,
        fontSize: 14.0,
        fontWeight: FontWeight.bold,
      ),
      child: Text(context.l10n.labelGoLive),
    );
  }
}

final _liveListProvider = Provider.autoDispose<List<LiveRoom>>(
  (_) => throw UnimplementedError(),
);

const _summaryPageSize = 20;
final _summaryProvider = FutureProvider.autoDispose
    .family<Paged<LiveRoomSummary>, (int, int)>(
      (ref, args) {
        if (ref.watch(userRepoProvider) == null) {
          return Future.value(Paged.empty());
        }
        final (page, size) = args;
        return ref
            .read(apiServiceProvider)
            .getLiveSummaryList(
              page: page,
              size: size,
              cancelToken: ref.cancelToken(),
            );
      },
    );

class _LiveList extends ConsumerWidget {
  const _LiveList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final list = ref.watch(_liveListProvider);
    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        const SliverGap.v(10.0),
        if (list.isEmpty)
          SliverEmptyView(
            height: 176.0,
            alignment: Alignment.center,
            message: context.l10n.textLiveListEmpty,
            onTap: () => ref.invalidate(_liveRoomListProvider),
            fillRemaining: false,
          )
        else
          MultiSliver(
            pushPinnedChildren: true,
            children: [
              _buildLiveIndicator(context),
              _buildLiveList(context, list),
            ],
          ),
        const SliverGap.v(10.0),
        MultiSliver(
          pushPinnedChildren: true,
          children: [
            _buildHistoryIndicator(context),
            _buildSummaryList(context, ref),
          ],
        ),
        const SliverGap.v(60.0),
      ],
    );
  }

  Widget _buildLiveIndicator(BuildContext context) {
    return SliverPinnedHeader(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 6.0),
        color: context.theme.scaffoldBackgroundColor,
        child: Row(
          spacing: 6.0,
          children: [
            SizedBox.square(
              dimension: 12.0,
              child: CircleAvatar(
                backgroundColor: context.themeColor.withValues(alpha: 0.3),
                child: FractionallySizedBox(
                  widthFactor: 0.4,
                  heightFactor: 0.4,
                  child: CircleAvatar(
                    backgroundColor: context.themeColor,
                  ),
                ),
              ),
            ),
            Text(
              context.l10n.labelGroupLastLiveIndicatorNow,
              style: context.textTheme.bodySmall?.copyWith(
                color: context.themeColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryIndicator(BuildContext context) {
    return SliverPinnedHeader(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 6.0),
        color: context.theme.scaffoldBackgroundColor,
        child: Row(
          spacing: 6.0,
          children: [
            const Icon(Icons.history_rounded, size: 12.0),
            Text(
              context.l10nME.historyButton,
              style: context.textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLiveList(BuildContext context, List<LiveRoom> list) {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      sliver: SliverList.builder(
        itemCount: list.length,
        itemBuilder: (context, index) => ProviderScope(
          overrides: [liveItemProvider.overrideWithValue(list[index])],
          child: const LivePreviewItem(),
        ),
      ),
    );
  }

  Widget _buildSummaryList(BuildContext context, WidgetRef ref) {
    const size = _summaryPageSize;
    final totalHistories = ref.watch(_summaryProvider((1, size)));
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      sliver: SliverWaterfallFlow(
        gridDelegate: const SliverWaterfallFlowDelegateWithMinCrossAxisExtent(
          minCrossAxisExtent: 420.0,
          crossAxisSpacing: 12.0,
        ),
        delegate: SliverChildBuilderDelegate(
          childCount: totalHistories.valueOrNull?.total ?? 3,
          (context, index) {
            final page = index ~/ size + 1;
            final indexInPage = index % size;
            final result = ref.watch(_summaryProvider((page, size)));
            return result.maybeWhen(
              data: (data) {
                if (indexInPage >= data.list.length) {
                  return null;
                }
                final item = data.list[indexInPage];
                return ProviderScope(
                  overrides: [
                    liveSummaryItemProvider.overrideWithValue(item),
                  ],
                  child: const LiveSummaryItem(),
                );
              },
              orElse: () => const LiveShimmer(),
            );
          },
        ),
      ),
    );
  }
}

class _GoLiveSheet extends StatelessWidget {
  const _GoLiveSheet();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0).copyWith(
        bottom: context.bottomPadding.max(24),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            context.l10n.labelGoLive,
            style: context.textTheme.headlineSmall,
          ),
          const Gap.v(24.0),
          _buildItem(
            context,
            onTap: (context) => context.navigator.maybePop(false),
            title: context.l10n.titlePublic,
            iconBuilder: (context) => Container(
              alignment: Alignment.center,
              color: context.themeColor,
              child: Assets.icons.buttonMicOn.svg(
                width: 20.0,
                height: 20.0,
              ),
            ),
          ),
          _buildItem(
            context,
            onTap: (context) => context.navigator.maybePop(true),
            title: context.l10n.titlePrivate,
            iconBuilder: (context) => Container(
              alignment: Alignment.center,
              color: context.themeColor,
              child: const Icon(
                Icons.lock_outline_rounded,
                color: Colors.black,
                size: 20.0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItem(
    BuildContext context, {
    required void Function(BuildContext context) onTap,
    required String title,
    required WidgetBuilder iconBuilder,
  }) {
    return RippleTap(
      onTap: () => onTap(context),
      height: 90.0,
      margin: const EdgeInsets.only(bottom: 6.0),
      padding: const EdgeInsets.all(10.0),
      borderRadius: BorderRadius.circular(20.0),
      color: context.theme.canvasColor,
      child: Row(
        spacing: 16.0,
        children: [
          AspectRatio(
            aspectRatio: 1.0,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16.0),
              child: iconBuilder(context),
            ),
          ),
          Expanded(
            child: Text(
              title,
              style: context.textTheme.headlineSmall,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

final _selectedGroupTabProvider = StateProvider.autoDispose<GroupType>(
  (_) => GroupType.owned,
);
final _groupPageControllerProvider = Provider.autoDispose<PageController>(
  (ref) {
    final controller = PageController();

    bool disposed = false;
    ref.onDispose(() {
      disposed = true;
      controller.dispose();
    });

    controller.addListener(() {
      if (disposed) {
        return;
      }
      final newTab = GroupType.values.run(
        (it) => it[controller.page?.round().min(it.length) ?? 0],
      );
      if (ref.read(_selectedGroupTabProvider) != newTab) {
        ref.read(_selectedGroupTabProvider.notifier).state = newTab;
      }
    });

    return controller;
  },
  dependencies: [_selectedGroupTabProvider],
);

class _Groups extends ConsumerStatefulWidget {
  const _Groups();

  @override
  ConsumerState<_Groups> createState() => _GroupsState();
}

class _GroupsState extends ConsumerState<_Groups> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          spacing: 8.0,
          children: [
            Expanded(
              child: AppTabBar(
                items: GroupType.values,
                selected: ref.watch(_selectedGroupTabProvider),
                onSelected: (item) => ref
                    .read(_groupPageControllerProvider)
                    .animateToPage(
                      GroupType.values.indexOf(item),
                      duration: kTabScrollDuration,
                      curve: Curves.ease,
                    ),
                displayText: (context, item) => item.displayName(context),
                withDivider: false,
              ),
            ),
            RippleTap(
              onTap: () async {
                final group = await context.navigator.pushNamed(
                  Routes.groupCreateOrEdit.name,
                );
                if (group is Group) {
                  context.navigator.pushNamed(
                    Routes.groupDetail.name,
                    arguments: Routes.groupDetail.d(group: group),
                  );
                }
                await 500.milliseconds.delay;
                if (!mounted) {
                  return;
                }
                ref.invalidate(fetchGroupListProvider);
              },
              width: 40.0,
              height: 24.0,
              margin: const EdgeInsetsDirectional.only(end: 24.0),
              padding: const EdgeInsets.all(5.0),
              borderRadius: BorderRadius.circular(20.0),
              color: context.themeColor,
              child: FittedBox(
                child: Assets.icons.buttonAdd.svg(),
              ),
            ),
          ],
        ),
        const GradientDivider(),
        Expanded(
          child: PageView(
            controller: ref.watch(_groupPageControllerProvider),
            children: GroupType.values.map(_GroupList.new).toList(),
          ),
        ),
      ],
    );
  }
}

class _GroupList extends ConsumerStatefulWidget {
  const _GroupList(this.type);

  final GroupType type;

  @override
  ConsumerState<_GroupList> createState() => _GroupListState();
}

class _GroupListState extends ConsumerState<_GroupList>
    with AutomaticKeepAliveClientMixin {
  final int size = 20;

  @override
  bool get wantKeepAlive => true;

  Future<void> _onRefresh() async {
    ref.invalidate(fetchGroupListProvider);
    await ref.read(
      fetchGroupListProvider(type: widget.type, page: 1, size: size).future,
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final totalResult = ref.watch(
      fetchGroupListProvider(type: widget.type, page: 1, size: size),
    );
    final Widget child;
    if (totalResult.valueOrNull?.total == 0) {
      child = RefreshableEmptyView(message: context.l10n.textCreateFirstGroup);
    } else {
      child = ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        itemCount: totalResult.valueOrNull?.total ?? 4,
        itemBuilder: (context, index) {
          final page = index ~/ size + 1;
          final indexInPage = index % size;
          final result = ref.watch(
            fetchGroupListProvider(type: widget.type, page: page, size: size),
          );
          return result.maybeWhen(
            data: (data) {
              if (indexInPage >= data.list.length) {
                return null;
              }
              final item = data.list[indexInPage];
              return ProviderScope(
                overrides: [
                  groupItemProvider.overrideWithValue(item),
                ],
                child: const GroupPreviewItem(),
              );
            },
            orElse: () => const GroupItemShimmer(),
          );
        },
      );
    }
    return RefreshIndicator(onRefresh: _onRefresh, child: child);
  }
}
