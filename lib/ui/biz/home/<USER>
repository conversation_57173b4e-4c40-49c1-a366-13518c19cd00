import 'package:flutter/material.dart';
import 'package:scoop/exports.dart';
import 'package:scoop/models/tokens.dart';
import 'package:scoop/provider/token.dart';

import '/ui/biz/token/item.dart';
import '../token/chain.dart';

enum _EnumTab {
  tokens
  // traders,
  ;

  String displayName(BuildContext context) {
    return switch (this) {
      _EnumTab.tokens => context.l10n.textTokens,
      // _EnumTab.traders => context.l10n.textTraders,
    };
  }
}

final _selectedTabProvider = StateProvider.autoDispose<_EnumTab>(
  (_) => _EnumTab.tokens,
);

class Discovery extends StatelessWidget {
  const Discovery({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          height: context.topPadding + 64.0,
          padding: const EdgeInsets.symmetric(
            horizontal: 24.0,
          ).copyWith(top: context.topPadding),
          color: context.theme.scaffoldBackgroundColor,
          child: Row(
            children: [
              UserAvatar(
                showBorder: true,
                onTap: () {
                  meNavigator.pushNamed(
                    Routes.userDetail.name,
                    arguments: Routes.userDetail.d(
                      user: globalContainer.read(userRepoProvider)!,
                    ),
                  );
                },
              ),
              const Gap.h(16.0),
              Expanded(
                child: RippleTap(
                  onTap: () => context.navigator.pushNamed(
                    Routes.searchAll.name,
                  ),
                  height: 42.0,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16.0,
                    vertical: 8.0,
                  ),
                  alignment: AlignmentDirectional.centerStart,
                  shape: RoundedRectangleBorder(
                    borderRadius: RadiusConstants.max,
                    side: BorderSide(color: context.theme.dividerColor),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          context.l10n.labelSearchHint,
                          style: context.textTheme.bodySmall?.copyWith(
                            fontSize: 16.0,
                          ),
                        ),
                      ),
                      Assets.icons.labelSearch.svg(width: 20.0, height: 20.0),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        const Gap.v(10.0),
        Consumer(
          builder: (context, ref, _) => AppTabBar(
            items: _EnumTab.values,
            selected: ref.watch(_selectedTabProvider),
            onSelected: (item) =>
                ref.read(_selectedTabProvider.notifier).state = item,
            displayText: (context, item) => item.displayName(context),
          ),
        ),
        const Gap.v(16.0),
        Expanded(
          child: MediaQuery.removePadding(
            context: context,
            removeTop: true,
            child: Consumer(
              builder: (context, ref, _) =>
                  switch (ref.watch(_selectedTabProvider)) {
                    _EnumTab.tokens => const _Tokens(),
                    // _EnumTab.traders => const _Traders(),
                  },
            ),
          ),
        ),
      ],
    );
  }
}

class _Tokens extends ConsumerStatefulWidget {
  const _Tokens();

  @override
  ConsumerState<_Tokens> createState() => _TokensState();
}

class _TokensState extends ConsumerState<_Tokens> {
  final _listController = ScrollController();

  @override
  void dispose() {
    _listController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final selectedCategory = ref.watch(tokenCategoryProvider);
    final selectedTimeFrame = ref.watch(tokenTimeFrameProvider);
    return Column(
      children: [
        SizedBox(
          height: 24.0,
          child: Row(
            spacing: 6.0,
            children: [
              Expanded(
                child: ListView.separated(
                  controller: _listController,
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 24.0),
                  itemCount: EnumTokenCategory.values.length,
                  itemBuilder: (context, index) {
                    final sortBy = EnumTokenCategory.values[index];
                    final selected = selectedCategory == sortBy;
                    return RippleTap(
                      onTap: () {
                        if (selected) {
                          return;
                        }
                        ref.read(tokenCategoryProvider.notifier).state = sortBy;
                        _listController.jumpTo(0);
                      },
                      borderRadius: RadiusConstants.max,
                      color: selected
                          ? context.theme.cardColor
                          : Colors.transparent,
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12.0,
                        vertical: 2.0,
                      ),
                      child: Text(
                        sortBy.displayName(context),
                        style: context.textTheme.bodySmall?.copyWith(
                          color: selected ? Colors.white : null,
                          fontSize: 14.0,
                          fontWeight: selected ? FontWeight.bold : null,
                          height: 1.0,
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (_, _) => const Gap.h(8.0),
                ),
              ),
              RippleTap(
                onTap: () async {
                  final result = await ChainsDialog.show(context: context);
                  if (result != null) {
                    ref.watch(chainProvider.notifier).state = result;
                  }
                },
                height: double.infinity,
                alignment: Alignment.center,
                shape: CircleBorder(
                  side: BorderSide(color: context.theme.dividerColor),
                ),
                child: Consumer(
                  builder: (context, ref, _) {
                    final chain = ref.watch(chainProvider);
                    return Padding(
                      padding: EdgeInsets.all(
                        chain.hasExtraPadding ? 5.0 : 3.0,
                      ),
                      child: MEImage(
                        chain.logo,
                        clipOval: false,
                        fit: BoxFit.scaleDown,
                        alternativeSVG: true,
                      ),
                    );
                  },
                ),
              ),
              RippleTap(
                onTap: () {
                  showModalBottomSheet(
                    context: context,
                    backgroundColor: context.theme.canvasColor,
                    builder: (context) => const _TokenFrameSheet(),
                  );
                },
                height: double.infinity,
                alignment: Alignment.center,
                margin: const EdgeInsetsDirectional.only(end: 24.0),
                padding: const EdgeInsetsDirectional.only(
                  start: 12.0,
                  end: 6.0,
                  top: 2.0,
                  bottom: 2.0,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: RadiusConstants.max,
                  side: BorderSide(color: context.theme.dividerColor),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      selectedTimeFrame.displayName(context),
                      style: context.textTheme.bodySmall,
                    ),
                    const Icon(
                      Icons.keyboard_arrow_down_rounded,
                      size: 16.0,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const Gap.v(10.0),
        const Expanded(child: _TokenListView()),
      ],
    );
  }
}

final _selectedTokenFrameProvider =
    StateProvider.autoDispose<EnumTokenTimeFrame>(
      (ref) => ref.read(tokenTimeFrameProvider),
    );

class _TokenFrameSheet extends ConsumerWidget {
  const _TokenFrameSheet();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selected = ref.watch(_selectedTokenFrameProvider);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0).copyWith(
        bottom: context.bottomPadding.max(24),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 24.0, bottom: 12.0),
            child: Text(
              context.l10n.textChooseTimeFrame,
              style: context.textTheme.headlineMedium,
            ),
          ),
          ...EnumTokenTimeFrame.values.map(
            (e) => RippleTap(
              onTap: () =>
                  ref.read(_selectedTokenFrameProvider.notifier).state = e,
              height: 48.0,
              borderRadius: BorderRadius.circular(8.0),
              padding: const EdgeInsets.symmetric(horizontal: 4.0),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      e.fullName(context),
                      style: context.textTheme.titleSmall,
                    ),
                  ),
                  MERadio(checked: selected == e, ignoreGesture: true),
                ],
              ),
            ),
          ),
          const Gap.v(12.0),
          ThemeTextButton(
            onPressed: () {
              ref.read(tokenTimeFrameProvider.notifier).state = selected;
              context.navigator.maybePop();
            },
            margin: const EdgeInsets.symmetric(horizontal: 4.0),
            text: context.l10nME.confirmButton,
          ),
        ],
      ),
    );
  }
}

class _TokenListView extends ConsumerWidget {
  const _TokenListView();

  Future<void> _onRefresh(WidgetRef ref) {
    return ref.refresh(fetchTokenDiscoveryListProvider.future);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final result = ref.watch(fetchTokenDiscoveryListProvider);
    return RefreshIndicator(
      onRefresh: () => _onRefresh(ref),
      child: result.when(
        data: (data) {
          if (data.list.isEmpty) {
            return RefreshableEmptyView(
              onTap: () => _onRefresh(ref),
              message: context.l10n.textDiscoveryEmpty,
            );
          }
          return _buildData(context, ref, data);
        },
        error: (e, s) {
          if (result.valueOrNull case final data?) {
            return _buildData(context, ref, data);
          }
          return RefreshableEmptyView(
            onTap: () => _onRefresh(ref),
            message: context.l10n.textDiscoveryError,
          );
        },
        loading: () => ListView.builder(
          itemCount: 20,
          itemBuilder: (_, _) => const TokenItemShimmer(),
        ),
      ),
    );
  }

  Widget _buildData(
    BuildContext context,
    WidgetRef ref,
    Paged<TokenObject> data,
  ) {
    final selectedCategory = ref.watch(tokenCategoryProvider);
    final selectedTimeFrame = ref.watch(tokenTimeFrameProvider);
    return ListView.builder(
      key: ValueKey(
        '${selectedCategory.value}:${selectedTimeFrame.value}',
      ),
      padding: const EdgeInsets.only(bottom: 24.0),
      itemCount: data.list.length,
      itemBuilder: (context, index) => ProviderScope(
        overrides: [
          tokenItemPreviewProvider.overrideWithValue(data.list[index]),
        ],
        child: const TokenItemPreview(hideOnEmpty: true),
      ),
    );
  }
}

// enum _EnumTraderCategory {
//   pl,
//   volume,
//   followers;
//
//   String displayName(BuildContext context) {
//     return switch (this) {
//       _EnumTraderCategory.pl => context.l10n.textPnL,
//       _EnumTraderCategory.volume => context.l10n.textVolume,
//       _EnumTraderCategory.followers => context.l10n.textFollowers,
//     };
//   }
// }

// final _categoryTraderProvider = StateProvider.autoDispose<_EnumTraderCategory>(
//   (_) => _EnumTraderCategory.pl,
// );
// final _traderListProvider = FutureProvider.autoDispose<List<TraderObject>>(
//   (ref) async {
//     await Future.delayed(2.seconds);
//     return List.generate(20, (_) => TraderObject.test);
//   },
// );
// final _traderItemProvider = Provider.autoDispose<TraderObject?>(
//   (ref) => throw UnimplementedError(),
// );
//
// class _Traders extends ConsumerWidget {
//   const _Traders();
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final selectedCategory = ref.watch(_categoryTraderProvider);
//     final result = ref.watch(_traderListProvider);
//     return Column(
//       children: [
//         SizedBox(
//           height: 24.0,
//           child: ListView.separated(
//             scrollDirection: Axis.horizontal,
//             padding: const EdgeInsets.symmetric(horizontal: 24.0),
//             itemCount: _EnumTraderCategory.values.length,
//             itemBuilder: (context, index) {
//               final category = _EnumTraderCategory.values[index];
//               final selected = selectedCategory == category;
//               return RippleTap(
//                 onTap: () =>
//                     ref.read(_categoryTraderProvider.notifier).state = category,
//                 borderRadius: RadiusConstants.max,
//                 color: selected ? context.theme.cardColor : Colors.transparent,
//                 alignment: Alignment.center,
//                 padding: const EdgeInsets.symmetric(
//                   horizontal: 12.0,
//                   vertical: 2.0,
//                 ),
//                 child: Text(
//                   category.displayName(context),
//                   style: context.textTheme.bodySmall?.copyWith(
//                     color: selected ? Colors.white : null,
//                     fontSize: 14.0,
//                     fontWeight: selected ? FontWeight.bold : null,
//                     height: 1.0,
//                   ),
//                 ),
//               );
//             },
//             separatorBuilder: (_, _) => const Gap.h(8.0),
//           ),
//         ),
//         const Gap.v(10.0),
//         Expanded(
//           child: result.when(
//             data: (data) => ListView.builder(
//               itemCount: data.length,
//               itemBuilder: (context, index) {
//                 final item = data[index];
//                 return ProviderScope(
//                   overrides: [_traderItemProvider.overrideWithValue(item)],
//                   child: const _TraderItem(),
//                 );
//               },
//             ),
//             loading: () => ListView.builder(
//               itemCount: 30,
//               itemBuilder: (_, _) => const _TraderItemShimmer(),
//             ),
//             error: (e, s) {
//               handleExceptions(error: e, stackTrace: s);
//               return ListView.builder(
//                 itemCount: 30,
//                 itemBuilder: (_, _) => const _TraderItemShimmer(),
//               );
//             },
//           ),
//         ),
//       ],
//     );
//   }
// }
//
// class _TraderItemShimmer extends StatelessWidget {
//   const _TraderItemShimmer();
//
//   @override
//   Widget build(BuildContext context) {
//     return Shimmer.fromColors(
//       baseColor: context.theme.cardColor.withValues(alpha: 0.5),
//       highlightColor: context.meTheme.shimmerHighlightColor,
//       child: Container(
//         height: 60.0,
//         padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 10.0),
//         child: Row(
//           children: [
//             AspectRatio(
//               aspectRatio: 1.0,
//               child: Container(
//                 decoration: BoxDecoration(
//                   color: context.theme.cardColor,
//                   shape: BoxShape.circle,
//                 ),
//               ),
//             ),
//             const Gap.h(10.0),
//             Expanded(
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                 children: [
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Gap.h(
//                         40.0,
//                         height: 16.0,
//                         color: context.theme.cardColor,
//                       ),
//                     ],
//                   ),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Gap.h(
//                         120.0,
//                         height: 14.0,
//                         color: context.theme.cardColor,
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//             Gap.h(
//               30.0,
//               height: 16.0,
//               color: context.theme.cardColor,
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
//
// class _TraderItem extends ConsumerWidget {
//   const _TraderItem();
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final trader = ref.watch(_traderItemProvider)!;
//     return RippleTap(
//       onTap: () {
//         context.navigator.pushNamed(
//           Routes.userDetail.name,
//           arguments: Routes.userDetail.d(user: trader),
//         );
//       },
//       height: 60.0,
//       padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 10.0),
//       child: Row(
//         children: [
//           AspectRatio(
//             aspectRatio: 1.0,
//             child: ClipOval(
//               child: Container(
//                 decoration: BoxDecoration(
//                   color: context.themeColor,
//                   shape: BoxShape.circle,
//                 ),
//               ),
//             ),
//           ),
//           const Gap.h(10.0),
//           Expanded(
//             child: Column(
//               mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//               children: [
//                 DefaultTextStyle.merge(
//                   style: const TextStyle(
//                     fontSize: 14.0,
//                     fontWeight: FontWeight.bold,
//                   ),
//                   child: Row(
//                     children: [
//                       Flexible(child: Text(trader.name)),
//                     ],
//                   ),
//                 ),
//                 DefaultTextStyle.merge(
//                   style: const TextStyle(fontSize: 10.0),
//                   child: Row(
//                     children: [
//                       Flexible(
//                         child: Container(
//                           padding: const EdgeInsets.symmetric(
//                             horizontal: 8.0,
//                             vertical: 2.0,
//                           ),
//                           decoration: BoxDecoration(
//                             border: Border.all(
//                               color: context.theme.dividerColor,
//                             ),
//                             borderRadius: RadiusConstants.max,
//                           ),
//                           child: Text.rich(
//                             TextSpan(
//                               children: [
//                                 TextSpan(
//                                   text: trader.followers.toNumerical(),
//                                   style: TextStyle(
//                                     color: context.textTheme.bodyMedium?.color,
//                                     fontWeight: FontWeight.bold,
//                                   ),
//                                 ),
//                                 const TextSpan(text: ' followers'),
//                               ],
//                               style: context.textTheme.bodySmall?.copyWith(
//                                 fontSize: 10.0,
//                               ),
//                             ),
//                           ),
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           const Gap.h(10.0),
//           Text(
//             '\$${trader.change.toDecimal().toNumerical()}',
//             style: TextStyle(
//               color: context.meTheme.successColor,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
