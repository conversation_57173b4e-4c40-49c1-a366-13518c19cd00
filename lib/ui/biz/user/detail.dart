import 'package:flutter/material.dart';
import 'package:scoop/exports.dart';

@FFAutoImport()
import 'package:scoop/models/user.dart';

import '/feat/share/builder.dart';
import '/internals/variables.dart';
import '/provider/api.dart' show apiScoopProviderProvider, apiServiceProvider;

const _avatarDimension = 80.0;
final _imageHeightProvider = Provider.autoDispose.family<double, BuildContext>(
  (_, context) => context.topPadding + kMinInteractiveDimension + 40.0,
);
final _userProvider = Provider.autoDispose<UserInfoBase>(
  (_) => throw UnimplementedError(),
);
final _userFetchProvider = FutureProvider.autoDispose<UserInfoBase>(
  (ref) async {
    final ct = ref.cancelToken();
    final u = ref.watch(_userProvider);
    final api = ref.read(apiServiceProvider);
    final UserInfoBase user = await switch (u) {
      PersistentUserInfo() => api.getSelfUserInfo(cancelToken: ct),
      _ => api.getUserInfo(userRawId: u.rawUserId, cancelToken: ct),
    };
    if (user is PersistentUserInfo) {
      ref.read(userRepoProvider.notifier).update(user);
    }
    return user;
  },
  dependencies: [_userProvider],
);
final _userFollowTogglingProvider = StateProvider.autoDispose<bool>(
  (ref) => false,
);

enum _EnumTab { invite, following, followers }

final _selectedTabProvider = StateProvider.autoDispose<_EnumTab>(
  (_) => throw UnimplementedError(),
);

@FFRoute(name: '/user/detail')
class UserDetailPage extends StatelessWidget {
  const UserDetailPage({super.key, required this.user});

  final UserInfoBase user;

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      overrides: [
        _userProvider.overrideWithValue(user),
        _selectedTabProvider.overrideWith(
          (ref) =>
              user is PersistentUserInfo ? _EnumTab.invite : _EnumTab.following,
        ),
      ],
      child: const _MainBody(),
    );
  }
}

class _MainBody extends ConsumerWidget {
  const _MainBody();

  Future<void> _toggleFollow(WidgetRef ref, UserInfo user) async {
    ref.read(_userFollowTogglingProvider.notifier).state = true;
    try {
      await ref
          .read(apiServiceProvider)
          .toggleUserFollow(userRawId: user.rawUserId, follow: !user.following);
      final _ = await ref.refresh(_userFetchProvider.future);
    } finally {
      ref.read(_userFollowTogglingProvider.notifier).state = false;
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userRaw = ref.watch(_userProvider);
    final userFetch = ref.watch(_userFetchProvider);
    final user = userFetch.valueOrNull ?? userRaw;
    final socialX = switch (user) {
      final UserInfoBaseWithSocials u => u.socialAccountBy(
        UserInfoBaseWithSocials.platformX,
      ),
      _ => null,
    };
    final loginWithGuest =
        ref.watch(userRepoProvider.notifier).getUserLoginMethod() ==
        UserLoginMethod.guest;
    final imageHeight = ref.watch(_imageHeightProvider(context));
    return Scaffold(
      body: Stack(
        children: [
          Column(
            spacing: 10.0,
            children: [
              Stack(
                children: [
                  Positioned.fill(
                    bottom: null,
                    child: RepaintBoundary(
                      child: Container(
                        height: imageHeight,
                        decoration: BoxDecoration(
                          border: Border.all(
                            width: 2.0,
                            color: context.theme.dividerColor,
                            strokeAlign: BorderSide.strokeAlignOutside,
                          ),
                        ),
                        child: Assets.icons.placeholderAnime.image(
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                  const _Header(),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: 10.0,
                children: [
                  if (socialX case final x?)
                    Flexible(
                      child: ScalableRippleTap(
                        onTap: () => launchUrlString(
                          'https://x.com/${x.username}',
                          mode: LaunchMode.externalApplication,
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16.0,
                          vertical: 12.0,
                        ),
                        borderRadius: RadiusConstants.max,
                        color: context.theme.cardColor,
                        child: Row(
                          spacing: 10.0,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Assets.icons.iconX.svg(height: 16.0),
                            Flexible(
                              child: Text.rich(
                                TextSpan(
                                  children: [
                                    TextSpan(
                                      text: '@',
                                      style: TextStyle(
                                        color: context.themeColor,
                                      ),
                                    ),
                                    TextSpan(text: x.username),
                                  ],
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  if (user is UserInfo)
                    Consumer(
                      builder: (context, ref, child) {
                        final toggling = ref.watch(_userFollowTogglingProvider);
                        return ScalableRippleTap(
                          onTap: () => _toggleFollow(ref, user),
                          height: 40.0,
                          alignment: Alignment.center,
                          borderRadius: RadiusConstants.max,
                          color: user.following
                              ? context.theme.cardColor
                              : context.themeColor,
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              Container(
                                height: double.infinity,
                                alignment: Alignment.center,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16.0,
                                ),
                                child: Text(
                                  user.following
                                      ? context.l10n.labelUserUnfollow
                                      : context.l10n.labelUserFollow,
                                  style: TextStyle(
                                    color: user.following ? null : Colors.black,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              if (toggling)
                                Positioned.fill(
                                  child: DecoratedBox(
                                    decoration: BoxDecoration(
                                      borderRadius: RadiusConstants.max,
                                      color: user.following
                                          ? context.theme.cardColor
                                          : context.themeColor,
                                    ),
                                    child: const AppLoading(
                                      size: 56.0,
                                      alignment: null,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        );
                      },
                    ),
                ],
              ),
              if (user case final UserInfoBaseWithSocials user) ...[
                const Gap.v(10.0),
                Row(
                  spacing: 30.0,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      spacing: 6.0,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          user.followingCount.toNumerical(fractionDigits: 1),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          context.l10n.labelUserFollowingCount,
                          style: context.textTheme.bodySmall?.copyWith(
                            fontSize: 14.0,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      spacing: 6.0,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          user.followersCount.toNumerical(fractionDigits: 1),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          context.l10n.labelUserFollowersCount,
                          style: context.textTheme.bodySmall?.copyWith(
                            fontSize: 14.0,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
              const Gap.v(30.0),
              Consumer(
                builder: (context, ref, _) => AppTabBar(
                  items: [
                    if (user is PersistentUserInfo && !loginWithGuest)
                      _EnumTab.invite,
                    _EnumTab.following,
                    _EnumTab.followers,
                  ],
                  selected: ref.watch(_selectedTabProvider),
                  onSelected: (item) =>
                      ref.read(_selectedTabProvider.notifier).state = item,
                  displayText: (context, item) => switch (item) {
                    _EnumTab.invite => context.l10n.labelUserInvite,
                    _EnumTab.following => switch (user) {
                      UserInfoBaseWithSocials() =>
                        '${context.l10n.labelUserFollowingCount} '
                            '**${user.followingCount}**',
                      _ => context.l10n.labelUserFollowingCount,
                    },
                    _EnumTab.followers => switch (user) {
                      UserInfoBaseWithSocials() =>
                        '${context.l10n.labelUserFollowersCount} '
                            '**${user.followersCount}**',
                      _ => context.l10n.labelUserFollowersCount,
                    },
                  },
                ),
              ),
              Expanded(
                child: Consumer(
                  builder: (context, ref, _) =>
                      switch (ref.watch(_selectedTabProvider)) {
                        _EnumTab.invite => const _Invites(),
                        _EnumTab.following => const _UserList(following: true),
                        _EnumTab.followers => const _UserList(following: false),
                      },
                ),
              ),
              Gap.v(context.bottomPadding.max(54.0)),
            ],
          ),
          const Positioned.fill(bottom: null, child: _AppBar()),
        ],
      ),
    );
  }
}

class _AppBar extends ConsumerWidget {
  const _AppBar();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userRaw = ref.watch(_userProvider);
    final userFetch = ref.watch(_userFetchProvider);
    final user = userFetch.valueOrNull ?? userRaw;
    return Material(
      type: MaterialType.transparency,
      child: Container(
        padding: MediaQuery.paddingOf(context).copyWith(bottom: 0.0),
        child: IconTheme(
          data: context.iconTheme.copyWith(color: Colors.white),
          child: Row(
            children: [
              const BackButton(),
              if (userFetch.isLoading)
                const Expanded(child: AppLoading(size: 48.0))
              else
                const Spacer(),
              if (user is PersistentUserInfo)
                IconButton(
                  onPressed: () =>
                      context.navigator.pushNamed(Routes.settings.name),
                  icon: const Icon(Icons.settings),
                )
              else
                const SizedBox.square(dimension: kMinInteractiveDimension),
            ],
          ),
        ),
      ),
    );
  }
}

class _Header extends ConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userRaw = ref.watch(_userProvider);
    final userFetch = ref.watch(_userFetchProvider);
    final user = userFetch.valueOrNull ?? userRaw;
    final imageHeight = ref.watch(_imageHeightProvider(context));
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        horizontal: 24.0,
      ).copyWith(top: imageHeight - _avatarDimension / 2),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          UserAvatar(
            dimension: 80.0,
            avatarUrl: user.avatar,
            backgroundColor: context.theme.dividerColor,
          ),
          const Gap.v(8.0),
          Text(
            user.name,
            style: context.textTheme.bodyLarge?.copyWith(
              fontSize: 24.0,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

class _Invites extends ConsumerWidget {
  const _Invites();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userRaw = ref.watch(_userProvider);
    final userFetch = ref.watch(_userFetchProvider);
    final user = (userFetch.valueOrNull ?? userRaw) as PersistentUserInfo;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0).copyWith(top: 24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            spacing: 6.0,
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                context.l10n.titleUserInvite,
                style: context.textTheme.headlineSmall,
              ),
              Text(
                context.l10n.textUserInvite,
                style: context.textTheme.bodySmall,
              ),
            ],
          ),
          Column(
            spacing: 6.0,
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                context.l10n.labelTotalReferrals,
                style: context.textTheme.bodySmall,
              ),
              Text(
                user.referCount.toString(),
                style: TextStyle(
                  color: context.themeColor,
                  fontFamily: FontFamily.mMMMono,
                  fontSize: 46.0,
                ),
              ),
            ],
          ),
          Column(
            spacing: 10.0,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: MEUIConfig.themeTextButtonConfig.height,
                padding: const EdgeInsets.symmetric(horizontal: 12.0),
                decoration: BoxDecoration(
                  borderRadius: MEUIConfig.themeTextButtonConfig.borderRadius,
                  color: context.theme.cardColor,
                ),
                child: DefaultTextStyle.merge(
                  style: const TextStyle(fontSize: 18.0),
                  child: Row(
                    spacing: 4.0,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Spacer(),
                      Text(
                        '${context.l10n.labelRefCode}:',
                        style: TextStyle(color: context.iconTheme.color),
                      ),
                      Text(
                        user.referCode,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Expanded(
                        child: Align(
                          alignment: AlignmentDirectional.centerEnd,
                          child: CopyButton(
                            onCopy: () => user.referCode,
                            color: context.themeColor,
                            dimension: 28.0,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              ThemeTextButton(
                onPressed: () async {
                  final user = ref.watch(userRepoProvider);
                  if (user == null) {
                    return;
                  }
                  final builder = ShareBuilder()
                      .addQueryParameter(
                        ShareBuilder.keySource,
                        ShareBuilder.sourceApp,
                      )
                      .addQueryParameter(
                        ShareBuilder.keyUserId,
                        user.rawUserId,
                      );
                  if (user.referCode.isNotEmpty) {
                    builder.addQueryParameter(
                      ShareBuilder.keyReferCode,
                      user.referCode,
                    );
                  }
                  final share = builder.build();
                  try {
                    final short = await AppLoading.run(
                      () => ref
                          .read(apiScoopProviderProvider)
                          .putShortUrl(share.toString()),
                    );
                    globalClipboardText = short;
                    ShareBuilder.shareUri(Uri.parse(short));
                  } catch (e, s) {
                    handleExceptions(error: e, stackTrace: s);
                    globalClipboardText = share.toString();
                    ShareBuilder.shareUri(share);
                  }
                },
                child: Row(
                  spacing: 16.0,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Assets.icons.buttonShare.svg(
                      colorFilter: Colors.black.filter,
                    ),
                    Text(context.l10n.labelInviteAFriend),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

final _userListProvider = FutureProvider.family
    .autoDispose<
      Paged<UserInfo>,
      (String id, bool following, int page, int size)
    >(
      (ref, args) {
        final (id, following, page, size) = args;
        return ref
            .read(apiServiceProvider)
            .getUserFollowingList(
              userRawId: id,
              page: page,
              size: size,
              following: following,
            );
      },
    );

final _userItemProvider = Provider.autoDispose<UserInfo>(
  (_) => throw UnimplementedError(),
);

class _UserList extends ConsumerWidget {
  const _UserList({required this.following});

  final bool following;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(_userProvider);
    final userRawId = user.rawUserId;
    const size = 20;
    final totalResult = ref.watch(
      _userListProvider((userRawId, following, 1, size)),
    );
    if (totalResult.valueOrNull?.total == 0) {
      return EmptyView(
        message: following
            ? context.l10n.textUserNoFollowingYet
            : context.l10n.textUserNoFollowersYet,
      );
    }
    return ListView.separated(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      separatorBuilder: (context, _) => const Gap.v(24.0),
      itemCount: totalResult.valueOrNull?.total ?? size,
      itemBuilder: (context, index) {
        final page = index ~/ size + 1;
        final indexInPage = index % size;
        final result = ref.watch(
          _userListProvider((userRawId, following, page, size)),
        );
        return result.maybeWhen(
          data: (data) {
            if (indexInPage >= data.list.length) {
              return null;
            }
            final user = data.list[indexInPage];
            return ProviderScope(
              overrides: [
                _userItemProvider.overrideWithValue(user),
              ],
              child: const _UserItem(),
            );
          },
          orElse: () => const _UserItemShimmer(),
        );
      },
    );
  }
}

class _UserItem extends ConsumerWidget {
  const _UserItem();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(_userItemProvider);
    return RippleTap(
      onTap: () => context.navigator.pushNamed(
        Routes.userDetail.name,
        arguments: Routes.userDetail.d(user: user),
      ),
      child: Container(
        height: 40.0,
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Row(
          spacing: 10.0,
          children: [
            UserAvatar(
              user: user,
              dimension: 40.0,
              backgroundColor: context.theme.canvasColor,
            ),
            Expanded(
              child: Text(
                user.name,
                style: context.textTheme.headlineSmall?.copyWith(
                  fontSize: 16.0,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _UserItemShimmer extends StatelessWidget {
  const _UserItemShimmer();

  @override
  Widget build(BuildContext context) {
    return MEShimmer(
      child: Container(
        height: 40.0,
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: Row(
          spacing: 10.0,
          children: [
            Container(
              width: 40.0,
              height: 40.0,
              decoration: BoxDecoration(
                color: context.theme.canvasColor,
                shape: BoxShape.circle,
              ),
            ),
            Expanded(
              child: Container(height: 18.0, color: context.theme.canvasColor),
            ),
          ],
        ),
      ),
    );
  }
}
