import 'package:flutter/material.dart';
import 'package:scoop/exports.dart';

@FFAutoImport()
import 'package:scoop/models/tokens.dart' show TokenObject;

import 'package:sliver_tools/sliver_tools.dart';

import '/api/bird_eye.dart';
import '/models/tokens.dart' hide TokenObject;
import '/provider/api.dart';
import '/provider/token.dart';
import '/ui/widgets/progressing_bar.dart';
import '/ui/widgets/sub_token_view.dart';
import 'trading_view.dart';

final _tokenProvider = Provider.autoDispose<TokenObject>(
  (_) => throw UnimplementedError(),
);
final _tokenAuthorityProvider = FutureProvider.autoDispose<TokenAuthority>(
  (ref) => ref
      .read(apiGoPlusProvider)
      .tokenAuthoritySolana(ref.read(_tokenProvider).address),
  dependencies: [_tokenProvider],
);
final _tokenTradeDataProvider =
    FutureProvider.autoDispose<ApiBirdEyeTokenTradeData>(
      (ref) {
        final token = ref.watch(_tokenProvider);
        final chain = ref.watch(chainByNameProvider(token.chainId));
        return ref.read(
          fetchTokenTradeDataProvider(
            chain: chain,
            address: token.address,
          ).future,
        );
      },
      dependencies: [_tokenProvider],
    );
final _tokenSubProvider = Provider.autoDispose<SubDexScreenerToken?>(
  (_) => throw UnimplementedError(),
);

enum _EnumTab {
  details
  // myOrder,
  ;

  String displayName(BuildContext context) {
    return switch (this) {
      _EnumTab.details => context.l10n.labelDetails,
      // _EnumTab.myOrder => context.l10n.labelMyOrders,
    };
  }
}

final _selectedTabProvider = StateProvider.autoDispose<_EnumTab>(
  (ref) => _EnumTab.values.first,
);

@FFRoute(name: '/token/detail')
class TokenDetailPage extends StatelessWidget {
  const TokenDetailPage({
    super.key,
    required this.token,
    required this.address,
    required this.chainId,
  });

  final TokenObject? token;
  final String address;
  final String chainId;

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      body: Consumer(
        builder: (context, ref, _) {
          if (token case final token?) {
            return SubTokenView(
              address: token.address,
              chainId: token.chainId,
              builder: (key, context, rt, child) => ProviderScope(
                overrides: [
                  _tokenProvider.overrideWithValue(token),
                  _tokenSubProvider.overrideWithValue(rt),
                ],
                child: child!,
              ),
              child: const _MainBody(),
            );
          }
          final result = ref.watch(
            fetchTokenObjectProvider(
              address: address,
              chainId: chainId,
            ),
          );
          return result.maybeWhen(
            skipLoadingOnRefresh: false,
            data: (data) {
              if (data == null) {
                return EmptyView(
                  onTap: () => ref.invalidate(
                    fetchTokenObjectProvider(
                      address: address,
                      chainId: chainId,
                    ),
                  ),
                  message: context.l10n.textNothingFound,
                );
              }
              return SubTokenView(
                address: data.address,
                chainId: data.chainId,
                builder: (key, context, rt, child) => ProviderScope(
                  overrides: [
                    _tokenProvider.overrideWithValue(data),
                    _tokenSubProvider.overrideWithValue(rt),
                  ],
                  child: child!,
                ),
                child: const _MainBody(),
              );
            },
            orElse: () => const AppLoading(),
          );
        },
      ),
    );
  }
}

class _MainBody extends ConsumerWidget {
  const _MainBody();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final token = ref.watch(_tokenProvider);
    final chain = ref.watch(chainByNameOrNullProvider(token.chainId));
    final tokenAddress = ref.watch(_tokenProvider).address;
    final tokenBalance = ref.watch(
      tokenBalanceStreamProvider(chain: chain, address: tokenAddress),
    );
    return Stack(
      fit: StackFit.expand,
      children: [
        Positioned.fill(
          child: CustomScrollView(
            slivers: [
              const SliverGap.v(10.0),
              const SliverToBoxAdapter(child: _Header()),
              const SliverGap.v(20.0),
              SliverToBoxAdapter(
                child: TokenTradingView(
                  address: tokenAddress,
                  chain: token.chainId,
                ),
              ),
              const SliverGap.v(20.0),
              const SliverToBoxAdapter(child: _SectionHeader()),
              Consumer(
                builder: (context, ref, _) =>
                    switch (ref.watch(_selectedTabProvider)) {
                      _EnumTab.details => const _SectionDetails(),
                      // _EnumTab.myOrder => const SliverToBoxAdapter(),
                    },
              ),
              SliverGap.v(96.0 + context.bottomPadding),
            ],
          ),
        ),
        if (ref.watch(userRepoProvider)?.walletBlocked == false &&
            chain?.tradable == true)
          Positioned(
            left: 0.0,
            right: 0.0,
            bottom: 0.0,
            child: _TradeButtons(tokenBalance.valueOrNull),
          ),
      ],
    );
  }
}

final _copyButtonKey = Provider.autoDispose<GlobalKey<CopyButtonState>>(
  (_) => GlobalKey<CopyButtonState>(),
);

class _Header extends ConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final buttonKey = ref.watch(_copyButtonKey);
    final frame = ref.watch(tokenTimeFrameProvider);
    final token = ref.watch(_tokenProvider);
    final sub = ref.watch(_tokenSubProvider);
    final marketCap = sub?.marketCap ?? token.marketCap;
    final price = sub?.priceUsd ?? token.priceUsd;
    Decimal? change =
        sub?.priceChanges[frame] ?? token.priceChangeByFrame(frame);
    if (change == Decimal.zero) {
      change = null;
    }
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Row(
            spacing: 10.0,
            children: [
              TokenIcon(
                dimension: 46.0,
                token: token,
                chainId: token.chainId,
              ),
              Expanded(
                child: DefaultTextStyle.merge(
                  style: const TextStyle(height: 1.0),
                  child: Tapper(
                    onTap: () {
                      buttonKey.currentState?.toggle();
                    },
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Gap.v(4.0),
                        Text(
                          token.symbol ?? '-',
                          style: context.textTheme.headlineMedium?.copyWith(
                            fontSize: 16.0,
                          ),
                        ),
                        Row(
                          children: [
                            Flexible(
                              child: Text(
                                token.address.asIdentifier,
                                style: context.textTheme.bodySmall,
                              ),
                            ),
                            CopyButton(
                              key: buttonKey,
                              onCopy: () => token.address,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.only(
            left: 24.0,
            right: 24.0,
            top: 16.0,
            bottom: 4.0,
          ),
          alignment: AlignmentDirectional.centerStart,
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: AnimatedText(
              '\$${marketCap?.toNumerical() ?? '-'}',
              style: const TextStyle(
                fontSize: 30.0,
                height: 36.0 / 30.0,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Row(
            children: [
              AnimatedText(
                '\$${price is Decimal ? Formatter.toUsd(price.toString()) : '-'}',
                style: context.textTheme.bodySmall?.copyWith(fontSize: 16.0),
              ),
              const Gap.h(8.0),
              ChangeText(
                change?.toDouble(),
                style: const TextStyle(fontSize: 16.0),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _SectionHeader extends ConsumerWidget {
  const _SectionHeader();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppTabBar(
      items: _EnumTab.values,
      selected: ref.watch(_selectedTabProvider),
      onSelected: (item) =>
          ref.read(_selectedTabProvider.notifier).state = item,
      displayText: (context, item) => item.displayName(context),
    );
  }
}

class _SectionDetails extends ConsumerWidget {
  const _SectionDetails();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final token = ref.watch(_tokenProvider);
    final frame = ref.watch(tokenTimeFrameProvider);
    final sub = ref.watch(_tokenSubProvider);
    final trade = ref.watch(_tokenTradeDataProvider).valueOrNull;
    final liquidity = sub?.liquidity ?? token.liquidity;
    final volume = sub?.volumeByFrame(frame) ?? token.volumeByFrame(frame);
    final buySell = token.buySellByFrame(EnumTokenTimeFrame.hour24);
    final websites = token.websites?.entries.toList();
    final authority = ref.watch(_tokenAuthorityProvider).valueOrNull;
    final authorityTop10Holders = authority?.holders.fold(
      Decimal.zero,
      (p, v) => Decimal.parse(v.percent) + p,
    );
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      sliver: MultiSliver(
        children: [
          _buildTitle(context, context.l10n.titleStats),
          SliverGrid(
            delegate: SliverChildListDelegate(
              [
                _buildGridPair(
                  context,
                  context.l10n.labelHolders,
                  trade?.holders.toString(),
                ),
                _buildGridPair(
                  context,
                  '${frame.fullName(context)} ${context.l10n.textVolume}',
                  volume?.toNumerical(),
                ),
                _buildGridPair(
                  context,
                  context.l10n.labelSupply,
                  authority?.totalSupply?.toNumerical(),
                ),
                _buildGridPair(
                  context,
                  context.l10n.labelLiquidity,
                  liquidity?.toNumerical(),
                ),
              ],
            ),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 3.5,
            ),
          ),
          SliverToBoxAdapter(
            child: _buildProgress(
              context,
              (
                (context.l10n.labelBuys, buySell?.$1),
                (context.l10n.labelSells, buySell?.$2),
              ),
            ),
          ),
          const SliverGap.v(20.0),
          _buildTitle(context, context.l10n.labelAbout),
          SliverList(
            delegate: SliverChildListDelegate(
              [
                _buildListPair(
                  context,
                  token.symbol ?? '-',
                  token.address.asIdentifier,
                  null,
                ),
                if (token.pairCreatedAt case final createdAt?)
                  _buildListPair(
                    context,
                    context.l10n.labelCreatedAt,
                    createdAt.format(format: 'yMMMd'),
                    null,
                  ),
              ],
            ),
          ),
          if (websites case final websites? when websites.isNotEmpty)
            SliverToBoxAdapter(
              child: Container(
                height: 40.0,
                margin: const EdgeInsets.only(top: 16.0),
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: websites.length,
                  itemBuilder: (context, index) {
                    final link = websites[index];
                    return ScalableRippleTap(
                      onTap: () {
                        launchUrlString(
                          link.value,
                          mode: LaunchMode.externalApplication,
                        );
                      },
                      width: 50.0,
                      height: 40.0,
                      alignment: Alignment.center,
                      borderRadius: BorderRadius.circular(16.0),
                      color: context.theme.cardColor,
                      child: tryGetLinkIcon(
                        link.key,
                      ).svg(width: 22.0, height: 22.0),
                    );
                  },
                  separatorBuilder: (_, _) => const Gap.h(10.0),
                ),
              ),
            ),
          if (authority?.metadata?.description case final description?
              when description.isNotEmpty)
            SliverPadding(
              padding: const EdgeInsets.only(top: 16.0),
              sliver: SliverToBoxAdapter(
                child: Text(description, style: context.textTheme.bodySmall),
              ),
            ),
          const SliverGap.v(20.0),
          _buildTitle(context, context.l10n.labelAudit),
          SliverList(
            delegate: SliverChildListDelegate(
              [
                _buildListPair(
                  context,
                  context.l10n.labelTopHolders,
                  authorityTop10Holders
                      ?.shift(2)
                      .min(Decimal.fromInt(100))
                      .roundAsFixed(2)
                      .append('%'),
                  switch (authorityTop10Holders
                      ?.shift(2)
                      .min(
                        Decimal.fromInt(100),
                      )) {
                    final v? => v < Decimal.fromInt(30),
                    _ => false,
                  },
                ),
                _buildListPairWithBoolean(
                  context,
                  context.l10n.labelMintable,
                  authority?.mintable == false,
                ),
                _buildListPairWithBoolean(
                  context,
                  context.l10n.labelFreezable,
                  authority?.freezable == false,
                ),
                _buildListPairWithBoolean(
                  context,
                  context.l10n.labelBalanceMutable,
                  authority?.balanceMutable == false,
                ),
                _buildListPairWithBoolean(
                  context,
                  context.l10n.labelMetadataMutable,
                  authority?.metadataMutable == false,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitle(BuildContext context, String title) {
    return SliverPadding(
      padding: const EdgeInsets.only(top: 20.0),
      sliver: SliverToBoxAdapter(
        child: Text(
          title,
          style: context.textTheme.headlineMedium,
        ),
      ),
    );
  }

  Widget _buildListPair(
    BuildContext context,
    String title,
    String? value,
    bool? verified, {
    TextStyle? valueStyle,
  }) {
    return Padding(
      padding: const EdgeInsets.only(top: 12.0, bottom: 4.0),
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(color: context.textTheme.bodySmall?.color),
          ),
          const Spacer(),
          if (verified != null)
            Icon(
              verified ? Icons.check : Icons.clear,
              color: verified
                  ? context.meTheme.successColor
                  : context.meTheme.failingColor,
              size: 18.0,
            ),
          const Gap.h(8.0),
          switch ((value, verified)) {
            (final value?, final verified) => Text(
              value,
              style: TextStyle(
                color: verified == null
                    ? null
                    : verified
                    ? context.meTheme.successColor
                    : context.meTheme.failingColor,
              ).merge(valueStyle),
            ),
            _ => const SizedBox.square(
              dimension: 14.0,
              child: CircularProgressIndicator(strokeAlign: 2.0),
            ),
          },
        ],
      ),
    );
  }

  Widget _buildListPairWithBoolean(
    BuildContext context,
    String title,
    bool? value, {
    TextStyle? valueStyle,
  }) {
    return _buildListPair(
      context,
      title,
      value == null
          ? null
          : value
          ? context.l10n.textPassed
          : context.l10n.textFailed,
      value,
      valueStyle: valueStyle,
    );
  }

  Widget _buildGridPair(BuildContext context, String title, String? value) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(color: context.textTheme.bodySmall?.color),
        ),
        Text(value ?? '-'),
      ],
    );
  }

  Widget _buildProgress(
    BuildContext context,
    ((String, int?), (String, int?)) values,
  ) {
    return Column(
      children: [
        SizedBox(
          height: 56.0,
          child: Row(
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      values.$1.$1,
                      style: TextStyle(
                        color: context.textTheme.bodySmall?.color,
                      ),
                    ),
                    Text(values.$1.$2.toDecimal()?.toNumerical() ?? '-'),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      values.$2.$1,
                      style: TextStyle(
                        color: context.textTheme.bodySmall?.color,
                      ),
                    ),
                    Text(values.$2.$2.toDecimal()?.toNumerical() ?? '-'),
                  ],
                ),
              ),
            ],
          ),
        ),
        ClipRRect(
          borderRadius: RadiusConstants.max,
          child: SizedBox(
            height: 4.0,
            child: Row(
              spacing: 1.0,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (values.$1.$2 case final v? when v > 0)
                  Expanded(
                    flex: v,
                    child: ColoredBox(color: context.meTheme.successColor),
                  ),
                if (values.$2.$2 case final v? when v > 0)
                  Expanded(
                    flex: v,
                    child: ColoredBox(color: context.meTheme.failingColor),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _TradeButtons extends ConsumerWidget {
  const _TradeButtons(this.amount);

  final TokenAmount? amount;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final token = ref.watch(_tokenProvider);
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.meTheme.backgroundColor.withValues(alpha: 0.0),
            context.meTheme.backgroundColor,
          ],
          stops: [0.0, 0.27],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 24.0).copyWith(
        bottom: context.bottomPadding.max(24.0),
        top: 16.0,
      ),
      child: Row(
        spacing: 10.0,
        children: [
          Expanded(
            child: ThemeTextButton(
              onPressed: () {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: false,
                  backgroundColor: context.theme.canvasColor,
                  builder: (context) => _TradeDialog(
                    buying: token,
                    selling: null,
                    amount: amount,
                  ),
                );
              },
              text: context.l10nME.buyButton,
            ),
          ),
          if (amount?.hasValue == true)
            Expanded(
              child: ThemeTextButton(
                onPressed: () {
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: false,
                    backgroundColor: context.theme.canvasColor,
                    builder: (context) => _TradeDialog(
                      buying: null,
                      selling: token,
                      amount: amount,
                    ),
                  );
                },
                color: context.meTheme.failingColor,
                text: context.l10nME.sellButton,
              ),
            ),
        ],
      ),
    );
  }
}

class _TradeDialog extends ConsumerStatefulWidget {
  const _TradeDialog({
    required this.amount,
    required this.buying,
    required this.selling,
  }) : assert(
         buying == null && selling != null || buying != null && selling == null,
       );

  final TokenAmount? amount;
  final TokenObject? buying;
  final TokenObject? selling;

  @override
  ConsumerState<_TradeDialog> createState() => _TradeDialogState();
}

class _TradeDialogState extends ConsumerState<_TradeDialog> {
  TokenObject get _token => (widget.buying ?? widget.selling)!;

  String get _chainId => _token.chainId;

  Future<void> _makeBuyingOrder(Decimal amount) async {
    final verified = await SecureVerify.verify(
      context,
      subtitle: '${context.l10nME.buyButton} ${widget.buying?.symbol}',
      closeable: true,
    );
    if (!verified) {
      return;
    }
    return ProgressingBar.run(
      () async {
        final (txId, data, res) = await ref
            .read(apiServiceProvider)
            .createTradeOrder(
              buying: widget.buying,
              selling: widget.selling,
              chain: _chainId,
              amount: amount,
              decimals: null,
            );
        if (txId == null) {
          throw StateError(
            [
              'Transaction failed',
              ...data.entries.map(
                (entry) => '—— D[${entry.key}]: ${entry.value}',
              ),
              ...res.entries.map(
                (entry) => '—— R[${entry.key}]: ${entry.value}',
              ),
            ].join('\n'),
          );
        }
      },
      ongoingText: context.l10n.textBuyingInProgress,
      succeedText: context.l10n.textTransactionProcessing,
      failedText: context.l10n.textTransactionFailed,
      onSucceed: () {
        ref.invalidate(fetchRawWalletPortfolioProvider);
        ref.invalidate(fetchWalletPortfolioProvider);
        meNavigator.pop();
      },
    );
  }

  Future<void> _makeSellingOrder(Decimal balance, int percent) async {
    final verified = await SecureVerify.verify(
      context,
      subtitle: '${context.l10nME.sellButton} ${widget.selling?.symbol}',
      closeable: true,
    );
    if (!verified) {
      return;
    }
    return ProgressingBar.run(
      () async {
        final amount = balance * Decimal.fromInt(percent).shift(-2);
        final (txId, data, res) = await ref
            .read(apiServiceProvider)
            .createTradeOrder(
              buying: widget.buying,
              selling: widget.selling,
              chain: _chainId,
              amount: amount,
              decimals: widget.amount?.decimals,
            );
        if (txId == null) {
          throw StateError(
            [
              'Transaction failed',
              ...data.entries.map(
                (entry) => '—— D[${entry.key}]: ${entry.value}',
              ),
              ...res.entries.map(
                (entry) => '—— R[${entry.key}]: ${entry.value}',
              ),
            ].join('\n'),
          );
        }
      },
      ongoingText: context.l10n.textSellingInProgress,
      succeedText: context.l10n.textTransactionProcessing,
      failedText: context.l10n.textTransactionFailed,
      onSucceed: () {
        ref.invalidate(fetchRawWalletPortfolioProvider);
        ref.invalidate(fetchWalletPortfolioProvider);
        meNavigator.pop();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final entry = ref
        .watch(fetchWalletPortfolioProvider)
        .valueOrNull
        ?.tokenByAddress(_token.address);
    final chain = ref.watch(chainByNameOrNullProvider(_chainId));
    final tokenBalance = widget.amount?.realBalance ?? entry?.key.realBalance;
    final solBalance = ref.watch(fetchSOLBalanceFromPortfolioProvider);
    final settings = ref.watch(tradeSettingsRepoProvider);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0).copyWith(top: 20.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            spacing: 4.0,
            children: [
              const Spacer(),
              if (settings.priorityLevel.svg case final svg?)
                svg.svg(width: 12.0),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 5.0,
                  vertical: 1.0,
                ),
                decoration: BoxDecoration(
                  borderRadius: RadiusConstants.max,
                  color: context.meTheme.listColor,
                ),
                child: Text(
                  switch (settings.slippage) {
                    0 => context.l10n.textAuto,
                    final s => s.toString().append('%'),
                  },
                  style: const TextStyle(
                    fontSize: 12.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              RippleTap(
                onTap: () {
                  showModalBottomSheet(
                    context: context,
                    scrollControlDisabledMaxHeightRatio: 0.9,
                    backgroundColor: context.theme.canvasColor,
                    builder: (context) => GestureDetector(
                      onTap: () {
                        hideKeyboard();
                        FocusManager.instance.primaryFocus?.unfocus();
                      },
                      child: const _TradeSettingsDialog(),
                    ),
                  );
                },
                shape: const CircleBorder(),
                child: Assets.icons.buttonSettings.svg(),
              ),
            ],
          ),
          const Gap.v(20.0),
          SizedBox(
            height: kMinInteractiveDimension,
            child: Row(
              children: [
                Expanded(
                  child: _TradeTokenIcon(widget.selling, _chainId),
                ),
                Container(
                  width: 48.0,
                  height: 48.0,
                  padding: const EdgeInsets.all(6.0),
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: context.theme.dividerColor),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(Icons.arrow_forward, color: context.themeColor),
                  ),
                ),
                Expanded(
                  child: _TradeTokenIcon(widget.buying, _chainId),
                ),
              ],
            ),
          ),
          const Gap.v(20.0),
          AnimatedText.rich(
            TextSpan(
              children: [
                TextSpan(
                  text:
                      (widget.buying != null ? solBalance : tokenBalance)
                          ?.toString() ??
                      '---',
                ),
                const TextSpan(text: ' '),
                if (widget.buying != null) TextSpan(text: chain?.symbol),
                if (widget.buying != null) const TextSpan(text: ' '),
                TextSpan(
                  text: context.l10n.textAvailable,
                  style: TextStyle(
                    color: context.textTheme.bodySmall?.color,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ],
            ),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const Gap.v(30.0),
          Row(
            spacing: 6.0,
            children: widget.buying != null
                ? _buildBuyButtons(context, solBalance)
                : _buildSellButtons(context, tokenBalance),
          ),
          Gap.v(MediaQuery.paddingOf(context).bottom.max(24.0)),
        ],
      ),
    );
  }

  List<Widget> _buildBuyButtons(BuildContext context, Decimal? solBalance) {
    final chain = ref.watch(chainByNameOrNullProvider(_chainId));
    return <Decimal>[
      Decimal.parse('0.01'),
      Decimal.parse('0.1'),
      Decimal.parse('0.2'),
    ].map(
      (e) {
        final canTrade = (solBalance ?? Decimal.zero) >= e;
        return Expanded(
          child: ThemeTextButton(
            onPressed: canTrade ? () => _makeBuyingOrder(e) : null,
            color: canTrade ? null : context.theme.dividerColor,
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: '$e',
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const TextSpan(text: ' '),
                  TextSpan(text: chain?.symbol),
                ],
              ),
              style: context.textTheme.bodySmall?.copyWith(
                fontSize: 18.0,
              ),
            ),
          ),
        );
      },
    ).toList();
  }

  List<Widget> _buildSellButtons(BuildContext context, Decimal? balance) {
    return <int>[20, 50, 100]
        .map(
          (e) => Expanded(
            child: ThemeTextButton(
              onPressed:
                  balance == null ||
                      balance < Decimal.fromInt(e).shift(-2) * balance
                  ? null
                  : () {
                      _makeSellingOrder(balance, e);
                    },
              child: Text.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: '$e%',
                      style: const TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                style: context.textTheme.bodySmall?.copyWith(
                  fontSize: 18.0,
                ),
              ),
            ),
          ),
        )
        .toList();
  }
}

class _TradeTokenIcon extends ConsumerWidget {
  const _TradeTokenIcon(this.token, this.chainId);

  final TokenObject? token;
  final String chainId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final chain = ref.watch(chainByNameProvider(chainId));
    return Container(
      padding: const EdgeInsets.all(6.0),
      decoration: BoxDecoration(
        borderRadius: RadiusConstants.max,
        color: context.theme.cardColor,
      ),
      child: Row(
        children: [
          TokenIcon(
            dimension: 36.0,
            token: token,
            chainId: token?.chainId,
            logo: token?.logo,
            backgroundColor: context.theme.scaffoldBackgroundColor,
            showProtocol: token != null,
            showBorder: token != null,
            fallbackBuilder: (context, name) {
              Widget? child;
              if (token == null) {
                child = ClipOval(
                  child: Padding(
                    padding: EdgeInsets.all(chain.hasExtraPadding ? 5.0 : 1.0),
                    child: MEImage(
                      chain.logo,
                      clipOval: false,
                      fit: BoxFit.scaleDown,
                      alternativeSVG: true,
                    ),
                  ),
                );
              }
              child ??= TokenIcon.defaultFallbackBuilder(context, name);
              return child;
            },
          ),
          const Gap.h(10.0),
          Expanded(
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                switch (token) {
                  final token? => token.symbol ?? '-',
                  _ => chain.symbol,
                },
                style: const TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _TradeSettingsDialog extends ConsumerStatefulWidget {
  const _TradeSettingsDialog();

  @override
  ConsumerState<_TradeSettingsDialog> createState() =>
      _TradeSettingsDialogState();
}

class _TradeSettingsDialogState extends ConsumerState<_TradeSettingsDialog> {
  late TradeSettings _settings = ref.read(tradeSettingsRepoProvider);
  late final _tecCustomSlippage = TextEditingController(
    text: _settings.slippage.toString(),
  );
  late final _tecMaxSlippage = TextEditingController(
    text: _settings.maxSlippage?.toString(),
  );
  late final _tecPrioritySpeedFee = TextEditingController(
    text: _settings.prioritySpeedFee.toString(),
  );
  final _keyCustomSlippage = GlobalKey();
  final _keyMaxSlippage = GlobalKey();
  final _keyPrioritySpeedFee = GlobalKey();
  final _focusCustomSlippage = FocusNode();
  final _focusMaxSlippage = FocusNode();
  final _focusPrioritySpeedFee = FocusNode();
  bool _editingCustomSlippage = false;

  @override
  void initState() {
    super.initState();
    _focusCustomSlippage.addListener(() {
      if (!_focusCustomSlippage.hasFocus) {
        safeSetState(() {
          _editingCustomSlippage = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _tecCustomSlippage.dispose();
    _tecMaxSlippage.dispose();
    _tecPrioritySpeedFee.dispose();
    _focusCustomSlippage.dispose();
    _focusMaxSlippage.dispose();
    super.dispose();
  }

  Future<void> _ensureVisible(GlobalKey key) async {
    await 500.milliseconds.delay;
    if (!mounted) {
      return;
    }
    if (key.currentContext case final context?) {
      Scrollable.ensureVisible(
        context,
        duration: 100.milliseconds,
        curve: Curves.decelerate,
      );
    }
  }

  void _saveSettings() {
    ref.read(tradeSettingsRepoProvider.notifier).update(_settings);
  }

  void _updateSettings(TradeSettings value) {
    _settings = value;
    setState(() {});
  }

  void _updateSlippage(int value) {
    final newSettings = _settings.copyWith(slippage: value);
    _updateSettings(newSettings);
  }

  void _tryParseSlippage(String value) {
    if (int.tryParse(value) case final v? when v > 0 && v <= 100) {
      _updateSlippage(v);
    }
  }

  void _updateMaxSlippage(int value) {
    final newSettings = _settings.copyWith(maxSlippage: value);
    _updateSettings(newSettings);
  }

  void _tryParseMaxSlippage(String value) {
    if (int.tryParse(value) case final v? when v > 0 && v <= 100) {
      _updateMaxSlippage(v);
    }
  }

  void _updatePriorityLevel(EnumTradePriorityLevel value) {
    final newSettings = _settings.copyWith(priorityLevel: value);
    _updateSettings(newSettings);
  }

  void _tryParsePrioritySpeedFee(String value) {
    if (double.tryParse(value) case final v? when v > 0.0) {
      _updatePrioritySpeedFee(v);
    }
  }

  void _updatePrioritySpeedFee(double value) {
    final newSettings = _settings.copyWith(prioritySpeedFee: value.toDouble());
    _updateSettings(newSettings);
  }

  @override
  Widget build(BuildContext context) {
    final slippage = _settings.slippage;
    final isSlippageAuto = slippage == 0;
    final isSlippageCustom = !TradeSettings.defaultSlippages.contains(slippage);
    final priorityLevel = _settings.priorityLevel;
    final isPriorityCustom = priorityLevel == EnumTradePriorityLevel.custom;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0).copyWith(top: 20.0),
      child: ListView(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        children: [
          Text(
            context.l10n.textSlippage,
            style: context.textTheme.headlineSmall,
          ),
          Text(
            context.l10n.textSlippageDescription,
            style: context.textTheme.bodySmall?.copyWith(fontSize: 14.0),
          ),
          const Gap.v(20.0),
          Row(
            spacing: 6.0,
            children: [
              ...TradeSettings.defaultSlippages.map(
                (e) {
                  final selected = slippage == e && !_editingCustomSlippage;
                  return Expanded(
                    child: ThemeTextButton(
                      onPressed: () {
                        _updateSlippage(e);
                        _focusCustomSlippage.unfocus();
                      },
                      height: 40.0,
                      color: selected
                          ? context.themeColor
                          : context.meTheme.listColor,
                      child: AutoSizeText(
                        e == 0
                            ? context.l10n.textAuto
                            : e.toString().append('%'),
                        style: TextStyle(
                          color: !selected
                              ? context.textTheme.bodySmall?.color
                              : null,
                          fontSize: 16.0,
                          fontWeight: !selected ? FontWeight.normal : null,
                        ),
                      ),
                    ),
                  );
                },
              ),
              Expanded(
                flex: 2,
                child: ThemeTextButton(
                  onPressed: _editingCustomSlippage
                      ? null
                      : () {
                          setState(() {
                            _editingCustomSlippage = true;
                          });
                          _tryParseSlippage(_tecCustomSlippage.text);
                          postRun(() => _focusCustomSlippage.requestFocus());
                        },
                  height: 40.0,
                  color: isSlippageCustom
                      ? context.themeColor
                      : context.meTheme.listColor,
                  child: switch (_editingCustomSlippage) {
                    true => TextField(
                      key: _keyCustomSlippage,
                      controller: _tecCustomSlippage,
                      focusNode: _focusCustomSlippage,
                      decoration: const InputDecoration(
                        contentPadding: EdgeInsets.symmetric(horizontal: 16.0),
                        suffix: Text('%'),
                      ),
                      keyboardType: TextInputType.number,
                      maxLength: 3,
                      buildCounter: emptyCounterBuilder,
                      onChanged: _tryParseSlippage,
                      onTap: () {
                        _ensureVisible(_keyCustomSlippage);
                        _tryParseSlippage(_tecCustomSlippage.text);
                      },
                    ),
                    false => AutoSizeText(
                      isSlippageCustom ? '$slippage%' : context.l10n.textCustom,
                      style: TextStyle(
                        color: !isSlippageCustom
                            ? context.textTheme.bodySmall?.color
                            : null,
                        fontSize: 16.0,
                        fontWeight: !isSlippageCustom
                            ? FontWeight.normal
                            : null,
                      ),
                    ),
                  },
                ),
              ),
            ],
          ),
          if (isSlippageAuto) ...[
            const Gap.v(20.0),
            Text(
              context.l10n.textMaxSlippage,
              style: context.textTheme.bodyLarge,
            ),
            Text(
              context.l10n.textMaxSlippageDescription,
              style: context.textTheme.bodySmall?.copyWith(fontSize: 14.0),
            ),
            const Gap.v(10.0),
            Builder(
              builder: (context) {
                final border = UnderlineInputBorder(
                  borderSide: BorderSide(color: context.theme.dividerColor),
                );
                return TextField(
                  key: _keyMaxSlippage,
                  controller: _tecMaxSlippage,
                  focusNode: _focusMaxSlippage,
                  onTap: () {
                    _ensureVisible(_keyMaxSlippage);
                    _tryParseMaxSlippage(_tecMaxSlippage.text);
                  },
                  decoration: InputDecoration(
                    border: border,
                    enabledBorder: border,
                    focusedBorder: border.copyWith(
                      borderSide: BorderSide(color: context.themeColor),
                    ),
                    filled: false,
                    suffix: const Text('%'),
                  ),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                  onChanged: _tryParseMaxSlippage,
                );
              },
            ),
          ],
          const Gap.v(20.0),
          Text(
            context.l10n.textNetworkFees,
            style: context.textTheme.headlineSmall,
          ),
          const Gap.v(10.0),
          Row(
            spacing: 6.0,
            children: [
              ...EnumTradePriorityLevel.values.map(
                (e) {
                  final selected = priorityLevel == e;
                  return Expanded(
                    child: ThemeTextButton(
                      onPressed: () {
                        _updatePriorityLevel(e);
                      },
                      height: 40.0,
                      color: selected
                          ? context.themeColor
                          : context.meTheme.listColor,
                      child: Row(
                        spacing: 6.0,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (e.svg case final svg?)
                            svg.svg(
                              width: 14.0,
                              height: 18.0,
                              colorFilter: selected
                                  ? Colors.black.filter
                                  : null,
                            ),
                          Text(
                            e.displayName(context),
                            style: TextStyle(
                              color: !selected
                                  ? context.textTheme.bodySmall?.color
                                  : null,
                              fontSize: 16.0,
                              fontWeight: !selected ? FontWeight.normal : null,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
          if (isPriorityCustom) ...[
            const Gap.v(20.0),
            Text(
              context.l10n.textCustomSpeedFee,
              style: context.textTheme.bodyLarge,
            ),
            Text(
              context.l10n.textCustomSpeedFeeDescription,
              style: context.textTheme.bodySmall?.copyWith(fontSize: 14.0),
            ),
            const Gap.v(10.0),
            Builder(
              builder: (context) {
                final border = UnderlineInputBorder(
                  borderSide: BorderSide(color: context.theme.dividerColor),
                );
                return TextField(
                  key: _keyPrioritySpeedFee,
                  controller: _tecPrioritySpeedFee,
                  focusNode: _focusPrioritySpeedFee,
                  onTap: () {
                    _ensureVisible(_keyPrioritySpeedFee);
                    _tryParsePrioritySpeedFee(_tecPrioritySpeedFee.text);
                  },
                  decoration: InputDecoration(
                    border: border,
                    enabledBorder: border,
                    focusedBorder: border.copyWith(
                      borderSide: BorderSide(color: context.themeColor),
                    ),
                    filled: false,
                    suffix: Text(context.l10n.textSol),
                  ),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                  onChanged: _tryParsePrioritySpeedFee,
                );
              },
            ),
          ],
          const Gap.v(20.0),
          ThemeTextButton(
            onPressed: () {
              _saveSettings();
              context.navigator.pop();
            },
            text: context.l10nME.doneButton,
          ),
          Gap.bottomInsets(
            context,
            context.bottomPadding.max(24.0),
          ),
        ],
      ),
    );
  }
}
