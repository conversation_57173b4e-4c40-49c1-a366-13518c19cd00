import 'dart:convert';
import 'dart:io' show HttpHeaders;

import 'package:agora_rtc_engine/agora_rtc_engine.dart'
    show ClientRoleType, ClientRoleTypeExt;
import 'package:decimal/decimal.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:solana/solana.dart' show solDecimalPlaces;

import '/constants/constants.dart' show globalDeepCollectionEquality;
import '/models/_shared.dart';
import '/models/group.dart';
import '/models/live.dart';
import '/models/messaging.dart';
import '/models/tokens.dart';
import '/models/user.dart';
import '/provider/api.dart' hide apiServiceProvider;
import '/provider/token.dart' show tradeSettingsRepoProvider;

part 'service.freezed.dart';

part 'service.g.dart';

final class ServiceApi {
  ServiceApi(this.ref);

  final Ref ref;

  late final http = ref
      .read(httpProvider)
      .clone(
        options: BaseOptions(
          baseUrl: '${env.API_URL_SERVICE}/user',
        ),
      );

  Future<String> firebaseLogin(
    String idToken, {
    String? xHandle,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.post(
        '/firebase/login',
        data: <String, String>{
          'idToken': idToken,
          if (xHandle != null) 'twitterHandle': xHandle,
        },
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<String> activateUser({
    required String code,
    required String token,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.put(
        '/user/active',
        data: <String, String>{'code': code},
        options: Options(
          headers: {HttpHeaders.authorizationHeader: 'Bearer $token'},
        ),
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<String> getDemoJWT() async {
    final res = await retryWith(
      () => http.get(
        '/demo/login',
        options: Options(
          headers: <String, dynamic>{
            HttpHeaders.authorizationHeader: '', // Anonymous.
          },
        ),
      ),
    );
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<String> getRefreshJWT({CancelToken? cancelToken}) async {
    final res = await retryWith(
      () => http.get('/user/refresh_token'),
    );
    final rep = Rep.fromJson(res.data, (v) => v as String);
    return rep.data;
  }

  Future<T> _getUserInfo<T extends UserInfoBase>({
    required T Function(Map<String, dynamic>) fromJson,
    String? userRawId,
    String? token,
    CancelToken? cancelToken,
  }) async {
    final result = await retryWith(
      () => http.get(
        userRawId != null ? '/user/$userRawId/info' : '/user/info',
        options: Options(
          headers: {
            if (token != null) HttpHeaders.authorizationHeader: 'Bearer $token',
          },
        ),
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(result.data, (v) => fromJson(v.asJson()));
    return rep.data;
  }

  Future<UserInfo> getUserInfo({
    required String userRawId,
    String? token,
    CancelToken? cancelToken,
  }) async {
    final result = await _getUserInfo(
      fromJson: UserInfo.fromJson,
      userRawId: userRawId,
      token: token,
      cancelToken: cancelToken,
    );
    return result;
  }

  Future<PersistentUserInfo> getSelfUserInfo({
    String? userRawId,
    String? token,
    CancelToken? cancelToken,
  }) async {
    final result = await _getUserInfo(
      fromJson: PersistentUserInfo.fromJson,
      token: token,
      cancelToken: cancelToken,
    );
    return result;
  }

  Future<List<Chain>> getChainSettings({
    String? token,
    bool enabledOnly = true,
    bool tradableOnly = false,
  }) async {
    final res = await retryWith(
      () => http.get(
        '/settings/chains',
        options: Options(
          headers: {
            if (token != null) HttpHeaders.authorizationHeader: 'Bearer $token',
          },
        ),
      ),
    );
    final rep = Rep.fromJson(
      res.data,
      (v) => (v as List).cast<Map>().map((e) => Chain.fromJson(e.cast())),
    );
    final chains = rep.data.where((e) {
      bool predicate = true;
      if (enabledOnly) {
        predicate = predicate && e.enabled;
      }
      if (tradableOnly) {
        predicate = predicate && e.tradable;
      }
      return predicate;
    });
    return chains.toList();
  }

  Future<void> updateSettings(
    UserSettingsRequest request, {
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.put(
        '/user/setting',
        data: request.toJson(),
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(res.data, (v) => v);
  }

  Future<void> activateWallet(Chain chain) async {
    final res = await retryWith(
      () => http.post(
        '/wallet/activate',
        data: <String, String>{'chain': chain.name},
      ),
    );
    Rep.fromJson(res.data, (v) => v);
  }

  Future<List<UserAccountProvider>> getAccountProviders({
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.get(
        '/user/providers',
        cancelToken: cancelToken,
      ),
    );
    final rep = ListRep.fromJson(
      res.data,
      (v) => UserAccountProvider.fromJson(v.asJson()),
    );
    return rep.list;
  }

  Future<void> bindAccountProvider(
    String idToken, {
    String? xHandle,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.post(
        '/user/providers/bind',
        data: <String, String>{
          'idToken': idToken,
          if (xHandle != null) 'twitterHandle': xHandle,
        },
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(res.data, (v) => v);
  }

  Future<void> unbindAccountProvider(
    String providerId, {
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.post(
        '/user/providers/unbind',
        data: <String, dynamic>{'providerId': providerId},
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(res.data, (v) => v);
  }

  Future<bool> toggleUserFollow({
    required String userRawId,
    required bool follow,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.post(
        '/user/${follow ? 'follow' : 'unfollow'}',
        queryParameters: <String, String>{'uniqId': userRawId},
        data: <String, String>{'uniqId': userRawId},
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(res.data, (v) => v);
    return follow;
  }

  Future<Paged<UserInfo>> getUserFollowingList({
    required String? userRawId,
    required int page,
    required int size,
    required bool following,
    CancelToken? cancelToken,
  }) async {
    final result = await retryWith(
      () => http.get(
        '/user/${following ? 'following' : 'followers'}',
        queryParameters: <String, String>{
          if (userRawId != null) 'uniqId': userRawId,
        },
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(
      result.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => UserInfo.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Map<String, String> _getTokenPagedQueryParameters({
    required String sortBy,
    required String timeframe,
    required int page,
    required int size,
  }) {
    return <String, String>{
      'sortBy': sortBy,
      'timeframe': timeframe,
      'pageNum': page.toString(),
      'pageSize': size.toString(),
    };
  }

  Future<Paged<TokenObject>> _getTokenPaged({
    required String path,
    required Map<String, String> queryParameters,
    CancelToken? cancelToken,
  }) async {
    final result = await retryWith(
      () => http.get(
        path,
        queryParameters: queryParameters,
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(
      result.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => TokenObject.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<TokenObject?> getToken(
    String address, {
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.get(
        '/token/$address',
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(
      res.data,
      (v) => switch (v) {
        final Map v => TokenObject.fromJson(v.cast<String, dynamic>()),
        _ => null,
      },
    );
    return rep.data;
  }

  Future<Paged<TokenObject>> getDiscoveryTokenList({
    required String sortBy,
    required String timeframe,
    required int page,
    required int size,
    CancelToken? cancelToken,
  }) {
    return _getTokenPaged(
      path: '/discovery/tokens',
      queryParameters: _getTokenPagedQueryParameters(
        sortBy: sortBy,
        timeframe: timeframe,
        page: page,
        size: size,
      ),
      cancelToken: cancelToken,
    );
  }

  Future<List<TokenObject>> getTokensBySearch({
    String? name,
    String? symbol,
    String? address,
    CancelToken? cancelToken,
  }) async {
    final paged = await _getTokenPaged(
      path: '/token/list',
      queryParameters: <String, String>{
        if (name != null) 'name': name,
        if (symbol != null) 'symbol': symbol,
        if (address != null) 'address': address,
        'pageNum': '1',
        'pageSize': '50',
      },
      cancelToken: cancelToken,
    );
    return paged.list;
  }

  Future<List<ApiServiceUserToken>> getUserTokens({
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.get(
        '/user/user_tokens',
        cancelToken: cancelToken,
      ),
    );
    final rep = ListRep.fromJson(
      res.data,
      (v) => ApiServiceUserToken.fromJson(v.asJson()),
    );
    return rep.list;
  }

  Future<String?> sendToken({
    required IToken token,
    required Decimal amount,
    required String address,
    CancelToken? cancelToken,
  }) async {
    final int decimals;
    if (token.isSOL) {
      decimals = solDecimalPlaces;
    } else {
      final mint = await ref
          .read(apiSolanaProvider)
          .getMintAccountByAddress(token.address);
      decimals = mint.decimals;
    }
    final data = <String, dynamic>{
      'amount': amount.shift(decimals).toString(),
      'decimals': decimals,
      'toAddress': address,
      if (!token.isSOL) 'tokenAddress': token.address,
    };
    final result = await http.post(
      '/wallet/send_${token.isSOL ? 'solana' : 'token'}',
      data: data,
      cancelToken: cancelToken,
    );
    final rep = Rep.fromJson(result.data, (v) => v as String?);
    return rep.data;
  }

  Future<(String?, Map<String, Object>, Map<String, dynamic>)>
  createTradeOrder({
    required TokenObject? buying,
    required TokenObject? selling,
    required String chain, // Chain.name
    required Decimal amount,
    required int? decimals,
    CancelToken? cancelToken,
  }) async {
    final tradeSettings = ref.read(tradeSettingsRepoProvider);
    final tradeDirection = buying != null
        ? EnumTokenTradeDirection.buy
        : EnumTokenTradeDirection.sell;
    if (decimals == null) {
      if (selling case final selling?) {
        final mint = await ref
            .read(apiSolanaProvider)
            .getMintAccountByAddress(selling.address);
        decimals = mint.decimals;
      } else {
        decimals = solDecimalPlaces;
      }
    }
    final scaledAmount = amount.shift(decimals).floor();
    final token = (buying ?? selling)!;
    final data = <String, Object>{
      'address': token.address,
      'amount': scaledAmount.toString(),
      'chain': chain,
      'dir': tradeDirection.value,
      ...tradeSettings.toData(solDecimalPlaces),
    };
    final result = await http.post(
      '/user/orders',
      data: data,
      cancelToken: cancelToken,
    );
    final rep = Rep.fromJson(
      result.data,
      (v) => TradeOrder.fromJson(v.asJson()),
    );
    return (rep.data.txId, data, result.data as Map<String, dynamic>);
  }

  Future<String> getWalletExport({required bool mnemonic}) async {
    final res = await retryWith(
      () => http.post(
        '/wallet/export',
        data: {'exportMnemonic': mnemonic},
      ),
    );
    final rep = Rep.fromJson(res.data, (v) => v as String);
    final decoded = utf8.decode(base64.decode(rep.data));
    return decoded;
  }

  Future<({String rtc, String rtm, ClientRoleType? role})> getLiveTokens(
    String id, {
    bool refresh = false,
    CancelToken? cancelToken,
  }) async {
    final result = await retryWith(
      () => http.post(
        '/channel/$id/agora_token',
        data: <String, dynamic>{'refresh': refresh},
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(result.data, (v) => v.asJson());
    return (
      rtc: rep.data['rtcToken'] as String,
      rtm: rep.data['rtmToken'] as String,
      role: switch (rep.data['agoraRole']) {
        final int value => ClientRoleTypeExt.fromValue(value),
        _ => null,
      },
    );
  }

  Future<List<LiveRoom>> getLiveList({
    String? tokenAddress,
    CancelToken? cancelToken,
  }) async {
    final result = await retryWith(
      () => http.get(
        '/channel/list',
        queryParameters: <String, String>{
          if (tokenAddress case final s? when s.isNotEmpty) 'address': s,
        },
        cancelToken: cancelToken,
      ),
    );
    final rep = ListRep.fromJson(
      result.data,
      (v) => LiveRoom.fromJson(v.asJson()),
    );
    return rep.list;
  }

  Future<LiveRoomParticipation> createLiveRoom({
    required String uuid,
    required bool private,
    String? tokenAddress,
    String? groupId,
    CancelToken? cancelToken,
  }) async {
    final result = await http.post(
      '/channel/create',
      data: <String, dynamic>{
        'uuid': uuid,
        'isPrivate': private,
        'address': tokenAddress,
        'groupId': groupId,
      },
      cancelToken: cancelToken,
    );
    final rep = Rep.fromJson(
      result.data,
      (v) => LiveRoomParticipation.fromJson(v.asJson()),
    );
    return rep.data;
  }

  Future<LiveRoomParticipation> joinLiveRoom({
    required String roomId,
    required String uuid,
    CancelToken? cancelToken,
  }) async {
    final result = await retryWith(
      () => http.post(
        '/channel/$roomId/join',
        data: <String, Object>{'uuid': uuid},
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(
      result.data,
      (v) => LiveRoomParticipation.fromJson(v.asJson()),
    );
    return rep.data;
  }

  Future<LiveRoom> updateLiveRoom(
    String id, {
    String? displayName,
    bool? chatEnabled,
    CancelToken? cancelToken,
  }) async {
    final result = await retryWith(
      () => http.put(
        '/channel/$id',
        data: <String, dynamic>{
          if (displayName != null) 'displayName': displayName,
          if (chatEnabled != null) 'chatEnabled': chatEnabled,
        },
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(result.data, (v) => v);
    return getLiveRoomInfo(id, cancelToken: cancelToken);
  }

  Future<void> closeLiveRoom(
    String id, {
    CancelToken? cancelToken,
  }) async {
    final result = await retryWith(
      () => http.delete(
        '/channel/$id',
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(result.data, (v) => v);
  }

  Future<LiveRoomUsers> getLiveRoomUsers(
    String id, {
    bool all = false,
    CancelToken? cancelToken,
  }) async {
    final result = await retryWith(
      () => http.get(
        '/channel/$id/${all ? 'all_users' : 'users'}',
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(
      result.data,
      (v) => LiveRoomUsers.fromJson(v.asJson()),
    );
    return rep.data;
  }

  Future<void> switchLiveRoomRoleForUser(
    String id,
    String userId,
    ClientRoleType role, {
    CancelToken? cancelToken,
  }) async {
    final result = await retryWith(
      () => http.put(
        '/channel/$id/switch_role',
        data: <String, dynamic>{
          'uniqId': userId,
          'role': role.value(),
        },
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(result.data, (v) => v);
  }

  Future<LiveRoom> getLiveRoomInfo(
    String id, {
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.get(
        '/channel/$id/info',
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(res.data, (v) => LiveRoom.fromJson(v.asJson()));
    return rep.data;
  }

  Future<LiveRoomSummary> getLiveRoomSummary(
    String id, {
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.get(
        '/channel/$id/summary',
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(
      res.data,
      (v) => LiveRoomSummary.fromJson(v.asJson()),
    );
    return rep.data;
  }

  Future<Paged<LiveRoomSummary>> getLiveSummaryList({
    required int page,
    required int size,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.get(
        '/channel/history_list',
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => LiveRoomSummary.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<void> toggleCAPinToLiveRoom({
    required bool pin,
    required String roomId,
    required String address,
    required String chainId,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.put(
        '/channel/$roomId/v2/'
        '${pin ? 'pin_token' : 'unpin_token'}',
        data: <String, dynamic>{'address': address, 'chain': chainId},
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(res.data, (v) => v);
  }

  Future<void> pushCAToLiveRoom(
    String id,
    String address,
    String chain, {
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.post(
        '/channel/$id/v2/tokens/create',
        data: <String, dynamic>{'address': address, 'chain': chain},
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(res.data, (v) => v);
  }

  Future<List<LiveRoomToken>> getLiveRoomCAs(
    String id, {
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.get(
        '/channel/$id/v2/tokens',
        cancelToken: cancelToken,
      ),
    );
    final rep = ListRep.fromJson(
      res.data,
      (v) => LiveRoomToken.fromJson(v.asJson()),
    );
    return rep.list;
  }

  Future<Group> getGroupInfo({
    required String id,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.get(
        '/group/$id',
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(res.data, (v) => Group.fromJson(v.asJson()));
    return rep.data;
  }

  Future<Paged<Group>> getGroupList({
    required GroupType type,
    required int page,
    required int size,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.get(
        switch (type) {
          GroupType.owned => '/group/created_list',
          GroupType.joined => '/group/joined_list',
          GroupType.explore => '/group/other_list',
        },
        queryParameters: <String, String>{
          'pageNum': page.toString(),
          'pageSize': size.toString(),
        },
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => Group.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<Paged<UserInfo>> getGroupUsers({
    required String id,
    required int page,
    required int size,
    required bool includeSelf,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.get(
        '/group/$id/users',
        queryParameters: <String, String>{
          'pageNum': page.toString(),
          'pageSize': size.toString(),
          'includeSelf': includeSelf.toString(),
        },
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => UserInfo.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<Paged<GroupLiveRoom>> getGroupLives({
    required String id,
    required int page,
    required int size,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.get(
        '/group/$id/channels',
        queryParameters: <String, String>{
          'pageNum': page.toString(),
          'pageSize': size.toString(),
        },
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(
      res.data,
      (v) => Paged.fromJson(
        v.asJson(),
        (v) => GroupLiveRoom.fromJson(v.asJson()),
      ),
    );
    return rep.data;
  }

  Future<Group> joinGroup({
    required String id,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.post(
        '/group/join/$id',
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(res.data, (v) => Group.fromJson(v.asJson()));
    return rep.data;
  }

  Future<void> leaveGroup({
    required String id,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.post(
        '/group/quit/$id',
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(res.data, (v) => v);
  }

  Future<Group> createGroup({
    required String name,
    required String description,
    required Uint8List logoBytes,
    required String logoExtension,
    required bool needConfirm,
    CancelToken? cancelToken,
  }) async {
    final hashCode = Object.hash(
      name,
      description,
      globalDeepCollectionEquality.hash(logoBytes),
      logoExtension,
      needConfirm,
    );
    final res = await retryWith(
      () => http.post(
        '/group',
        data: <String, Object>{
          'name': name,
          'description': description,
          'logo': base64.encode(logoBytes),
          'ext': logoExtension,
          'needConfirm': needConfirm,
          'hashCode': hashCode.toRadixString(16),
        },
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(res.data, (v) => Group.fromJson(v.asJson()));
    return rep.data;
  }

  Future<Group> updateGroup({
    required String id,
    String? name,
    String? description,
    Uint8List? logoBytes,
    String? logoExtension,
    bool? needConfirm,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.put(
        '/group/$id',
        data: <String, Object>{
          if (name != null) 'name': name,
          if (description != null) 'description': description,
          if (logoBytes != null) 'logo': base64.encode(logoBytes),
          if (logoExtension != null) 'ext': logoExtension,
          if (needConfirm != null) 'needConfirm': needConfirm,
        },
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(res.data, (v) => Group.fromJson(v.asJson()));
    return rep.data;
  }

  Future<List<UserInfo>> getGroupAppliedUsers({
    required String id,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.get('/group/$id/applys', cancelToken: cancelToken),
    );
    final rep = ListRep.fromJson(
      res.data,
      (v) => UserInfo.fromJson(v.asJson()),
    );
    return rep.list;
  }

  Future<void> updateGroupAppliedUser({
    required String groupId,
    required String userId,
    required bool approved,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.put(
        '/group/$groupId/applys/$userId'
        '/${approved ? 'approve' : 'reject'}',
      ),
    );
    Rep.fromJson(res.data, (v) => v);
  }

  Future<CallSession> createCall({
    required String roomId,
    required Set<String> userIds,
    String? groupId,
    CancelToken? cancelToken,
  }) async {
    final hashCode = Object.hashAll([
      if (groupId != null) groupId,
      roomId,
      globalDeepCollectionEquality.hash(userIds),
    ]);
    final res = await retryWith(
      () => http.post(
        '/call/create',
        data: <String, Object>{
          if (groupId != null) 'groupId': groupId,
          'channelId': roomId,
          'userIds': userIds.toList(growable: false),
          'hashCode': hashCode.toRadixString(16),
        },
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(
      res.data,
      (v) => CallSession.fromJson(v.asJson()),
    );
    return rep.data;
  }

  Future<CallSession> getCallInfo({
    required String callId,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.get(
        '/call/$callId',
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(
      res.data,
      (v) => CallSession.fromJson(v.asJson()),
    );
    return rep.data;
  }

  Future<void> cancelCall({
    required String callId,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.delete(
        '/call/$callId',
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(res.data, (v) => v);
  }

  Future<void> acceptCall({
    required String callId,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.put(
        '/call/$callId/accept',
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(res.data, (v) => v);
  }

  Future<void> declineCall({
    required String callId,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.put(
        '/call/$callId/decline',
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(res.data, (v) => v);
  }

  Future<LiveRoomLink> getLinkResolve(
    String url, {
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.post(
        '/link/create',
        data: <String, String>{'href': url},
        cancelToken: cancelToken,
      ),
    );
    final rep = Rep.fromJson(
      res.data,
      (v) => LiveRoomLink.fromJson(v.asJson()),
    );
    return rep.data;
  }

  Future<List<LiveRoomLink>> getLiveRoomLinks(
    String roomId, {
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.get(
        '/channel/$roomId/links',
        cancelToken: cancelToken,
      ),
    );
    final rep = ListRep.fromJson(
      res.data,
      (v) => LiveRoomLink.fromJson(v.asJson()),
    );
    return rep.list;
  }

  Future<void> pushLinkToLiveRoom(
    String roomId,
    String linkId, {
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.post(
        '/channel/$roomId/links',
        data: <String, dynamic>{'uniqId': linkId},
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(res.data, (v) => v);
  }

  Future<void> removeLinkFromLiveRoom(
    String roomId,
    String linkId, {
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.delete(
        '/channel/$roomId/links/$linkId',
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(res.data, (v) => v);
  }

  Future<void> toggleLinkPinToLiveRoom({
    required bool pin,
    required String roomId,
    required String linkId,
    CancelToken? cancelToken,
  }) async {
    final res = await retryWith(
      () => http.put(
        '/channel/$roomId/'
        '${pin ? 'pin_link' : 'unpin_link'}',
        data: <String, dynamic>{'uniqId': linkId},
        cancelToken: cancelToken,
      ),
    );
    Rep.fromJson(res.data, (v) => v);
  }
}

@freezed
sealed class ApiServiceUserToken with _$ApiServiceUserToken {
  const factory ApiServiceUserToken({
    @JsonKey(name: 'tokenAddress') required String address,
    @JsonKey(name: 'totalQuantity') required Decimal totalQuantity,
    @JsonKey(name: 'averageCost') required Decimal costAverage,
    @JsonKey(name: 'totalCost') required Decimal costTotal,
    @JsonKey(name: 'accuCost') required Decimal costAccumulate,
    @JsonKey(name: 'accuQuoteCost') required Decimal costQuoteAccumulate,
    @JsonKey(name: 'realizedPnl') required Decimal pnlRealized,
    @JsonKey(name: 'unrealizedPnl') required Decimal pnlUnrealized,
    @JsonKey(name: 'totalPnl') required Decimal pnlTotal,
    @JsonKey(name: 'quoteTotalPnl') required Decimal pnlQuoteTotal,
  }) = _ApiServiceUserToken;

  factory ApiServiceUserToken.fromJson(Map<String, Object?> json) =>
      _$ApiServiceUserTokenFromJson(json);
}
