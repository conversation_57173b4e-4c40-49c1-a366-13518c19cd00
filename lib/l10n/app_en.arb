{"@@locale": "en", "appTitle": "<PERSON><PERSON>", "appTitleBeta": "<PERSON><PERSON> (Beta)", "textTakingLongerThanExpected": "This takes longer than expected... ({time}s)", "textPinForgot": "You need to verify your account to reset the PIN code.", "textLoginAgreeToPre": "I have read and agreed to", "textLoginAgreeToEULA": "EULA", "textLoginAgreeToAnd": "and", "textLoginAgreeToPP": "Privacy Policy", "toastLoginAgreeToTerms": "You have to read and agree to the terms and conditions before proceeding.", "titleLoginEmail": "Sign In With Email", "textLoginFirebaseAuthException": "Something went wrong when signing in to your account. Please try again later.", "textLoginActivateInvalidCode": "The activation code you entered is invalid. Try another one.", "toastEmailSent": "An email has been sent to your address.", "labelNavDiscovery": "Discovery", "labelNavSocial": "Social", "labelNavWallet": "Wallet", "toastErrorMismatchAuthorizedUser": "Authorized users mismatched.", "toastErrorNoPermissionMicrophoneForLive": "Microphone permission is required to enabled the mic.", "toastErrorLiveHostDeclinedMicRequest": "Host declined your mic request.", "toastErrorLiveClosed": "The live is closed.", "toastErrorGroupUserBlocked": "You have been blocked by the group.", "toastErrorGroupNotJoined": "Please join the group first.", "titleQrCodeInvalidPrompt": "Invalid QR Code", "textQrCodeInvalidPrompt": "Please try scanning another QR code", "textQrCodeErrorResolve": "Aim at the QR code or adjust the distance.", "textQrCodeErrorInvalid": "QR code is not supported.", "textQrCodeNotFound": "QR code not found", "labelSearchHint": "Search CA/Token", "textLiveCACount": "{count, plural, =1{1 CA} other{{count} CAs}}", "@textLiveCACount": {"placeholders": {"count": {"type": "int"}}}, "textLiveCAMentioned": "mentioned", "textChainNotYetSupported": "{chain} is not yet supported.", "textFailedToLookup": "Failed to lookup {text}.\nClick to retry.", "titleSelectToken": "Select Token", "textNoTokenFound": "No token was found.", "labelWalletSendTo": "To:", "hintWalletAddress": "Type wallet address...", "textSendInProgress": "Send in progress...", "textTransactionProcessing": "Transaction is processing!", "textTransactionFailed": "Transaction failed, try again later.", "textLiveOnlineCount": "**{count}** online", "toastCANotFound": "CA not found", "toastCAFailedToFetch": "Failed to fetch the CA info", "toastLinkInvalid": "Invalid link", "titleRequestToSpeak": "Request to speak", "titleAudiences": "Audiences", "hintPinCA": "Pin a CA", "hintPinLink": "Pin a link", "labelPin": "<PERSON>n", "textCallEnded": "Call Ended.", "textCalling": "Calling...", "titleScoopCall": "<PERSON><PERSON> Call", "labelCallStartNew": "Start New Call", "labelCallCancel": "Cancel Call", "labelCallUserCount": "Call ({count})", "labelChat": "Cha<PERSON>", "labelShare": "Share", "labelGoLive": "Go Live", "labelJoin": "Join", "labelJoinLive": "Join Live", "titleMembers": "Members", "labelGroupLastLiveIndicatorNow": "Live Now", "labelGroupLastLiveIndicatorAgo": "Live {ago} ago", "labelGroupLastLiveIndicatorSuffixPlural": "s", "labelGroupLastLiveIndicatorSuffixSecond": " second", "labelGroupLastLiveIndicatorSuffixMinute": " minute", "labelGroupLastLiveIndicatorSuffixHour": " hour", "labelGroupLastLiveIndicatorSuffixDay": " day", "labelGroupLastLiveIndicatorSuffixMonth": " month", "labelGroupLastLiveIndicatorSuffixYear": " year", "titleJoinRequests": "Join Requests", "titleRecentLivestreams": "Recent livestreams", "labelGroupStartLivestream": "Start a Livestream", "textGroupLiveListEmpty": "Waiting for the first group live...", "labelLiveDisplayNameFallback": "Live in {name}", "labelLiveIndicator": "live", "titleSettings": "Settings", "titleSettingsGroupAccount": "Account", "textSettingsAccountConnections": "Connections", "titleSettingsGroupSecurity": "Security", "titleSettingsGroupWallet": "Wallet", "textSettingsExportWallet": "Export Wallet", "titleSettingsGroupMiscellaneous": "Miscellaneous", "textSettingsSmartAppLinkRecognition": "Smart App Link Recognition", "textSettingsClearCaches": "Clear caches", "textSettingsAboutUs": "About us", "labelLogout": "Log out", "labelSwitchXAccount": "Switch X account", "titleSettingsLogout": "Ready to Log Out?", "textSettingsLogout": "You'll need to sign in again, or sign in with a different account.", "textSettingsSwitchX": "If you want to switch to another X account before signing in, please check this guide: ", "textContinueWith": "Continue with", "textViewAsGuest": "View as Guest", "titleActivateAccount": "Activate Your Account", "hintInviteCode": "Enter the invite code", "labelActivate": "Activate", "textActivationFailed": "Failed to activate. ({error})", "titleAccountConnectionsSignInWith": "Sign in with {name}", "labelNotConnected": "Not Connected", "labelConnect": "Connect", "labelDisconnect": "Disconnect", "textAccountConnectionProviderLinked": "This account has already been connected to your Scoop account.", "textAccountConnectionProviderLinkedToAnother": "This account has already been used to create a Scoop account and cannot be connected again.", "textAccountConnectionProviderCannotRemoveLast": "You currently have only one connected account. Please connect another account before disconnecting this one.", "textAccountConnectionProviderNotFound": "The account is not connected to your Scoop account. Please try again.", "titleExportWalletNotice": "Notice", "textExportWalletNotice1": "The mnemonics provides full access to my wallet and funds.", "textExportWalletNotice2": "Turnkey Wallet is a **non-custodial wallet**. That means you're the sole owner of your wallet", "textExportWalletNotice3": "**Make sure no one is looking at your screen.** No one should request this.", "titleYourWallet": "Your Wallet", "textSaveMnemonics": "DO NOT forget to save this mnemonics. Save a backup on a storage medium and write it down.", "textClickToReveal": "Click to reveal", "textPoweredByTurnkey": "Powered by <PERSON><PERSON>", "textLiveConnectionStateConnecting": "Connecting…", "textLiveConnectionStateReconnecting": "Reconnecting…", "textLiveConnectionStateFailed": "Connection error. Trying to reconnect…", "textLiveConnectionStateDisconnected": "You seems disconnected. Trying to reconnect…", "labelMicRequesting": "Requesting", "labelMicAskToSpeak": "ask to speak", "labelMicOff": "mic off", "labelMicOn": "mic on", "labelAudioRouteNoAvailable": "No available audio routes", "labelAudioRouteBluetooth": "Bluetooth", "labelAudioRouteSpeaker": "Speaker", "labelAudioRouteEarpiece": "Earpiece", "titleLiveMicUpdated": "Mic status updated!", "toastLiveMicWaiting": "<PERSON><PERSON> is waiting to be approved by the host.", "toastLiveNowSpeaker": "You are now a speaker!", "toastLiveNoLongerSpeaker": "The host has removed your speaker role.", "textTokens": "Tokens", "textTraders": "Traders", "textActivity": "Activity", "textChooseTimeFrame": "Choose the time frame", "textDiscoveryEmpty": "Have some rest...", "textDiscoveryError": "Oops, you are overheated...", "titleYourChainAddress": "Your {chain} wallet address", "textWalletNotAvailableForTheChain": "The wallet is not available for the chain.", "textWalletCreateTip": "Create a wallet to receive airdrops and manage assets.", "toastGroupFailedToPickImage": "Failed to pick image.", "toastGroupFailedToParsePickedFile": "Unable to parse {filename}", "hintGroupImageSize": "(<PERSON>: {max}, Current Size: {current})", "hintGroupName": "Group name", "hintGroupInfo": "Group info", "textJoinByRequest": "Join By Request", "labelRequestToJoin": "Request to join", "titleGroupAccessRequired": "Group Access Require", "textGroupAccessRequired": "This live is for group members with an approval only.", "textGroupWaitingForApproval": "Waiting for approval...", "titleGroupJoinToAccessLive": "Join Group to Access Live", "textGroupJoinToAccessLive": "Join the group instantly to access this live room.", "labelCreate": "Create", "textPosition": "Position", "textPnL": "P&L", "textWalletListEmpty": "Human... try to earn more....", "labelGroups": "Groups", "labelLive": "Live", "textLiveListEmpty": "Waiting for lives...", "textLiveListError": "Oops, somebody has to fix the mic...", "titlePrivateLive": "Private Live", "titleGroupLive": "Group Live", "titlePublicLive": "Public Live", "titlePublic": "Public", "titlePrivate": "Private", "labelGroupsOwned": "Owned", "labelGroupsJoined": "Joined", "labelGroupsExplore": "Explore", "textCreateFirstGroup": "Your turn to create the first group!", "titleGroupActions": "Group Actions", "labelEditGroup": "Edit group info", "labelLeaveGroup": "Leave the group", "labelDismissGroup": "Dismiss the group", "titleConfirmation": "Confirmation", "textConfirmLeaveGroup": "Are you sure you want to leave this group?", "textConfirmDismissGroup": "Are you sure you want to dismiss this group? **After your action, everyone will be gone from the group.**", "labelCancel": "Cancel", "labelConfirm": "Confirm", "textGroupLeft": "You have left the group", "textFollowings": "Followings", "textFollowers": "Followers", "textVolume": "Volume", "labelShareVia": "Share via...", "textShareVia": "X, Telegram, Warpcast, ...", "labelDetails": "Details", "labelMyOrders": "My Orders", "textNothingFound": "We've tried, but nothing found.", "titleStats": "Stats", "labelMarketCap": "Market Cap", "labelSupply": "Supply", "labelLiquidity": "Liquidity", "labelHolders": "Holders", "labelBuys": "Buys", "labelSells": "Sells", "labelAbout": "About", "labelCreatedAt": "Created At", "labelAudit": "Audit", "labelTopHolders": "Top 10 holders", "labelMintable": "Mintable", "labelFreezable": "Freezable", "labelBalanceMutable": "Balance Mutable", "labelMetadataMutable": "<PERSON><PERSON><PERSON>", "textPassed": "Passed", "textFailed": "Failed", "textBuyingInProgress": "Buying in progress...", "textSellingInProgress": "Selling in progress...", "textSlippage": "Slippage", "textSlippageDescription": "Slippage tolerance allows price deviation, with higher slippage increasing success in volatile markets.", "textAuto": "Auto", "textAvailable": "Available", "textTokenBalanceAvailable": "**{balance} {symbol}** Available", "textCustom": "Custom", "textMaxSlippage": "Max slippage", "textMaxSlippageDescription": "Slippage will be intelligently chosen based on the token and network conditions. Set the maximum slippage you are willing to accept.", "textNetworkFees": "Network Fees", "textCustomSpeedFee": "Custom Speed Fee", "textCustomSpeedFeeDescription": "Choose how much to tip validators to process your trades faster.", "textSol": "SOL", "textTapBackToLive": "Tap to get back to the live.", "textRequestingToSpeak": "Requesting to speak in the live...", "textLiveWaitingForApproval": "Waiting for approval...", "labelLivePanelAllowChat": "Allow chat", "labelLivePanelMuteAllSpeakers": "Mute all speakers", "labelLivePanelBackgroundAudio": "Background Audio", "labelReport": "Report", "titleLivePanelReportLive": "Report the Live", "textLivePanelReportLiveDescription": "You are about to report the Live for inappropriate content. Are you sure?", "textThanksReport": "Thanks for your report!", "labelCloseLive": "Close the Live", "labelLeaveLive": "Leave the Live", "labelSpeakers": "Speakers", "labelLiveUserInviteToSpeak": "Invite to speak", "labelLiveUserRemoveFromSpeaker": "<PERSON><PERSON><PERSON> from Speaker", "labelViewUserProfile": "View Profile", "labelLiveUserMute": "Mute", "labelLiveUserUnmute": "Unmute", "labelLiveUserKick": "Kick", "labelLiveUserBlock": "Block", "titleLiveNotificationCAPinned": "A new CA was pinned!", "textLiveNotificationCAPinned": "{symbol} was just pinned by {username} in the live room.", "titleLiveNotificationLinkPinned": "A new link was pinned!", "textLiveNotificationLinkPinned": "{title} was just pinned by {username} in the live room.", "titleLiveNotificationKicked": "Your device is kicked from the live room.", "textLiveNotificationKicked": "Another device has joined the live room, or you have been kicked by the room host.", "labelUserFollow": "Follow", "labelUserUnfollow": "Unfollow", "labelUserFollowingCount": "Following", "labelUserFollowersCount": "Followers", "textUserNoFollowingYet": "No following yet.", "textUserNoFollowersYet": "No followers yet.", "labelUserInvite": "Invite", "titleUserInvite": "Invite Friends", "textUserInvite": "Share your Scoop invite code—invite friends and get rewarded together!", "labelTotalReferrals": "Total Referrals", "labelRefCode": "Referral Code", "labelInviteAFriend": "Invite a friend", "titleLiveEnded": "Live Ended", "titleLiveLeaved": "Left Live Room", "titleLiveEndedListeners": "Listeners", "titleLiveEndedMentionedCAs": "Mentioned CAs", "titleLiveEndedDuration": "Duration"}