import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'gen/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('en')];

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'Scoop'**
  String get appTitle;

  /// No description provided for @appTitleBeta.
  ///
  /// In en, this message translates to:
  /// **'Scoop (Beta)'**
  String get appTitleBeta;

  /// No description provided for @textTakingLongerThanExpected.
  ///
  /// In en, this message translates to:
  /// **'This takes longer than expected... ({time}s)'**
  String textTakingLongerThanExpected(Object time);

  /// No description provided for @textPinForgot.
  ///
  /// In en, this message translates to:
  /// **'You need to verify your account to reset the PIN code.'**
  String get textPinForgot;

  /// No description provided for @textLoginAgreeToPre.
  ///
  /// In en, this message translates to:
  /// **'I have read and agreed to'**
  String get textLoginAgreeToPre;

  /// No description provided for @textLoginAgreeToEULA.
  ///
  /// In en, this message translates to:
  /// **'EULA'**
  String get textLoginAgreeToEULA;

  /// No description provided for @textLoginAgreeToAnd.
  ///
  /// In en, this message translates to:
  /// **'and'**
  String get textLoginAgreeToAnd;

  /// No description provided for @textLoginAgreeToPP.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get textLoginAgreeToPP;

  /// No description provided for @toastLoginAgreeToTerms.
  ///
  /// In en, this message translates to:
  /// **'You have to read and agree to the terms and conditions before proceeding.'**
  String get toastLoginAgreeToTerms;

  /// No description provided for @titleLoginEmail.
  ///
  /// In en, this message translates to:
  /// **'Sign In With Email'**
  String get titleLoginEmail;

  /// No description provided for @textLoginFirebaseAuthException.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong when signing in to your account. Please try again later.'**
  String get textLoginFirebaseAuthException;

  /// No description provided for @textLoginActivateInvalidCode.
  ///
  /// In en, this message translates to:
  /// **'The activation code you entered is invalid. Try another one.'**
  String get textLoginActivateInvalidCode;

  /// No description provided for @toastEmailSent.
  ///
  /// In en, this message translates to:
  /// **'An email has been sent to your address.'**
  String get toastEmailSent;

  /// No description provided for @labelNavDiscovery.
  ///
  /// In en, this message translates to:
  /// **'Discovery'**
  String get labelNavDiscovery;

  /// No description provided for @labelNavSocial.
  ///
  /// In en, this message translates to:
  /// **'Social'**
  String get labelNavSocial;

  /// No description provided for @labelNavWallet.
  ///
  /// In en, this message translates to:
  /// **'Wallet'**
  String get labelNavWallet;

  /// No description provided for @toastErrorMismatchAuthorizedUser.
  ///
  /// In en, this message translates to:
  /// **'Authorized users mismatched.'**
  String get toastErrorMismatchAuthorizedUser;

  /// No description provided for @toastErrorNoPermissionMicrophoneForLive.
  ///
  /// In en, this message translates to:
  /// **'Microphone permission is required to enabled the mic.'**
  String get toastErrorNoPermissionMicrophoneForLive;

  /// No description provided for @toastErrorLiveHostDeclinedMicRequest.
  ///
  /// In en, this message translates to:
  /// **'Host declined your mic request.'**
  String get toastErrorLiveHostDeclinedMicRequest;

  /// No description provided for @toastErrorLiveClosed.
  ///
  /// In en, this message translates to:
  /// **'The live is closed.'**
  String get toastErrorLiveClosed;

  /// No description provided for @toastErrorGroupUserBlocked.
  ///
  /// In en, this message translates to:
  /// **'You have been blocked by the group.'**
  String get toastErrorGroupUserBlocked;

  /// No description provided for @toastErrorGroupNotJoined.
  ///
  /// In en, this message translates to:
  /// **'Please join the group first.'**
  String get toastErrorGroupNotJoined;

  /// No description provided for @titleQrCodeInvalidPrompt.
  ///
  /// In en, this message translates to:
  /// **'Invalid QR Code'**
  String get titleQrCodeInvalidPrompt;

  /// No description provided for @textQrCodeInvalidPrompt.
  ///
  /// In en, this message translates to:
  /// **'Please try scanning another QR code'**
  String get textQrCodeInvalidPrompt;

  /// No description provided for @textQrCodeErrorResolve.
  ///
  /// In en, this message translates to:
  /// **'Aim at the QR code or adjust the distance.'**
  String get textQrCodeErrorResolve;

  /// No description provided for @textQrCodeErrorInvalid.
  ///
  /// In en, this message translates to:
  /// **'QR code is not supported.'**
  String get textQrCodeErrorInvalid;

  /// No description provided for @textQrCodeNotFound.
  ///
  /// In en, this message translates to:
  /// **'QR code not found'**
  String get textQrCodeNotFound;

  /// No description provided for @labelSearchHint.
  ///
  /// In en, this message translates to:
  /// **'Search CA/Token'**
  String get labelSearchHint;

  /// No description provided for @textLiveCACount.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, =1{1 CA} other{{count} CAs}}'**
  String textLiveCACount(int count);

  /// No description provided for @textLiveCAMentioned.
  ///
  /// In en, this message translates to:
  /// **'mentioned'**
  String get textLiveCAMentioned;

  /// No description provided for @textChainNotYetSupported.
  ///
  /// In en, this message translates to:
  /// **'{chain} is not yet supported.'**
  String textChainNotYetSupported(Object chain);

  /// No description provided for @textFailedToLookup.
  ///
  /// In en, this message translates to:
  /// **'Failed to lookup {text}.\nClick to retry.'**
  String textFailedToLookup(Object text);

  /// No description provided for @titleSelectToken.
  ///
  /// In en, this message translates to:
  /// **'Select Token'**
  String get titleSelectToken;

  /// No description provided for @textNoTokenFound.
  ///
  /// In en, this message translates to:
  /// **'No token was found.'**
  String get textNoTokenFound;

  /// No description provided for @labelWalletSendTo.
  ///
  /// In en, this message translates to:
  /// **'To:'**
  String get labelWalletSendTo;

  /// No description provided for @hintWalletAddress.
  ///
  /// In en, this message translates to:
  /// **'Type wallet address...'**
  String get hintWalletAddress;

  /// No description provided for @textSendInProgress.
  ///
  /// In en, this message translates to:
  /// **'Send in progress...'**
  String get textSendInProgress;

  /// No description provided for @textTransactionProcessing.
  ///
  /// In en, this message translates to:
  /// **'Transaction is processing!'**
  String get textTransactionProcessing;

  /// No description provided for @textTransactionFailed.
  ///
  /// In en, this message translates to:
  /// **'Transaction failed, try again later.'**
  String get textTransactionFailed;

  /// No description provided for @textLiveOnlineCount.
  ///
  /// In en, this message translates to:
  /// **'**{count}** online'**
  String textLiveOnlineCount(Object count);

  /// No description provided for @toastCANotFound.
  ///
  /// In en, this message translates to:
  /// **'CA not found'**
  String get toastCANotFound;

  /// No description provided for @toastCAFailedToFetch.
  ///
  /// In en, this message translates to:
  /// **'Failed to fetch the CA info'**
  String get toastCAFailedToFetch;

  /// No description provided for @toastLinkInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid link'**
  String get toastLinkInvalid;

  /// No description provided for @titleRequestToSpeak.
  ///
  /// In en, this message translates to:
  /// **'Request to speak'**
  String get titleRequestToSpeak;

  /// No description provided for @titleAudiences.
  ///
  /// In en, this message translates to:
  /// **'Audiences'**
  String get titleAudiences;

  /// No description provided for @hintPinCA.
  ///
  /// In en, this message translates to:
  /// **'Pin a CA'**
  String get hintPinCA;

  /// No description provided for @hintPinLink.
  ///
  /// In en, this message translates to:
  /// **'Pin a link'**
  String get hintPinLink;

  /// No description provided for @labelPin.
  ///
  /// In en, this message translates to:
  /// **'Pin'**
  String get labelPin;

  /// No description provided for @textCallEnded.
  ///
  /// In en, this message translates to:
  /// **'Call Ended.'**
  String get textCallEnded;

  /// No description provided for @textCalling.
  ///
  /// In en, this message translates to:
  /// **'Calling...'**
  String get textCalling;

  /// No description provided for @titleScoopCall.
  ///
  /// In en, this message translates to:
  /// **'Scoop Call'**
  String get titleScoopCall;

  /// No description provided for @labelCallStartNew.
  ///
  /// In en, this message translates to:
  /// **'Start New Call'**
  String get labelCallStartNew;

  /// No description provided for @labelCallCancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel Call'**
  String get labelCallCancel;

  /// No description provided for @labelCallUserCount.
  ///
  /// In en, this message translates to:
  /// **'Call ({count})'**
  String labelCallUserCount(Object count);

  /// No description provided for @labelChat.
  ///
  /// In en, this message translates to:
  /// **'Chat'**
  String get labelChat;

  /// No description provided for @labelShare.
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get labelShare;

  /// No description provided for @labelGoLive.
  ///
  /// In en, this message translates to:
  /// **'Go Live'**
  String get labelGoLive;

  /// No description provided for @labelJoin.
  ///
  /// In en, this message translates to:
  /// **'Join'**
  String get labelJoin;

  /// No description provided for @labelJoinLive.
  ///
  /// In en, this message translates to:
  /// **'Join Live'**
  String get labelJoinLive;

  /// No description provided for @titleMembers.
  ///
  /// In en, this message translates to:
  /// **'Members'**
  String get titleMembers;

  /// No description provided for @labelGroupLastLiveIndicatorNow.
  ///
  /// In en, this message translates to:
  /// **'Live Now'**
  String get labelGroupLastLiveIndicatorNow;

  /// No description provided for @labelGroupLastLiveIndicatorAgo.
  ///
  /// In en, this message translates to:
  /// **'Live {ago} ago'**
  String labelGroupLastLiveIndicatorAgo(Object ago);

  /// No description provided for @labelGroupLastLiveIndicatorSuffixPlural.
  ///
  /// In en, this message translates to:
  /// **'s'**
  String get labelGroupLastLiveIndicatorSuffixPlural;

  /// No description provided for @labelGroupLastLiveIndicatorSuffixSecond.
  ///
  /// In en, this message translates to:
  /// **' second'**
  String get labelGroupLastLiveIndicatorSuffixSecond;

  /// No description provided for @labelGroupLastLiveIndicatorSuffixMinute.
  ///
  /// In en, this message translates to:
  /// **' minute'**
  String get labelGroupLastLiveIndicatorSuffixMinute;

  /// No description provided for @labelGroupLastLiveIndicatorSuffixHour.
  ///
  /// In en, this message translates to:
  /// **' hour'**
  String get labelGroupLastLiveIndicatorSuffixHour;

  /// No description provided for @labelGroupLastLiveIndicatorSuffixDay.
  ///
  /// In en, this message translates to:
  /// **' day'**
  String get labelGroupLastLiveIndicatorSuffixDay;

  /// No description provided for @labelGroupLastLiveIndicatorSuffixMonth.
  ///
  /// In en, this message translates to:
  /// **' month'**
  String get labelGroupLastLiveIndicatorSuffixMonth;

  /// No description provided for @labelGroupLastLiveIndicatorSuffixYear.
  ///
  /// In en, this message translates to:
  /// **' year'**
  String get labelGroupLastLiveIndicatorSuffixYear;

  /// No description provided for @titleJoinRequests.
  ///
  /// In en, this message translates to:
  /// **'Join Requests'**
  String get titleJoinRequests;

  /// No description provided for @titleRecentLivestreams.
  ///
  /// In en, this message translates to:
  /// **'Recent livestreams'**
  String get titleRecentLivestreams;

  /// No description provided for @labelGroupStartLivestream.
  ///
  /// In en, this message translates to:
  /// **'Start a Livestream'**
  String get labelGroupStartLivestream;

  /// No description provided for @textGroupLiveListEmpty.
  ///
  /// In en, this message translates to:
  /// **'Waiting for the first group live...'**
  String get textGroupLiveListEmpty;

  /// No description provided for @labelLiveDisplayNameFallback.
  ///
  /// In en, this message translates to:
  /// **'Live in {name}'**
  String labelLiveDisplayNameFallback(Object name);

  /// No description provided for @labelLiveIndicator.
  ///
  /// In en, this message translates to:
  /// **'live'**
  String get labelLiveIndicator;

  /// No description provided for @titleSettings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get titleSettings;

  /// No description provided for @titleSettingsGroupAccount.
  ///
  /// In en, this message translates to:
  /// **'Account'**
  String get titleSettingsGroupAccount;

  /// No description provided for @textSettingsAccountConnections.
  ///
  /// In en, this message translates to:
  /// **'Connections'**
  String get textSettingsAccountConnections;

  /// No description provided for @titleSettingsGroupSecurity.
  ///
  /// In en, this message translates to:
  /// **'Security'**
  String get titleSettingsGroupSecurity;

  /// No description provided for @titleSettingsGroupWallet.
  ///
  /// In en, this message translates to:
  /// **'Wallet'**
  String get titleSettingsGroupWallet;

  /// No description provided for @textSettingsExportWallet.
  ///
  /// In en, this message translates to:
  /// **'Export Wallet'**
  String get textSettingsExportWallet;

  /// No description provided for @titleSettingsGroupMiscellaneous.
  ///
  /// In en, this message translates to:
  /// **'Miscellaneous'**
  String get titleSettingsGroupMiscellaneous;

  /// No description provided for @textSettingsSmartAppLinkRecognition.
  ///
  /// In en, this message translates to:
  /// **'Smart App Link Recognition'**
  String get textSettingsSmartAppLinkRecognition;

  /// No description provided for @textSettingsClearCaches.
  ///
  /// In en, this message translates to:
  /// **'Clear caches'**
  String get textSettingsClearCaches;

  /// No description provided for @textSettingsAboutUs.
  ///
  /// In en, this message translates to:
  /// **'About us'**
  String get textSettingsAboutUs;

  /// No description provided for @labelLogout.
  ///
  /// In en, this message translates to:
  /// **'Log out'**
  String get labelLogout;

  /// No description provided for @labelSwitchXAccount.
  ///
  /// In en, this message translates to:
  /// **'Switch X account'**
  String get labelSwitchXAccount;

  /// No description provided for @titleSettingsLogout.
  ///
  /// In en, this message translates to:
  /// **'Ready to Log Out?'**
  String get titleSettingsLogout;

  /// No description provided for @textSettingsLogout.
  ///
  /// In en, this message translates to:
  /// **'You\'ll need to sign in again, or sign in with a different account.'**
  String get textSettingsLogout;

  /// No description provided for @textSettingsSwitchX.
  ///
  /// In en, this message translates to:
  /// **'If you want to switch to another X account before signing in, please check this guide: '**
  String get textSettingsSwitchX;

  /// No description provided for @textContinueWith.
  ///
  /// In en, this message translates to:
  /// **'Continue with'**
  String get textContinueWith;

  /// No description provided for @textViewAsGuest.
  ///
  /// In en, this message translates to:
  /// **'View as Guest'**
  String get textViewAsGuest;

  /// No description provided for @titleActivateAccount.
  ///
  /// In en, this message translates to:
  /// **'Activate Your Account'**
  String get titleActivateAccount;

  /// No description provided for @hintInviteCode.
  ///
  /// In en, this message translates to:
  /// **'Enter the invite code'**
  String get hintInviteCode;

  /// No description provided for @labelActivate.
  ///
  /// In en, this message translates to:
  /// **'Activate'**
  String get labelActivate;

  /// No description provided for @textActivationFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to activate. ({error})'**
  String textActivationFailed(Object error);

  /// No description provided for @titleAccountConnectionsSignInWith.
  ///
  /// In en, this message translates to:
  /// **'Sign in with {name}'**
  String titleAccountConnectionsSignInWith(Object name);

  /// No description provided for @labelNotConnected.
  ///
  /// In en, this message translates to:
  /// **'Not Connected'**
  String get labelNotConnected;

  /// No description provided for @labelConnect.
  ///
  /// In en, this message translates to:
  /// **'Connect'**
  String get labelConnect;

  /// No description provided for @labelDisconnect.
  ///
  /// In en, this message translates to:
  /// **'Disconnect'**
  String get labelDisconnect;

  /// No description provided for @textAccountConnectionProviderLinked.
  ///
  /// In en, this message translates to:
  /// **'This account has already been connected to your Scoop account.'**
  String get textAccountConnectionProviderLinked;

  /// No description provided for @textAccountConnectionProviderLinkedToAnother.
  ///
  /// In en, this message translates to:
  /// **'This account has already been used to create a Scoop account and cannot be connected again.'**
  String get textAccountConnectionProviderLinkedToAnother;

  /// No description provided for @textAccountConnectionProviderCannotRemoveLast.
  ///
  /// In en, this message translates to:
  /// **'You currently have only one connected account. Please connect another account before disconnecting this one.'**
  String get textAccountConnectionProviderCannotRemoveLast;

  /// No description provided for @textAccountConnectionProviderNotFound.
  ///
  /// In en, this message translates to:
  /// **'The account is not connected to your Scoop account. Please try again.'**
  String get textAccountConnectionProviderNotFound;

  /// No description provided for @titleExportWalletNotice.
  ///
  /// In en, this message translates to:
  /// **'Notice'**
  String get titleExportWalletNotice;

  /// No description provided for @textExportWalletNotice1.
  ///
  /// In en, this message translates to:
  /// **'The mnemonics provides full access to my wallet and funds.'**
  String get textExportWalletNotice1;

  /// No description provided for @textExportWalletNotice2.
  ///
  /// In en, this message translates to:
  /// **'Turnkey Wallet is a **non-custodial wallet**. That means you\'re the sole owner of your wallet'**
  String get textExportWalletNotice2;

  /// No description provided for @textExportWalletNotice3.
  ///
  /// In en, this message translates to:
  /// **'**Make sure no one is looking at your screen.** No one should request this.'**
  String get textExportWalletNotice3;

  /// No description provided for @titleYourWallet.
  ///
  /// In en, this message translates to:
  /// **'Your Wallet'**
  String get titleYourWallet;

  /// No description provided for @textSaveMnemonics.
  ///
  /// In en, this message translates to:
  /// **'DO NOT forget to save this mnemonics. Save a backup on a storage medium and write it down.'**
  String get textSaveMnemonics;

  /// No description provided for @textClickToReveal.
  ///
  /// In en, this message translates to:
  /// **'Click to reveal'**
  String get textClickToReveal;

  /// No description provided for @textPoweredByTurnkey.
  ///
  /// In en, this message translates to:
  /// **'Powered by Turnkey'**
  String get textPoweredByTurnkey;

  /// No description provided for @textLiveConnectionStateConnecting.
  ///
  /// In en, this message translates to:
  /// **'Connecting…'**
  String get textLiveConnectionStateConnecting;

  /// No description provided for @textLiveConnectionStateReconnecting.
  ///
  /// In en, this message translates to:
  /// **'Reconnecting…'**
  String get textLiveConnectionStateReconnecting;

  /// No description provided for @textLiveConnectionStateFailed.
  ///
  /// In en, this message translates to:
  /// **'Connection error. Trying to reconnect…'**
  String get textLiveConnectionStateFailed;

  /// No description provided for @textLiveConnectionStateDisconnected.
  ///
  /// In en, this message translates to:
  /// **'You seems disconnected. Trying to reconnect…'**
  String get textLiveConnectionStateDisconnected;

  /// No description provided for @labelMicRequesting.
  ///
  /// In en, this message translates to:
  /// **'Requesting'**
  String get labelMicRequesting;

  /// No description provided for @labelMicAskToSpeak.
  ///
  /// In en, this message translates to:
  /// **'ask to speak'**
  String get labelMicAskToSpeak;

  /// No description provided for @labelMicOff.
  ///
  /// In en, this message translates to:
  /// **'mic off'**
  String get labelMicOff;

  /// No description provided for @labelMicOn.
  ///
  /// In en, this message translates to:
  /// **'mic on'**
  String get labelMicOn;

  /// No description provided for @labelAudioRouteNoAvailable.
  ///
  /// In en, this message translates to:
  /// **'No available audio routes'**
  String get labelAudioRouteNoAvailable;

  /// No description provided for @labelAudioRouteBluetooth.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth'**
  String get labelAudioRouteBluetooth;

  /// No description provided for @labelAudioRouteSpeaker.
  ///
  /// In en, this message translates to:
  /// **'Speaker'**
  String get labelAudioRouteSpeaker;

  /// No description provided for @labelAudioRouteEarpiece.
  ///
  /// In en, this message translates to:
  /// **'Earpiece'**
  String get labelAudioRouteEarpiece;

  /// No description provided for @titleLiveMicUpdated.
  ///
  /// In en, this message translates to:
  /// **'Mic status updated!'**
  String get titleLiveMicUpdated;

  /// No description provided for @toastLiveMicWaiting.
  ///
  /// In en, this message translates to:
  /// **'Mic is waiting to be approved by the host.'**
  String get toastLiveMicWaiting;

  /// No description provided for @toastLiveNowSpeaker.
  ///
  /// In en, this message translates to:
  /// **'You are now a speaker!'**
  String get toastLiveNowSpeaker;

  /// No description provided for @toastLiveNoLongerSpeaker.
  ///
  /// In en, this message translates to:
  /// **'The host has removed your speaker role.'**
  String get toastLiveNoLongerSpeaker;

  /// No description provided for @textTokens.
  ///
  /// In en, this message translates to:
  /// **'Tokens'**
  String get textTokens;

  /// No description provided for @textTraders.
  ///
  /// In en, this message translates to:
  /// **'Traders'**
  String get textTraders;

  /// No description provided for @textActivity.
  ///
  /// In en, this message translates to:
  /// **'Activity'**
  String get textActivity;

  /// No description provided for @textChooseTimeFrame.
  ///
  /// In en, this message translates to:
  /// **'Choose the time frame'**
  String get textChooseTimeFrame;

  /// No description provided for @textDiscoveryEmpty.
  ///
  /// In en, this message translates to:
  /// **'Have some rest...'**
  String get textDiscoveryEmpty;

  /// No description provided for @textDiscoveryError.
  ///
  /// In en, this message translates to:
  /// **'Oops, you are overheated...'**
  String get textDiscoveryError;

  /// No description provided for @titleYourChainAddress.
  ///
  /// In en, this message translates to:
  /// **'Your {chain} wallet address'**
  String titleYourChainAddress(Object chain);

  /// No description provided for @textWalletNotAvailableForTheChain.
  ///
  /// In en, this message translates to:
  /// **'The wallet is not available for the chain.'**
  String get textWalletNotAvailableForTheChain;

  /// No description provided for @textWalletCreateTip.
  ///
  /// In en, this message translates to:
  /// **'Create a wallet to receive airdrops and manage assets.'**
  String get textWalletCreateTip;

  /// No description provided for @toastGroupFailedToPickImage.
  ///
  /// In en, this message translates to:
  /// **'Failed to pick image.'**
  String get toastGroupFailedToPickImage;

  /// No description provided for @toastGroupFailedToParsePickedFile.
  ///
  /// In en, this message translates to:
  /// **'Unable to parse {filename}'**
  String toastGroupFailedToParsePickedFile(Object filename);

  /// No description provided for @hintGroupImageSize.
  ///
  /// In en, this message translates to:
  /// **'(Max Size: {max}, Current Size: {current})'**
  String hintGroupImageSize(Object current, Object max);

  /// No description provided for @hintGroupName.
  ///
  /// In en, this message translates to:
  /// **'Group name'**
  String get hintGroupName;

  /// No description provided for @hintGroupInfo.
  ///
  /// In en, this message translates to:
  /// **'Group info'**
  String get hintGroupInfo;

  /// No description provided for @textJoinByRequest.
  ///
  /// In en, this message translates to:
  /// **'Join By Request'**
  String get textJoinByRequest;

  /// No description provided for @labelRequestToJoin.
  ///
  /// In en, this message translates to:
  /// **'Request to join'**
  String get labelRequestToJoin;

  /// No description provided for @titleGroupAccessRequired.
  ///
  /// In en, this message translates to:
  /// **'Group Access Require'**
  String get titleGroupAccessRequired;

  /// No description provided for @textGroupAccessRequired.
  ///
  /// In en, this message translates to:
  /// **'This live is for group members with an approval only.'**
  String get textGroupAccessRequired;

  /// No description provided for @textGroupWaitingForApproval.
  ///
  /// In en, this message translates to:
  /// **'Waiting for approval...'**
  String get textGroupWaitingForApproval;

  /// No description provided for @titleGroupJoinToAccessLive.
  ///
  /// In en, this message translates to:
  /// **'Join Group to Access Live'**
  String get titleGroupJoinToAccessLive;

  /// No description provided for @textGroupJoinToAccessLive.
  ///
  /// In en, this message translates to:
  /// **'Join the group instantly to access this live room.'**
  String get textGroupJoinToAccessLive;

  /// No description provided for @labelCreate.
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get labelCreate;

  /// No description provided for @textPosition.
  ///
  /// In en, this message translates to:
  /// **'Position'**
  String get textPosition;

  /// No description provided for @textPnL.
  ///
  /// In en, this message translates to:
  /// **'P&L'**
  String get textPnL;

  /// No description provided for @textWalletListEmpty.
  ///
  /// In en, this message translates to:
  /// **'Human... try to earn more....'**
  String get textWalletListEmpty;

  /// No description provided for @labelGroups.
  ///
  /// In en, this message translates to:
  /// **'Groups'**
  String get labelGroups;

  /// No description provided for @labelLive.
  ///
  /// In en, this message translates to:
  /// **'Live'**
  String get labelLive;

  /// No description provided for @textLiveListEmpty.
  ///
  /// In en, this message translates to:
  /// **'Waiting for lives...'**
  String get textLiveListEmpty;

  /// No description provided for @textLiveListError.
  ///
  /// In en, this message translates to:
  /// **'Oops, somebody has to fix the mic...'**
  String get textLiveListError;

  /// No description provided for @titlePrivateLive.
  ///
  /// In en, this message translates to:
  /// **'Private Live'**
  String get titlePrivateLive;

  /// No description provided for @titleGroupLive.
  ///
  /// In en, this message translates to:
  /// **'Group Live'**
  String get titleGroupLive;

  /// No description provided for @titlePublicLive.
  ///
  /// In en, this message translates to:
  /// **'Public Live'**
  String get titlePublicLive;

  /// No description provided for @titlePublic.
  ///
  /// In en, this message translates to:
  /// **'Public'**
  String get titlePublic;

  /// No description provided for @titlePrivate.
  ///
  /// In en, this message translates to:
  /// **'Private'**
  String get titlePrivate;

  /// No description provided for @labelGroupsOwned.
  ///
  /// In en, this message translates to:
  /// **'Owned'**
  String get labelGroupsOwned;

  /// No description provided for @labelGroupsJoined.
  ///
  /// In en, this message translates to:
  /// **'Joined'**
  String get labelGroupsJoined;

  /// No description provided for @labelGroupsExplore.
  ///
  /// In en, this message translates to:
  /// **'Explore'**
  String get labelGroupsExplore;

  /// No description provided for @textCreateFirstGroup.
  ///
  /// In en, this message translates to:
  /// **'Your turn to create the first group!'**
  String get textCreateFirstGroup;

  /// No description provided for @titleGroupActions.
  ///
  /// In en, this message translates to:
  /// **'Group Actions'**
  String get titleGroupActions;

  /// No description provided for @labelEditGroup.
  ///
  /// In en, this message translates to:
  /// **'Edit group info'**
  String get labelEditGroup;

  /// No description provided for @labelLeaveGroup.
  ///
  /// In en, this message translates to:
  /// **'Leave the group'**
  String get labelLeaveGroup;

  /// No description provided for @labelDismissGroup.
  ///
  /// In en, this message translates to:
  /// **'Dismiss the group'**
  String get labelDismissGroup;

  /// No description provided for @titleConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Confirmation'**
  String get titleConfirmation;

  /// No description provided for @textConfirmLeaveGroup.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to leave this group?'**
  String get textConfirmLeaveGroup;

  /// No description provided for @textConfirmDismissGroup.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to dismiss this group? **After your action, everyone will be gone from the group.**'**
  String get textConfirmDismissGroup;

  /// No description provided for @labelCancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get labelCancel;

  /// No description provided for @labelConfirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get labelConfirm;

  /// No description provided for @textGroupLeft.
  ///
  /// In en, this message translates to:
  /// **'You have left the group'**
  String get textGroupLeft;

  /// No description provided for @textFollowings.
  ///
  /// In en, this message translates to:
  /// **'Followings'**
  String get textFollowings;

  /// No description provided for @textFollowers.
  ///
  /// In en, this message translates to:
  /// **'Followers'**
  String get textFollowers;

  /// No description provided for @textVolume.
  ///
  /// In en, this message translates to:
  /// **'Volume'**
  String get textVolume;

  /// No description provided for @labelShareVia.
  ///
  /// In en, this message translates to:
  /// **'Share via...'**
  String get labelShareVia;

  /// No description provided for @textShareVia.
  ///
  /// In en, this message translates to:
  /// **'X, Telegram, Warpcast, ...'**
  String get textShareVia;

  /// No description provided for @labelDetails.
  ///
  /// In en, this message translates to:
  /// **'Details'**
  String get labelDetails;

  /// No description provided for @labelMyOrders.
  ///
  /// In en, this message translates to:
  /// **'My Orders'**
  String get labelMyOrders;

  /// No description provided for @textNothingFound.
  ///
  /// In en, this message translates to:
  /// **'We\'ve tried, but nothing found.'**
  String get textNothingFound;

  /// No description provided for @titleStats.
  ///
  /// In en, this message translates to:
  /// **'Stats'**
  String get titleStats;

  /// No description provided for @labelMarketCap.
  ///
  /// In en, this message translates to:
  /// **'Market Cap'**
  String get labelMarketCap;

  /// No description provided for @labelSupply.
  ///
  /// In en, this message translates to:
  /// **'Supply'**
  String get labelSupply;

  /// No description provided for @labelLiquidity.
  ///
  /// In en, this message translates to:
  /// **'Liquidity'**
  String get labelLiquidity;

  /// No description provided for @labelHolders.
  ///
  /// In en, this message translates to:
  /// **'Holders'**
  String get labelHolders;

  /// No description provided for @labelBuys.
  ///
  /// In en, this message translates to:
  /// **'Buys'**
  String get labelBuys;

  /// No description provided for @labelSells.
  ///
  /// In en, this message translates to:
  /// **'Sells'**
  String get labelSells;

  /// No description provided for @labelAbout.
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get labelAbout;

  /// No description provided for @labelCreatedAt.
  ///
  /// In en, this message translates to:
  /// **'Created At'**
  String get labelCreatedAt;

  /// No description provided for @labelAudit.
  ///
  /// In en, this message translates to:
  /// **'Audit'**
  String get labelAudit;

  /// No description provided for @labelTopHolders.
  ///
  /// In en, this message translates to:
  /// **'Top 10 holders'**
  String get labelTopHolders;

  /// No description provided for @labelMintable.
  ///
  /// In en, this message translates to:
  /// **'Mintable'**
  String get labelMintable;

  /// No description provided for @labelFreezable.
  ///
  /// In en, this message translates to:
  /// **'Freezable'**
  String get labelFreezable;

  /// No description provided for @labelBalanceMutable.
  ///
  /// In en, this message translates to:
  /// **'Balance Mutable'**
  String get labelBalanceMutable;

  /// No description provided for @labelMetadataMutable.
  ///
  /// In en, this message translates to:
  /// **'Metadata Mutable'**
  String get labelMetadataMutable;

  /// No description provided for @textPassed.
  ///
  /// In en, this message translates to:
  /// **'Passed'**
  String get textPassed;

  /// No description provided for @textFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed'**
  String get textFailed;

  /// No description provided for @textBuyingInProgress.
  ///
  /// In en, this message translates to:
  /// **'Buying in progress...'**
  String get textBuyingInProgress;

  /// No description provided for @textSellingInProgress.
  ///
  /// In en, this message translates to:
  /// **'Selling in progress...'**
  String get textSellingInProgress;

  /// No description provided for @textSlippage.
  ///
  /// In en, this message translates to:
  /// **'Slippage'**
  String get textSlippage;

  /// No description provided for @textSlippageDescription.
  ///
  /// In en, this message translates to:
  /// **'Slippage tolerance allows price deviation, with higher slippage increasing success in volatile markets.'**
  String get textSlippageDescription;

  /// No description provided for @textAuto.
  ///
  /// In en, this message translates to:
  /// **'Auto'**
  String get textAuto;

  /// No description provided for @textAvailable.
  ///
  /// In en, this message translates to:
  /// **'Available'**
  String get textAvailable;

  /// No description provided for @textTokenBalanceAvailable.
  ///
  /// In en, this message translates to:
  /// **'**{balance} {symbol}** Available'**
  String textTokenBalanceAvailable(Object balance, Object symbol);

  /// No description provided for @textCustom.
  ///
  /// In en, this message translates to:
  /// **'Custom'**
  String get textCustom;

  /// No description provided for @textMaxSlippage.
  ///
  /// In en, this message translates to:
  /// **'Max slippage'**
  String get textMaxSlippage;

  /// No description provided for @textMaxSlippageDescription.
  ///
  /// In en, this message translates to:
  /// **'Slippage will be intelligently chosen based on the token and network conditions. Set the maximum slippage you are willing to accept.'**
  String get textMaxSlippageDescription;

  /// No description provided for @textNetworkFees.
  ///
  /// In en, this message translates to:
  /// **'Network Fees'**
  String get textNetworkFees;

  /// No description provided for @textCustomSpeedFee.
  ///
  /// In en, this message translates to:
  /// **'Custom Speed Fee'**
  String get textCustomSpeedFee;

  /// No description provided for @textCustomSpeedFeeDescription.
  ///
  /// In en, this message translates to:
  /// **'Choose how much to tip validators to process your trades faster.'**
  String get textCustomSpeedFeeDescription;

  /// No description provided for @textSol.
  ///
  /// In en, this message translates to:
  /// **'SOL'**
  String get textSol;

  /// No description provided for @textTapBackToLive.
  ///
  /// In en, this message translates to:
  /// **'Tap to get back to the live.'**
  String get textTapBackToLive;

  /// No description provided for @textRequestingToSpeak.
  ///
  /// In en, this message translates to:
  /// **'Requesting to speak in the live...'**
  String get textRequestingToSpeak;

  /// No description provided for @textLiveWaitingForApproval.
  ///
  /// In en, this message translates to:
  /// **'Waiting for approval...'**
  String get textLiveWaitingForApproval;

  /// No description provided for @labelLivePanelAllowChat.
  ///
  /// In en, this message translates to:
  /// **'Allow chat'**
  String get labelLivePanelAllowChat;

  /// No description provided for @labelLivePanelMuteAllSpeakers.
  ///
  /// In en, this message translates to:
  /// **'Mute all speakers'**
  String get labelLivePanelMuteAllSpeakers;

  /// No description provided for @labelLivePanelBackgroundAudio.
  ///
  /// In en, this message translates to:
  /// **'Background Audio'**
  String get labelLivePanelBackgroundAudio;

  /// No description provided for @labelReport.
  ///
  /// In en, this message translates to:
  /// **'Report'**
  String get labelReport;

  /// No description provided for @titleLivePanelReportLive.
  ///
  /// In en, this message translates to:
  /// **'Report the Live'**
  String get titleLivePanelReportLive;

  /// No description provided for @textLivePanelReportLiveDescription.
  ///
  /// In en, this message translates to:
  /// **'You are about to report the Live for inappropriate content. Are you sure?'**
  String get textLivePanelReportLiveDescription;

  /// No description provided for @textThanksReport.
  ///
  /// In en, this message translates to:
  /// **'Thanks for your report!'**
  String get textThanksReport;

  /// No description provided for @labelCloseLive.
  ///
  /// In en, this message translates to:
  /// **'Close the Live'**
  String get labelCloseLive;

  /// No description provided for @labelLeaveLive.
  ///
  /// In en, this message translates to:
  /// **'Leave the Live'**
  String get labelLeaveLive;

  /// No description provided for @labelSpeakers.
  ///
  /// In en, this message translates to:
  /// **'Speakers'**
  String get labelSpeakers;

  /// No description provided for @labelLiveUserInviteToSpeak.
  ///
  /// In en, this message translates to:
  /// **'Invite to speak'**
  String get labelLiveUserInviteToSpeak;

  /// No description provided for @labelLiveUserRemoveFromSpeaker.
  ///
  /// In en, this message translates to:
  /// **'Remove from Speaker'**
  String get labelLiveUserRemoveFromSpeaker;

  /// No description provided for @labelViewUserProfile.
  ///
  /// In en, this message translates to:
  /// **'View Profile'**
  String get labelViewUserProfile;

  /// No description provided for @labelLiveUserMute.
  ///
  /// In en, this message translates to:
  /// **'Mute'**
  String get labelLiveUserMute;

  /// No description provided for @labelLiveUserUnmute.
  ///
  /// In en, this message translates to:
  /// **'Unmute'**
  String get labelLiveUserUnmute;

  /// No description provided for @labelLiveUserKick.
  ///
  /// In en, this message translates to:
  /// **'Kick'**
  String get labelLiveUserKick;

  /// No description provided for @labelLiveUserBlock.
  ///
  /// In en, this message translates to:
  /// **'Block'**
  String get labelLiveUserBlock;

  /// No description provided for @titleLiveNotificationCAPinned.
  ///
  /// In en, this message translates to:
  /// **'A new CA was pinned!'**
  String get titleLiveNotificationCAPinned;

  /// No description provided for @textLiveNotificationCAPinned.
  ///
  /// In en, this message translates to:
  /// **'{symbol} was just pinned by {username} in the live room.'**
  String textLiveNotificationCAPinned(Object symbol, Object username);

  /// No description provided for @titleLiveNotificationLinkPinned.
  ///
  /// In en, this message translates to:
  /// **'A new link was pinned!'**
  String get titleLiveNotificationLinkPinned;

  /// No description provided for @textLiveNotificationLinkPinned.
  ///
  /// In en, this message translates to:
  /// **'{title} was just pinned by {username} in the live room.'**
  String textLiveNotificationLinkPinned(Object title, Object username);

  /// No description provided for @titleLiveNotificationKicked.
  ///
  /// In en, this message translates to:
  /// **'Your device is kicked from the live room.'**
  String get titleLiveNotificationKicked;

  /// No description provided for @textLiveNotificationKicked.
  ///
  /// In en, this message translates to:
  /// **'Another device has joined the live room, or you have been kicked by the room host.'**
  String get textLiveNotificationKicked;

  /// No description provided for @labelUserFollow.
  ///
  /// In en, this message translates to:
  /// **'Follow'**
  String get labelUserFollow;

  /// No description provided for @labelUserUnfollow.
  ///
  /// In en, this message translates to:
  /// **'Unfollow'**
  String get labelUserUnfollow;

  /// No description provided for @labelUserFollowingCount.
  ///
  /// In en, this message translates to:
  /// **'Following'**
  String get labelUserFollowingCount;

  /// No description provided for @labelUserFollowersCount.
  ///
  /// In en, this message translates to:
  /// **'Followers'**
  String get labelUserFollowersCount;

  /// No description provided for @textUserNoFollowingYet.
  ///
  /// In en, this message translates to:
  /// **'No following yet.'**
  String get textUserNoFollowingYet;

  /// No description provided for @textUserNoFollowersYet.
  ///
  /// In en, this message translates to:
  /// **'No followers yet.'**
  String get textUserNoFollowersYet;

  /// No description provided for @labelUserInvite.
  ///
  /// In en, this message translates to:
  /// **'Invite'**
  String get labelUserInvite;

  /// No description provided for @titleUserInvite.
  ///
  /// In en, this message translates to:
  /// **'Invite Friends'**
  String get titleUserInvite;

  /// No description provided for @textUserInvite.
  ///
  /// In en, this message translates to:
  /// **'Share your Scoop invite code—invite friends and get rewarded together!'**
  String get textUserInvite;

  /// No description provided for @labelTotalReferrals.
  ///
  /// In en, this message translates to:
  /// **'Total Referrals'**
  String get labelTotalReferrals;

  /// No description provided for @labelRefCode.
  ///
  /// In en, this message translates to:
  /// **'Referral Code'**
  String get labelRefCode;

  /// No description provided for @labelInviteAFriend.
  ///
  /// In en, this message translates to:
  /// **'Invite a friend'**
  String get labelInviteAFriend;

  /// No description provided for @titleLiveEnded.
  ///
  /// In en, this message translates to:
  /// **'Live Ended'**
  String get titleLiveEnded;

  /// No description provided for @titleLiveLeaved.
  ///
  /// In en, this message translates to:
  /// **'Left Live Room'**
  String get titleLiveLeaved;

  /// No description provided for @titleLiveEndedListeners.
  ///
  /// In en, this message translates to:
  /// **'Listeners'**
  String get titleLiveEndedListeners;

  /// No description provided for @titleLiveEndedMentionedCAs.
  ///
  /// In en, this message translates to:
  /// **'Mentioned CAs'**
  String get titleLiveEndedMentionedCAs;

  /// No description provided for @titleLiveEndedDuration.
  ///
  /// In en, this message translates to:
  /// **'Duration'**
  String get titleLiveEndedDuration;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
