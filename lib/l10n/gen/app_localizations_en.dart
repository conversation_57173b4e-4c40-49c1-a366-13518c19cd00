// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Scoop';

  @override
  String get appTitleBeta => 'Scoop (Beta)';

  @override
  String textTakingLongerThanExpected(Object time) {
    return 'This takes longer than expected... (${time}s)';
  }

  @override
  String get textPinForgot =>
      'You need to verify your account to reset the PIN code.';

  @override
  String get textLoginAgreeToPre => 'I have read and agreed to';

  @override
  String get textLoginAgreeToEULA => 'EULA';

  @override
  String get textLoginAgreeToAnd => 'and';

  @override
  String get textLoginAgreeToPP => 'Privacy Policy';

  @override
  String get toastLoginAgreeToTerms =>
      'You have to read and agree to the terms and conditions before proceeding.';

  @override
  String get titleLoginEmail => 'Sign In With Email';

  @override
  String get textLoginFirebaseAuthException =>
      'Something went wrong when signing in to your account. Please try again later.';

  @override
  String get textLoginActivateInvalidCode =>
      'The activation code you entered is invalid. Try another one.';

  @override
  String get toastEmailSent => 'An email has been sent to your address.';

  @override
  String get labelNavDiscovery => 'Discovery';

  @override
  String get labelNavSocial => 'Social';

  @override
  String get labelNavWallet => 'Wallet';

  @override
  String get toastErrorMismatchAuthorizedUser => 'Authorized users mismatched.';

  @override
  String get toastErrorNoPermissionMicrophoneForLive =>
      'Microphone permission is required to enabled the mic.';

  @override
  String get toastErrorLiveHostDeclinedMicRequest =>
      'Host declined your mic request.';

  @override
  String get toastErrorLiveClosed => 'The live is closed.';

  @override
  String get toastErrorGroupUserBlocked =>
      'You have been blocked by the group.';

  @override
  String get toastErrorGroupNotJoined => 'Please join the group first.';

  @override
  String get titleQrCodeInvalidPrompt => 'Invalid QR Code';

  @override
  String get textQrCodeInvalidPrompt => 'Please try scanning another QR code';

  @override
  String get textQrCodeErrorResolve =>
      'Aim at the QR code or adjust the distance.';

  @override
  String get textQrCodeErrorInvalid => 'QR code is not supported.';

  @override
  String get textQrCodeNotFound => 'QR code not found';

  @override
  String get labelSearchHint => 'Search CA/Token';

  @override
  String textLiveCACount(int count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count CAs',
      one: '1 CA',
    );
    return '$_temp0';
  }

  @override
  String get textLiveCAMentioned => 'mentioned';

  @override
  String textChainNotYetSupported(Object chain) {
    return '$chain is not yet supported.';
  }

  @override
  String textFailedToLookup(Object text) {
    return 'Failed to lookup $text.\nClick to retry.';
  }

  @override
  String get titleSelectToken => 'Select Token';

  @override
  String get textNoTokenFound => 'No token was found.';

  @override
  String get labelWalletSendTo => 'To:';

  @override
  String get hintWalletAddress => 'Type wallet address...';

  @override
  String get textSendInProgress => 'Send in progress...';

  @override
  String get textTransactionProcessing => 'Transaction is processing!';

  @override
  String get textTransactionFailed => 'Transaction failed, try again later.';

  @override
  String textLiveOnlineCount(Object count) {
    return '**$count** online';
  }

  @override
  String get toastCANotFound => 'CA not found';

  @override
  String get toastCAFailedToFetch => 'Failed to fetch the CA info';

  @override
  String get toastLinkInvalid => 'Invalid link';

  @override
  String get titleRequestToSpeak => 'Request to speak';

  @override
  String get titleAudiences => 'Audiences';

  @override
  String get hintPinCA => 'Pin a CA';

  @override
  String get hintPinLink => 'Pin a link';

  @override
  String get labelPin => 'Pin';

  @override
  String get textCallEnded => 'Call Ended.';

  @override
  String get textCalling => 'Calling...';

  @override
  String get titleScoopCall => 'Scoop Call';

  @override
  String get labelCallStartNew => 'Start New Call';

  @override
  String get labelCallCancel => 'Cancel Call';

  @override
  String labelCallUserCount(Object count) {
    return 'Call ($count)';
  }

  @override
  String get labelChat => 'Chat';

  @override
  String get labelShare => 'Share';

  @override
  String get labelGoLive => 'Go Live';

  @override
  String get labelJoin => 'Join';

  @override
  String get labelJoinLive => 'Join Live';

  @override
  String get titleMembers => 'Members';

  @override
  String get labelGroupLastLiveIndicatorNow => 'Live Now';

  @override
  String labelGroupLastLiveIndicatorAgo(Object ago) {
    return 'Live $ago ago';
  }

  @override
  String get labelGroupLastLiveIndicatorSuffixPlural => 's';

  @override
  String get labelGroupLastLiveIndicatorSuffixSecond => ' second';

  @override
  String get labelGroupLastLiveIndicatorSuffixMinute => ' minute';

  @override
  String get labelGroupLastLiveIndicatorSuffixHour => ' hour';

  @override
  String get labelGroupLastLiveIndicatorSuffixDay => ' day';

  @override
  String get labelGroupLastLiveIndicatorSuffixMonth => ' month';

  @override
  String get labelGroupLastLiveIndicatorSuffixYear => ' year';

  @override
  String get titleJoinRequests => 'Join Requests';

  @override
  String get titleRecentLivestreams => 'Recent livestreams';

  @override
  String get labelGroupStartLivestream => 'Start a Livestream';

  @override
  String get textGroupLiveListEmpty => 'Waiting for the first group live...';

  @override
  String labelLiveDisplayNameFallback(Object name) {
    return 'Live in $name';
  }

  @override
  String get labelLiveIndicator => 'live';

  @override
  String get titleSettings => 'Settings';

  @override
  String get titleSettingsGroupAccount => 'Account';

  @override
  String get textSettingsAccountConnections => 'Connections';

  @override
  String get titleSettingsGroupSecurity => 'Security';

  @override
  String get titleSettingsGroupWallet => 'Wallet';

  @override
  String get textSettingsExportWallet => 'Export Wallet';

  @override
  String get titleSettingsGroupMiscellaneous => 'Miscellaneous';

  @override
  String get textSettingsSmartAppLinkRecognition =>
      'Smart App Link Recognition';

  @override
  String get textSettingsClearCaches => 'Clear caches';

  @override
  String get textSettingsAboutUs => 'About us';

  @override
  String get labelLogout => 'Log out';

  @override
  String get labelSwitchXAccount => 'Switch X account';

  @override
  String get titleSettingsLogout => 'Ready to Log Out?';

  @override
  String get textSettingsLogout =>
      'You\'ll need to sign in again, or sign in with a different account.';

  @override
  String get textSettingsSwitchX =>
      'If you want to switch to another X account before signing in, please check this guide: ';

  @override
  String get textContinueWith => 'Continue with';

  @override
  String get textViewAsGuest => 'View as Guest';

  @override
  String get titleActivateAccount => 'Activate Your Account';

  @override
  String get hintInviteCode => 'Enter the invite code';

  @override
  String get labelActivate => 'Activate';

  @override
  String textActivationFailed(Object error) {
    return 'Failed to activate. ($error)';
  }

  @override
  String titleAccountConnectionsSignInWith(Object name) {
    return 'Sign in with $name';
  }

  @override
  String get labelNotConnected => 'Not Connected';

  @override
  String get labelConnect => 'Connect';

  @override
  String get labelDisconnect => 'Disconnect';

  @override
  String get textAccountConnectionProviderLinked =>
      'This account has already been connected to your Scoop account.';

  @override
  String get textAccountConnectionProviderLinkedToAnother =>
      'This account has already been used to create a Scoop account and cannot be connected again.';

  @override
  String get textAccountConnectionProviderCannotRemoveLast =>
      'You currently have only one connected account. Please connect another account before disconnecting this one.';

  @override
  String get textAccountConnectionProviderNotFound =>
      'The account is not connected to your Scoop account. Please try again.';

  @override
  String get titleExportWalletNotice => 'Notice';

  @override
  String get textExportWalletNotice1 =>
      'The mnemonics provides full access to my wallet and funds.';

  @override
  String get textExportWalletNotice2 =>
      'Turnkey Wallet is a **non-custodial wallet**. That means you\'re the sole owner of your wallet';

  @override
  String get textExportWalletNotice3 =>
      '**Make sure no one is looking at your screen.** No one should request this.';

  @override
  String get titleYourWallet => 'Your Wallet';

  @override
  String get textSaveMnemonics =>
      'DO NOT forget to save this mnemonics. Save a backup on a storage medium and write it down.';

  @override
  String get textClickToReveal => 'Click to reveal';

  @override
  String get textPoweredByTurnkey => 'Powered by Turnkey';

  @override
  String get textLiveConnectionStateConnecting => 'Connecting…';

  @override
  String get textLiveConnectionStateReconnecting => 'Reconnecting…';

  @override
  String get textLiveConnectionStateFailed =>
      'Connection error. Trying to reconnect…';

  @override
  String get textLiveConnectionStateDisconnected =>
      'You seems disconnected. Trying to reconnect…';

  @override
  String get labelMicRequesting => 'Requesting';

  @override
  String get labelMicAskToSpeak => 'ask to speak';

  @override
  String get labelMicOff => 'mic off';

  @override
  String get labelMicOn => 'mic on';

  @override
  String get labelAudioRouteNoAvailable => 'No available audio routes';

  @override
  String get labelAudioRouteBluetooth => 'Bluetooth';

  @override
  String get labelAudioRouteSpeaker => 'Speaker';

  @override
  String get labelAudioRouteEarpiece => 'Earpiece';

  @override
  String get titleLiveMicUpdated => 'Mic status updated!';

  @override
  String get toastLiveMicWaiting =>
      'Mic is waiting to be approved by the host.';

  @override
  String get toastLiveNowSpeaker => 'You are now a speaker!';

  @override
  String get toastLiveNoLongerSpeaker =>
      'The host has removed your speaker role.';

  @override
  String get textTokens => 'Tokens';

  @override
  String get textTraders => 'Traders';

  @override
  String get textActivity => 'Activity';

  @override
  String get textChooseTimeFrame => 'Choose the time frame';

  @override
  String get textDiscoveryEmpty => 'Have some rest...';

  @override
  String get textDiscoveryError => 'Oops, you are overheated...';

  @override
  String titleYourChainAddress(Object chain) {
    return 'Your $chain wallet address';
  }

  @override
  String get textWalletNotAvailableForTheChain =>
      'The wallet is not available for the chain.';

  @override
  String get textWalletCreateTip =>
      'Create a wallet to receive airdrops and manage assets.';

  @override
  String get toastGroupFailedToPickImage => 'Failed to pick image.';

  @override
  String toastGroupFailedToParsePickedFile(Object filename) {
    return 'Unable to parse $filename';
  }

  @override
  String hintGroupImageSize(Object current, Object max) {
    return '(Max Size: $max, Current Size: $current)';
  }

  @override
  String get hintGroupName => 'Group name';

  @override
  String get hintGroupInfo => 'Group info';

  @override
  String get textJoinByRequest => 'Join By Request';

  @override
  String get labelRequestToJoin => 'Request to join';

  @override
  String get titleGroupAccessRequired => 'Group Access Require';

  @override
  String get textGroupAccessRequired =>
      'This live is for group members with an approval only.';

  @override
  String get textGroupWaitingForApproval => 'Waiting for approval...';

  @override
  String get titleGroupJoinToAccessLive => 'Join Group to Access Live';

  @override
  String get textGroupJoinToAccessLive =>
      'Join the group instantly to access this live room.';

  @override
  String get labelCreate => 'Create';

  @override
  String get textPosition => 'Position';

  @override
  String get textPnL => 'P&L';

  @override
  String get textWalletListEmpty => 'Human... try to earn more....';

  @override
  String get labelGroups => 'Groups';

  @override
  String get labelLive => 'Live';

  @override
  String get textLiveListEmpty => 'Waiting for lives...';

  @override
  String get textLiveListError => 'Oops, somebody has to fix the mic...';

  @override
  String get titlePrivateLive => 'Private Live';

  @override
  String get titleGroupLive => 'Group Live';

  @override
  String get titlePublicLive => 'Public Live';

  @override
  String get titlePublic => 'Public';

  @override
  String get titlePrivate => 'Private';

  @override
  String get labelGroupsOwned => 'Owned';

  @override
  String get labelGroupsJoined => 'Joined';

  @override
  String get labelGroupsExplore => 'Explore';

  @override
  String get textCreateFirstGroup => 'Your turn to create the first group!';

  @override
  String get titleGroupActions => 'Group Actions';

  @override
  String get labelEditGroup => 'Edit group info';

  @override
  String get labelLeaveGroup => 'Leave the group';

  @override
  String get labelDismissGroup => 'Dismiss the group';

  @override
  String get titleConfirmation => 'Confirmation';

  @override
  String get textConfirmLeaveGroup =>
      'Are you sure you want to leave this group?';

  @override
  String get textConfirmDismissGroup =>
      'Are you sure you want to dismiss this group? **After your action, everyone will be gone from the group.**';

  @override
  String get labelCancel => 'Cancel';

  @override
  String get labelConfirm => 'Confirm';

  @override
  String get textGroupLeft => 'You have left the group';

  @override
  String get textFollowings => 'Followings';

  @override
  String get textFollowers => 'Followers';

  @override
  String get textVolume => 'Volume';

  @override
  String get labelShareVia => 'Share via...';

  @override
  String get textShareVia => 'X, Telegram, Warpcast, ...';

  @override
  String get labelDetails => 'Details';

  @override
  String get labelMyOrders => 'My Orders';

  @override
  String get textNothingFound => 'We\'ve tried, but nothing found.';

  @override
  String get titleStats => 'Stats';

  @override
  String get labelMarketCap => 'Market Cap';

  @override
  String get labelSupply => 'Supply';

  @override
  String get labelLiquidity => 'Liquidity';

  @override
  String get labelHolders => 'Holders';

  @override
  String get labelBuys => 'Buys';

  @override
  String get labelSells => 'Sells';

  @override
  String get labelAbout => 'About';

  @override
  String get labelCreatedAt => 'Created At';

  @override
  String get labelAudit => 'Audit';

  @override
  String get labelTopHolders => 'Top 10 holders';

  @override
  String get labelMintable => 'Mintable';

  @override
  String get labelFreezable => 'Freezable';

  @override
  String get labelBalanceMutable => 'Balance Mutable';

  @override
  String get labelMetadataMutable => 'Metadata Mutable';

  @override
  String get textPassed => 'Passed';

  @override
  String get textFailed => 'Failed';

  @override
  String get textBuyingInProgress => 'Buying in progress...';

  @override
  String get textSellingInProgress => 'Selling in progress...';

  @override
  String get textSlippage => 'Slippage';

  @override
  String get textSlippageDescription =>
      'Slippage tolerance allows price deviation, with higher slippage increasing success in volatile markets.';

  @override
  String get textAuto => 'Auto';

  @override
  String get textAvailable => 'Available';

  @override
  String textTokenBalanceAvailable(Object balance, Object symbol) {
    return '**$balance $symbol** Available';
  }

  @override
  String get textCustom => 'Custom';

  @override
  String get textMaxSlippage => 'Max slippage';

  @override
  String get textMaxSlippageDescription =>
      'Slippage will be intelligently chosen based on the token and network conditions. Set the maximum slippage you are willing to accept.';

  @override
  String get textNetworkFees => 'Network Fees';

  @override
  String get textCustomSpeedFee => 'Custom Speed Fee';

  @override
  String get textCustomSpeedFeeDescription =>
      'Choose how much to tip validators to process your trades faster.';

  @override
  String get textSol => 'SOL';

  @override
  String get textTapBackToLive => 'Tap to get back to the live.';

  @override
  String get textRequestingToSpeak => 'Requesting to speak in the live...';

  @override
  String get textLiveWaitingForApproval => 'Waiting for approval...';

  @override
  String get labelLivePanelAllowChat => 'Allow chat';

  @override
  String get labelLivePanelMuteAllSpeakers => 'Mute all speakers';

  @override
  String get labelLivePanelBackgroundAudio => 'Background Audio';

  @override
  String get labelReport => 'Report';

  @override
  String get titleLivePanelReportLive => 'Report the Live';

  @override
  String get textLivePanelReportLiveDescription =>
      'You are about to report the Live for inappropriate content. Are you sure?';

  @override
  String get textThanksReport => 'Thanks for your report!';

  @override
  String get labelCloseLive => 'Close the Live';

  @override
  String get labelLeaveLive => 'Leave the Live';

  @override
  String get labelSpeakers => 'Speakers';

  @override
  String get labelLiveUserInviteToSpeak => 'Invite to speak';

  @override
  String get labelLiveUserRemoveFromSpeaker => 'Remove from Speaker';

  @override
  String get labelViewUserProfile => 'View Profile';

  @override
  String get labelLiveUserMute => 'Mute';

  @override
  String get labelLiveUserUnmute => 'Unmute';

  @override
  String get labelLiveUserKick => 'Kick';

  @override
  String get labelLiveUserBlock => 'Block';

  @override
  String get titleLiveNotificationCAPinned => 'A new CA was pinned!';

  @override
  String textLiveNotificationCAPinned(Object symbol, Object username) {
    return '$symbol was just pinned by $username in the live room.';
  }

  @override
  String get titleLiveNotificationLinkPinned => 'A new link was pinned!';

  @override
  String textLiveNotificationLinkPinned(Object title, Object username) {
    return '$title was just pinned by $username in the live room.';
  }

  @override
  String get titleLiveNotificationKicked =>
      'Your device is kicked from the live room.';

  @override
  String get textLiveNotificationKicked =>
      'Another device has joined the live room, or you have been kicked by the room host.';

  @override
  String get labelUserFollow => 'Follow';

  @override
  String get labelUserUnfollow => 'Unfollow';

  @override
  String get labelUserFollowingCount => 'Following';

  @override
  String get labelUserFollowersCount => 'Followers';

  @override
  String get textUserNoFollowingYet => 'No following yet.';

  @override
  String get textUserNoFollowersYet => 'No followers yet.';

  @override
  String get labelUserInvite => 'Invite';

  @override
  String get titleUserInvite => 'Invite Friends';

  @override
  String get textUserInvite =>
      'Share your Scoop invite code—invite friends and get rewarded together!';

  @override
  String get labelTotalReferrals => 'Total Referrals';

  @override
  String get labelRefCode => 'Referral Code';

  @override
  String get labelInviteAFriend => 'Invite a friend';

  @override
  String get titleLiveEnded => 'Live Ended';

  @override
  String get titleLiveLeaved => 'Left Live Room';

  @override
  String get titleLiveEndedListeners => 'Listeners';

  @override
  String get titleLiveEndedMentionedCAs => 'Mentioned CAs';

  @override
  String get titleLiveEndedDuration => 'Duration';
}
